<template>
  <div style="display: inline-block;">
      <el-dropdown @command="commandHandle"  >
        <el-button :type="buttonType" size="mini">{{buttonName}}<i class="el-icon-arrow-down el-icon--right"></i></el-button>
        <el-dropdown-menu slot="dropdown" style="width: 100px">
          <el-dropdown-item command="importXlsx">
            <FileUpload btnType="span" :isShowTip="false" @input="batchUpdateRes" triggerButtonTitle="导入xlsx" uploadIcon="el-icon-s-fold" :uploadUrl="uploadUrl" :extendParam="{ code: importCode}" />
          </el-dropdown-item>
          <el-dropdown-item command="record">导入记录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    <common-import-recode ref="recodeRef" :openView="openRecord" @close="openRecord = false" :showCurImportRecord="true" :importCode="importCode"/>
  </div>
</template>

<script>

export default {
  name: 'ImportDataDropdown',
  props:{
    importCode: { type: String, default: '' },//导入码
    uploadUrl: { type: String, default: '' },//导入文件时的接口地址
    buttonName: {type:String,default:''},//显示按钮
    buttonType: {type: String,default:'primary'},//按钮颜色
  },
  data() {
    return {
      openRecord: false
    }
  },
  created() {},
  methods: {
    async commandHandle(value){
      console.log(value);
      if(value==='record'){
        this.openRecord = true;
        await this.$nextTick()
        await this.$refs.recodeRef?.init()
      }
    },
    batchUpdateRes(data) {
      if (data && data.errorFilePath) {
        data.desc = data.desc + "<br><br><a onclick=\"" +
          "var path = '" + data.errorFilePath + "'\n" +
          "fetch('" + process.env.VUE_APP_BASE_API + "' + path).then((res) => {\n" +
          "  res.blob().then((blob) => {\n" +
          "    const blobUrl = window.URL.createObjectURL(blob)\n" +
          "    const a = document.createElement('a')\n" +
          "    a.href = blobUrl\n" +
          "    a.download = path.substring(path.lastIndexOf('error_'), path.length)\n" +
          "    a.click()\n" +
          "    a.remove()\n" +
          "    window.URL.revokeObjectURL(blobUrl)\n" +
          "  })\n" +
          "})\n" +
          "\" style='color: blue;'>错误文件</a>"
      }
      if (data && data.desc) {
        this.$message({
          message: data.desc,
          dangerouslyUseHTMLString: true,
          center: true,
          duration: 0,
          offset: 50,
          showClose: true,
        })
      }
    },
  },
  components:{
    commonImportRecode:()=>import('@/components/CommonImport/record.vue'),
    FileUpload: () => import('@/components/FileUpload'),
  }
}
</script>
<style lang="scss" scoped>
</style>
