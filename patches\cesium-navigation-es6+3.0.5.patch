diff --git a/node_modules/cesium-navigation-es6/dist/CesiumNavigation.js b/node_modules/cesium-navigation-es6/dist/CesiumNavigation.js
index 31abe9c..345aee9 100644
--- a/node_modules/cesium-navigation-es6/dist/CesiumNavigation.js
+++ b/node_modules/cesium-navigation-es6/dist/CesiumNavigation.js
@@ -79,11 +79,13 @@ function initialize(viewerCesiumWidget, options) {
     throw new DeveloperError('CesiumWidget or Viewer is required.')
   }
 
-  var cesiumWidget = defined(viewerCesiumWidget.cesiumWidget) ? viewerCesiumWidget.cesiumWidget : viewerCesiumWidget
+  var containerWidget = document.getElementById(options.containerId)
+  // var cesiumWidget = defined(viewerCesiumWidget.cesiumWidget) ? viewerCesiumWidget.cesiumWidget : viewerCesiumWidget
 
   var container = document.createElement('div')
   container.className = 'cesium-widget-cesiumNavigationContainer'
-  cesiumWidget.container.appendChild(container)
+  // cesiumWidget.container.appendChild(container)
+  containerWidget.appendChild(container)
   this.terria = viewerCesiumWidget
   this.terria.options = (defined(options)) ? options : {}
   this.terria.afterWidgetChanged = new CesiumEvent()
diff --git a/node_modules/cesium-navigation-es6/dist/styles/cesium-navigation.css b/node_modules/cesium-navigation-es6/dist/styles/cesium-navigation.css
index f00dbe9..3892660 100644
--- a/node_modules/cesium-navigation-es6/dist/styles/cesium-navigation.css
+++ b/node_modules/cesium-navigation-es6/dist/styles/cesium-navigation.css
@@ -4,7 +4,8 @@
   border-radius: 15px;
   padding-left: 5px;
   padding-right: 5px;
-  bottom: 30px;
+  bottom: -40px;
+  right: -20px;
   height: 30px;
   width: 125px;
   box-sizing: content-box;
@@ -14,7 +15,7 @@
   display: inline-block;
   font-family: 'Roboto', sans-serif;
   font-size: 14px;
-  font-weight: lighter;
+  font-weight: bold;
   line-height: 30px;
   color: #FFFFFF;
   width: 125px;
@@ -103,7 +104,7 @@ screen and (max-height: 420px) {
   pointer-events: auto;
   position: absolute;
   right: 0px;
-  top: 100px;
+  top: 20px;
   width: 95px;
   height: 95px;
   overflow: hidden;
@@ -114,7 +115,7 @@ screen and (max-height: 420px) {
   top: 0;
   width: 95px;
   height: 95px;
-  fill: rgba(255, 255, 255, 0.5);
+  fill: rgba(255, 255, 255, 0.8);
 }
 
 .compass-outer-ring-background {
diff --git a/node_modules/cesium-navigation-es6/dist/viewModels/NavigationViewModel.js b/node_modules/cesium-navigation-es6/dist/viewModels/NavigationViewModel.js
index 5c26ed9..e4aec16 100644
--- a/node_modules/cesium-navigation-es6/dist/viewModels/NavigationViewModel.js
+++ b/node_modules/cesium-navigation-es6/dist/viewModels/NavigationViewModel.js
@@ -110,7 +110,7 @@ NavigationViewModel.prototype.show = function (container) {
   const compassOuterRing = (this.terria.options.compassOuterRingSvg ? compassOuterRingSelf : compassOuterRingDefaultSvg) + closeStr
 
 
-  const compassGyroBackground = ' <div class="compass-gyro-background"></div>'
+  const compassGyroBackground = ' <div id="compass-gyro-background" class="compass-gyro-background"></div>'
   const compassGyroPre = ' <div class="compass-gyro" data-bind="css: { \'compass-gyro-active\': isOrbiting }'
   const compassGyroDefaultSvg = compassGyroPre + ',cesiumSvgPath: { path: svgCompassGyro, width: 145, height: 145 } "' + divCloseStr
   const compassGyroSelf = compassGyroPre + '"' + divCloseStr + this.terria.options.compassGyroSvg
