import request from '@/utils/request'

// 查询cosmic项目列表
export function listTCosmicProject(query) {
  return request({
    url: '/cosmic/project/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic项目详细
export function getTCosmicProject(id) {
  return request({
    url: '/cosmic/project/' + id,
    method: 'get'
  })
}

//初始化任务
export function initTCosmicProject(id) {
  return request({
    url: '/cosmic/project/init/' + id,
    method: 'get'
  })
}


// 新增cosmic项目
export function addTCosmicProject(data) {
  return request({
    url: '/cosmic/project',
    method: 'post',
    data: data
  })
}

// 修改cosmic项目
export function updateTCosmicProject(data) {
  return request({
    url: '/cosmic/project',
    method: 'put',
    data: data
  })
}

// 删除cosmic项目
export function delTCosmicProject(id) {
  return request({
    url: '/cosmic/project/' + id,
    method: 'delete'
  })
}

//分析交互消息
export function analysisCosmicMessage(data,historyNum) {
  return request({
    url: '/cosmic/project/analysisCosmicMessage/'+historyNum,
    method: 'post',
    data: data
  })
}
//分析交互消息
export function analysisCosmicMessageBatch(data,historyNum) {
  return request({
    url: '/cosmic/project/analysisCosmicMessage/'+historyNum+'/batch',
    method: 'post',
    data: data
  })
}


//删除cosmic对象
export function deleteTCosmicObject(code,id,deleteSelf) {
  return request({
    url: '/cosmic/project/deleteTCosmicObject/'+code+'/' + id+'/' + deleteSelf,
    method: 'get'
  })
}
//按内容合并excel
export function excekRange(data) {
  return request({
    url: '/cosmic/project/excekRange',
    method: 'post',
    data: data
  })
}

//按内容合并excel
export function exportWord(id,modelId) {
  return request({
    url: '/cosmic/project/exportWord/'+id+'/'+modelId,
    method: 'get',
  })
}

//按内容合并excel
export function generateDoc(url) {
  return request({
    url: '/cosmic/generateDoc?url='+url,
    method: 'get',
  })
}

//获取请求三方的报文
export function getAnalysisContent(data) {
  return request({
    url: '/cosmic/project/getAnalysisContent',
    method: 'post',
    data: data
  })
}


//三方请求报文入库
export function analysisCosmicMessageToDB(data) {
  return request({
    url: '/cosmic/project/analysisCosmicMessageToDB',
    method: 'post',
    data: data
  })
}

