import request from '@/utils/request'

export const legend = (style) => {
  return request({
    url: '/geoserver/wms?REQUEST=GetLegendGraphic&VERSION=1.0.0&FORMAT=image/png&WIDTH=20&HEIGHT=20&STRICT=false&style=' +style,
    method: 'get'
  })
}
export const line = (data) => {
  return request({
    url: '/cov/menu/line?id=' +data.id + '&type=' + data.type + '&nettype=' + data.nettype,
    method: 'get'
  })
}
