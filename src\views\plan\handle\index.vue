<template>
  <div :class='{ lock: loading }' class='handle'>
    <div class='page-loading loading-ball' v-show='loading' />

    <CollapseTab
      :tabs='tabStages'
      @handleClick='handleClick'
      class='top-tabs'
      title='阶段展示'
      v-if='siteCmccId && tabStages.length'
    />

    <!-- tab 附件 -->
    <BasicTable
      :insertIndex='[0]'
      :tableData='allFilesTableData'
      :tableTitle='allFilesTableTitle.slice(0, -1)'
      border
      v-if='switchTab === "file"'
    >
      <template slot-scope='row'>
        <el-link
          @click='downloadFields(row)'
          type='primary'
          v-if='row.column == 0'
        >{{ row.item.name }}
        </el-link>
      </template>
    </BasicTable>

    <!-- tab 数据接口 -->
    <Preview
      :siteIdCmcc='siteCmccId'
      code='qryRequestCall'
      hiddenHeader
      type='common'
      v-else-if='switchTab === "interface"'
    />

    <!-- tab 处理情况 -->
    <BasicTable
      :insertIndex='[5]'
      :tableData='historyTableData'
      :tableTitle='historyTableTitle'
      border
      v-else-if='switchTab === "handle"'
    >
      <template slot-scope='row'>
        <span v-if='row.column == 5' v-html="row.item.comment"></span>
      </template>
    </BasicTable>

    <!-- tab 拓扑图 -->
    <CanvasGplot
      :data='siteGraphModel'
      class='gplot'
      v-else-if='switchTab === "gplot"'
    />

    <!-- tab 流程图 -->
    <template v-else-if='switchTab === "flowChart"'>
      <keep-alive>
        <History :nodeId='nodeId' :orderId='formInsId' />
      </keep-alive>
    </template>

    <!-- tab stageChangeRecord -->
    <stageChangeRecord :order-no="siteCmccId" v-else-if="switchTab==='stageChangeRecord'"></stageChangeRecord>


    <template v-else>
      <DemandTab
        :fields='getCurrentStageFields'
        :inResourcePool='inResourcePool'
        :planSite='getCurrentStagePlanSite'
        :type='type'
        @setPlanSite='setPlanSite'
        @submit='submit'
        ref='demand'
      />

      <el-collapse v-model='collapses'>
        <!--逻辑基站信息，只有需求库才显示-->
        <el-collapse-item name='logic' title='逻辑基站信息' v-if='inResourcePool || currentStage === "XQJD"'>
          <LogicBaseStation :fields='getCurrentStageFields' :inResourcePool='inResourcePool' :planSite='getCurrentStagePlanSite' :schemeIndex='0' ref='logicStation'></LogicBaseStation>
        </el-collapse-item>

        <el-collapse-item name='scheme' title='方案' v-if='!notShowSchemeStageCodeList.includes(currentChildStage)'>
          <Scheme :fields='getCurrentStageFields' :planSite='getCurrentStagePlanSite' :clickItem="clickItem" ref='scheme' />
        </el-collapse-item>

        <!-- 特殊阶段显示的collapse -->
        <el-collapse-item name='schemeSelect' title='方案选择' v-if='currentChildStage === "LXYJ"'>
          <BasicTable :tableData='schemeData' :tableTitle='schemeHeader' @handleSelectionChange='($event) => nodeExtendData = $event.length ? $event[0] : null' checkbox radio ref='schemeTable'>
          </BasicTable>
        </el-collapse-item>

        <el-collapse-item name='approval' title='立项批复' v-if='currentChildStage === "LXPF"'>
          <LogicBaseStation :fields='getCurrentStageFields' group-mark="enodeb_approval" :planSite='getCurrentStagePlanSite' :schemeIndex='0' @setPeriodStage='($event) => nodeExtendData = $event' period ref='constructionPeroid'>
          </LogicBaseStation>
        </el-collapse-item>

        <el-collapse-item name='appointment' title='施工预约' v-if='currentChildStage === "SGYY"'>
          <BasicForm
            :disabled='!ableToHandleTask'
            :fields='useFields("construction_appointment")'
            :formValue='
              getCurrentStagePlanSite && getCurrentStagePlanSite.siteList
                ? getCurrentStagePlanSite.siteList[0]
                : constructionAppointment'
            :groupColumn='3'
            @returnFormValue='setConstructionAppointment'
            size='small'
          />
        </el-collapse-item>

        <el-collapse-item
          name='antCompare'
          title='天线数据对比'
          v-if='isShowAntCompareStages.includes(currentChildStage)'
        >
          <Antenna ref='antCompare' />
        </el-collapse-item>

        <el-collapse-item
          name='transmitting'
          title='传输调度'
          v-if='currentChildStage === "CSDD"'
          v-show="isCurrentProvince('HN')"
        >
          <BasicTable
            :tableData='transDispatchData'
            :tableTitle='transDispatchHeader'
          />
        </el-collapse-item>

        <el-collapse-item
          name='autoOpenSiteResult'
          title='自动开站结果'
          v-if='currentChildStage === "SJJZ"'
        >
          <BasicTable
            :tableData='autoOpenSiteResultData'
            :tableTitle='autoOpenSiteResultHeader'
          />
        </el-collapse-item>

        <el-collapse-item
          name='alarm'
          title='基站告警'
          v-if='currentChildStage === "GJHC"'
        >
          <EnodebAlarm :enodebList='getEnodebList' ref='enodebAlarm' />
        </el-collapse-item>

        <el-collapse-item
          name='kpi'
          title='性能指标'
          v-if='currentChildStage === "RWPG"'
        >
          <EnodebPerformance
            :enodebList='getEnodebList'
            :siteIdCmcc='siteCmccId'
            ref='enodebPerformance'
            v-if="!isCurrentProvince('BJ') "
          />
          <PMMrDay :siteIdCmcc='siteCmccId' v-if="isCurrentProvince('BJ') " />
        </el-collapse-item>

        <el-collapse-item
          name='singleCheck'
          title='单验测试结果'
          v-if='isShowSingleCheckStages.includes(currentChildStage)'
        >
          <SingeTestResult
            :enodebList='getEnodebList'
            :siteCmccId='siteCmccId'
            @hasSingleTestRes='canSubmitNext = true'
          />
        </el-collapse-item>

        <!-- 宏站审核 -->
        <el-collapse-item name='macrosite' title='宏站审核' v-if='isShowMacroSiteCheck'>
          <macrositeCheck :admin='true' :siteCmccId='siteCmccId' />

          <OutdoorGis
            :suffix='renderData.renderType'
            :taskId='renderData.renderId'
            v-if='renderData.renderId'
          />
        </el-collapse-item>

        <!-- 室分自动审核 -->
        <el-collapse-item
          name='autoCheck'
          title='室分自动审核'
          v-if='isShowRoomSiteCheck'
        >
          <IndoorAutoCheck
            :admin='false'
            :planSite='getCurrentStagePlanSite'
            :siteCmccId='siteCmccId'
          />
        </el-collapse-item>

        <el-collapse-item
          name='scAutoCheck'
          title='AIDP室分审核'
          v-if='sysconfig.SC_AIDP_AUDIT && currentChildStage === "SJSH" && isShowAidp'
        >
          <iframe
            :src='sysconfig.SC_AIDP_AUDIT + "?siteId=" + siteCmccId'
            frameborder='0'
            height='400px'
            width='100%'
          ></iframe>
        </el-collapse-item>

        <el-collapse-item
          name='pmAnalysis'
          title='入网后评估'
          v-if='currentChildStage === "RWPG" && sysconfig.pmAnalysis == "ENABLE"'
        >
          <PMAnalysis
            :admin='false'
            :planSite='getCurrentStagePlanSite'
            :siteCmccId='siteCmccId'
            v-if='getCurrentStagePlanSite.siteList'
          />
        </el-collapse-item>

        <!-- 参考信息 -->
        <el-collapse-item name='refer' v-if='currentChildStage === "XQK"'>
          <template slot='title'>
            参考信息
            <el-tooltip effect='light' placement='right'>
              <div slot='content'>
                示例：
                <br />V2*#*#小区数:3*#*#小区1*#*#配置功率（dBm）:46.3*#*#数字倾角:
                <br />1*#*#预置倾角:2*#*#状态:XXX*#*#是否为新址:是*#*#仿真所属区域:
                <br />XXXX*#*#小区2*#*#配置功率（dBm）:46.3*#*#数字倾角:1*#*#预置
                <br />倾角:2*#*#状态: XXX *#*#是否为新址:是*#*#仿真所属区域:XXXX
                <br />*#*#小区3*#*#配置功率（dBm）:46.3*#*#数字倾角1:*#*#预置倾角:
                <br />2*#*#状态:XXX*#*#是否为新址:是*#*#仿真所属区域:XXXX*#*#
              </div>
              <i class='el-icon-question'></i>
            </el-tooltip>
          </template>
          <el-input
            :autosize='{ minRows: 14 }'
            :disabled='!inResourcePool'
            placeholder='请输入内容'
            type='textarea'
            v-model='reference'
          ></el-input>
        </el-collapse-item>

        <el-collapse-item
          name='system'
          title='光调'
          v-if='isCurrentProvince("HN") && lightShow.includes(currentChildStage)'
        >
          <el-button
            :loading='lightLoading'
            @click='requestTriggerLight'
            class='light'
            type='primary'
          >获取光调
          </el-button>
          <BasicTable
            :tableData='lightTableData'
            :tableTitle='lightTableTitle'
            border
          />
        </el-collapse-item>

        <el-collapse-item
          name='crsResult'
          title='综资结果'
          v-if='isCurrentProvince("HN") && crsNodes.includes(currentChildStage)'
        >
          <el-input
            :autosize='{ minRows: 5, maxRows: 10 }'
            :disabled='true'
            type='textarea'
            v-model='crsResultText'
          ></el-input>
        </el-collapse-item>

        <el-collapse-item
          name='crsCheckResult'
          title='资管校验异常信息'
          v-if='crsResultNodes.includes(nodeId)'
        >
          <BasicTable
            :tableData='crsTableData'
            :tableTitle='crsTableTitle'
            border
          />
        </el-collapse-item>

        <el-collapse-item
          name='crsCuUpdateResult'
          title='采集对比更新信息'
          v-if='crsCuResultNodes.includes(currentChildStage)'
        >
          <BasicTable
            :tableData='crsCuTableData'
            :tableTitle='crsCuTableTitle'
            border
          />
        </el-collapse-item>

        <!-- audit审核 -->
        <el-collapse-item
          :title='auditCheck[currentChildStage].title'
          name='audit'
          v-if='auditCheck[currentChildStage]'
        >
          <el-tabs class='audit-tabs' type='card' v-model='auditActiveName'>
            <el-tab-pane label='审核结果' name='result'>
              <BasicTable
                :tableData='auditCheck[currentChildStage].tableData'
                :tableTitle='auditCheck[currentChildStage].tableTitle'
                border
              />
            </el-tab-pane>
            <el-tab-pane label='审核规则' name='audit'>
              <AuditRule :nodeId='auditCheck[currentChildStage].nodeId' :autoScroll="false"/>
            </el-tab-pane>
          </el-tabs>
        </el-collapse-item>

        <el-collapse-item v-if="!('hide'===(sysconfig.ShowAttachment && JSON.parse(sysconfig.ShowAttachment)[nodeId]))"
                          name='files' title='附件'>
          <template v-if='inResourcePool || (type !== "view" && ableToHandleTask)'>
            <template v-if='filesUploadClassify.length'>
              <FileUpload
                :extendParam='{
                  path: siteCmccId + "/enclosure",
                  siteIdCmcc: siteCmccId,
                  stageCode: currentStage,
                  classify: file.value,
                  nodeId: nodeId,
                  module: "PLAN",
                  checkRepeat: true
                }'
                :isShowTip='false'
                :key='file.value'
                multiple
                :triggerButtonTitle='file.name'
                @input='uploadRes'
                v-for='file in filesUploadClassify'
              />
            </template>
            <FileUpload
              :extendParam='{
                path: siteCmccId + "/enclosure",
                siteIdCmcc: siteCmccId,
                stageCode: currentStage,
                nodeId: nodeId,
                module: "PLAN",
                checkRepeat: true
              }'
              multiple
              :isShowTip='false'
              @input='uploadRes'
              v-else
            />
          </template>

          <BasicTable
            :insertIndex='[0,1]'
            :insertName='["opert","fileType"]'
            :rowIndex='true'
            :tableData='filesTableData'
            :tableTitle='filesTableTitle'
            border
          >
            <template slot-scope='row'>
              <el-button
                @click='delAttachment(row)'
                type='primary'
                v-if='row.columnName == "opert" && (inResourcePool || ableToHandleTask)'
              >删除
              </el-button>
              <el-select v-if='row.columnName === "fileType" && checkRole(["FAKE_FUNCTION"])' v-model="row.item.fileType">
                <el-option label="30度环拍照片" value="a" key="a"/>
                <el-option label="45度环拍照片" value="b" key="b"/>
                <el-option label="杆位图片" value="c" key="c"/>
                <el-option label="测试图片" value="d" key="d"/>
                <el-option label="主覆盖图片" value="e" key="e"/>
              </el-select>
              <el-link
                @click='downloadFields(row)'
                type='primary'
                v-if='row.column == 0'
              >{{ row.item.name }}
              </el-link>&nbsp;
            </template>
          </BasicTable>

          <template
            v-if='sysconfig.PLAN_PREV_FILES === "ENABLE" && prevNodeFiles && prevNodeFiles.length'
          >
            <el-divider content-position='left'>上一环节附件</el-divider>
            <BasicTable
              :insertIndex='[0, 3]'
              :rowIndex='true'
              :tableData='prevNodeFiles'
              :tableTitle='filesTableTitle'
              border
            >
              <template slot-scope='row'>
                <el-link
                  @click='downloadFields(row)'
                  type='primary'
                  v-if='row.column == 0'
                >{{ row.item.name }}
                </el-link>&nbsp;
              </template>
            </BasicTable>
          </template>
        </el-collapse-item>

        <template>
          <el-collapse-item
            v-if='(showSystemStages.includes(currentStage) && type !== "new")'
            name='system'
            title='系统判断结果'
          >
            <BasicTable
              :set-row-style='setRowStyle'
              :tableData='systemJudgeTableData'
              :tableTitle='systemJudgeTableTitle'
              border
              class='auditTable'
            >
            </BasicTable>
          </el-collapse-item>
        </template>

        <el-collapse-item name='aiCheck' title='AI验收' v-if="currentStage === 'SGJD'&&!isBanqianSite">
          <TabAICheck :orderNo="siteCmccId" :stage="currentStage"></TabAICheck>
        </el-collapse-item>
        <!--规划新建站优先级多维评分-->
        <el-collapse-item  name='priorityRate' title='规划新建站优先级多维评分' v-if='(type !== "new" && sysconfig.priorityRate==="true")' class='auditTable'>
            <PriorityRate :siteIdCmcc="siteCmccId"></PriorityRate>
        </el-collapse-item>
        <el-collapse-item  name='DTNSiteRationalityEvaluation' title='规划站点合理性评估' v-if='(hasDtnShow())' class='auditTable'>
          <dtn-site-evalution :site-id-cmcc="siteCmccId"></dtn-site-evalution>
        </el-collapse-item>

      </el-collapse>
    </template>

    <section
      class='task-handle'
      v-hasPermi='["plan:demand:deal"]'
      v-if='ableToHandleTask'
    >
      <el-button
        :loading='temporarySaveLoading'
        @click='temporarySave()'
        size='large'
        type='danger'
        v-if='isShowSaveChildStage'
      >暂存
      </el-button>
      <el-button @click='openModal("deal")' size='large' v-if="!notShowDealButStageCodeList.includes(currentChildStage)" type='primary'>处理
      </el-button>
      <el-button @click='openModal("transfer")' size='large' v-if="!notShowTransferButStageCodeList.includes(currentChildStage)" type='primary'>转派
      </el-button>
    </section>

    <el-dialog
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      :show-close='false'
      :visible.sync='jLAuditResult'
      v-if="jLAuditResult  && isCurrentProvince('JL') "
      title='审核结果'
      width='70%'
    >
      <BasicTable
        :tableData='auditCheck[currentChildStage].tableData'
        :tableTitle='auditCheck[currentChildStage].tableTitle'
        border
      />
      <span slot="footer" class="dialog-footer">
        <el-button type='primary' @click="colseJlAuditDialog()">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      :visible.sync='dealTask'
      :destroy-on-close="isCurrentProvince('BJ')"
      title='任务处理'
      width='70%'
    >
      <TaskDeal
        :buttons='buttons'
        :ck='ck'
        :currentChildStage='currentChildStage'
        :dealTask.sync='dealTask'
        :dealType='dealType'
        :dispatchAllInterfaceData='dispatchInterfaceData'
        :isNBSite='isNBSite'
        :isRejected='isRejected || lightIsPublic'
        :isSFSite='isSFSite'
        :siteIdCmcc='siteCmccId'
        :siteData="getCurrentStagePlanSite"
        @handleTask='handleTask'
        ref='taskDeal'
      />
    </el-dialog>
    <el-backtop :bottom='ableToHandleTask ? 90 : 40' />
    <ValidateNotification
      :visible.sync="showValidateNotification"
      title="资管校验异常信息"
      :items="validateItems"
      :nested-data="validateItems"
      @close="handleValidateClose"
      @item-click="handleValidateItemClick"
    />

    <!-- 添加新的对话框组件 -->
    <el-dialog
      title="流转提示"
      :visible.sync="interfaceTipsVisible"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="interface-tips">
        <div class="tips-header">
          <i class="el-icon-warning-outline"></i>
          <span>工单流转后将触发以下接口:</span>
        </div>
        <el-timeline class="tips-content">
          <el-timeline-item
            v-for="(tip, index) in interfaceTipsList"
            :key="index"
            :timestamp="`接口 ${index + 1}`"
            placement="top"
          >
            <el-card class="tip-card">
              <div class="tip-info">{{ tip.tipInfo }}</div>
              <div class="tip-url">接口地址: {{ tip.thirdUrl }}</div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmInterfaceTips">确 定</el-button>
        <el-button @click="closeInterfaceTips">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  ref,
  toRef,
  toRefs,
  reactive,
  computed,
  onMounted,
  provide,
  nextTick,
  defineComponent,
  getCurrentInstance,watch
} from '@vue/composition-api'
import {
  enodebList,
  roomList,
  roofList,
  rpList,
  getDefaultList,
} from '@/utils/plan/static'
import * as service from '@/api/site/plan/handle'
import {deepClone, strCutNumber, strSum, isJsonResolve} from '@/utils'
import { useGetters } from '@/utils/useMappers'
import {useOrderDictionary, useFieldsChildren, useCommonQueryData} from '@/utils/useDictionary'
import {
  useJudgeRejected,
  usePlanSiteCompletion,
  useBackForwardRoute,
  useCanvasSiteGraphModel, usePlanSiteChangeLogCompletion, enableShowChangeToolTip,
} from '@/utils/plan/useBusiness'
import { isNull, isCurrentProvince, getEnodebTypeByNetworkStandard } from '@/utils/common'
import validNotify from '@/utils/plan/validNotify'
import attachmentApi from '@/api/system/attachmentApi'
import { handleButtons, qryNodeStageList,qryNodeStageMsg } from '@/api/kernel/flow'
import { commonQuery, commonQueryFieldData, queryCrsCuUpdateData } from '@/api/kernel/query'
import {
  interfaceValid,
  fieldConfigValidate,
  fieldCustomValidate,
  fieldsRequiredValid,
} from '@/utils/plan/validate'
import * as businessFieldConfig from '@/api/system/businessFieldConfig'
import store from "@/store";
import * as stageChangeRecordApi from "@/api/site/plan/stageChangeRecord";
import { getCrsErrorTips, planBatchFlow } from '@/api/site/plan/handle'
import { checkRole } from '@/utils/permission'
import * as _ from 'lodash'

export default defineComponent({
  methods: { isCurrentProvince, checkRole },
  setup(props, {root}) {
    const {
      $route,
      $store,
      $message,
      $notify,
      $confirm,
      $set,
      $createElement,
      isCurrentProvince,
      HTTP_CODE,
      $updateWatermark
    } = root

    const {
      type,
      stage,
      taskId,
      nodeId,
      roleName,
      formInsId,
      statuscode,
      orderState,
      tagsViewTitle,
      inResourcePool,
      flowKey,
    } = $route.query
    const {userId} = $store.state.user.user
    // dom ref
    const demand = ref(null)
    const scheme = ref(null)
    const taskDeal = ref({})
    const antCompare = ref(null)
    const enodebAlarm = ref(null)
    const schemeTable = ref(null)
    const logicStation = ref(null)
    const constructionPeroid = ref(null)
    const enodebPerformance = ref(null)
    const crsCheckResult = ref(null)
    const state = reactive({
      isXjReject:false,
      isView:true,
      preventTaskHandle: false,
      dynamicMessage: '',
      showMessage: false,
      loading: true,
      tabStages: [],
      nodeStage: [],
      buttons: {},
      ck: '',
      currentAuditStage: '',
      currentAuditRole: '',
      adjAudit: 'N',
      dwgAudit: 'N',
      dyAudit: 'N',
      hepinAudit: 'N',
      type,
      taskId,
      nodeId,
      roleName,
      formInsId,
      orderState,
      siteCmccId: type === 'new' ? '' : tagsViewTitle,
      collapses: [
        'basic',
        'logic',
        'scheme',
        'baseBand',
        'site',
        'cell',
        'rru',
        'antenna',
        'schemeSelect',
        'approval',
        'appointment',
        'antCompare',
        'system',
        'singleCheck'
      ],
      dealType: '',
      reference: '',
      dealTask: false,
      isSFSite: false,
      isNBSite: false, //制式 NB
      isCovIndoorSite: undefined, //是否覆盖室内站
      isRejected: false, //当前阶段是否驳回
      lightIsPublic: false, //光调是否共用
      canSubmitNext: false, //单站验证阶段单验结果是否返回
      inResourcePool: inResourcePool === 'true',
      isBanqianSite: flowKey == 'sc_banqian_flow',
      flowKey: flowKey,
      lightLoading: false,
      isShowSaveChildStage: false,
      temporarySaveLoading: false,

      switchTab: 'scheme',
      currentStage: '', // currentStage会随着tab切换改变
      currentChildStage: '',
      currentStageFields: {}, // 当前阶段字段
      currentStagePlanSite: {}, //当前阶段站点数据
      recordOriginStageFields: {}, //备份初始节点数据
      recordOriginStagePlanSite: {},

      dispatchInterfaceData: [],
      isShowAntCompareStages: ['ZLSH'], //天线数据对比显示的阶段
      isShowSingleCheckStages: ['DYSH'], //单站验证显示的阶段
      advanceRequestHistoryStages: ['SJSH'], //需要提前加载历史流转的阶段

      // tab显示的全阶段附件
      allFilesTableTitle: [],
      allFilesTableData: [],

      // 附件
      filesTableTitle: [],
      filesTableData: [],
      fileType: null,
      prevNodeFiles: [], //上一环节附件
      filesUploadClassify: [], //附件上传分类

      // 系统判断结果
      showSystemStages: ['XQJD', 'GHJD', 'SJJD', 'SGJD'],
      systemJudgeTableTitle: [],
      systemJudgeTableData: [],
      systemJudgeHistoryTableTitle: [],

      historyTableTitle: [],
      historyTableData: [],

      // 传输调度
      transDispatchHeader: [],
      transDispatchData: [],

      //自动开站结果
      autoOpenSiteResultData: [],
      autoOpenSiteResultHeader: [],

      // 方案
      hideSchemeStages: ['XQK', 'XQSH', 'LXYJ'],
      schemeData: [],
      schemeHeader: [],

      // 施工预约
      constructionAppointment: {
        constructionStartDate: null,
        constructionEndDate: null,
        constructionSubscribeUser: null,
      },

      //特殊表,表实际不存在，
      specialTable: [
        'XQJD_site',
        'site_require_problem',
        'lxpf_contrunction',
        'construction_appointment',
        'xqk_referance',
        't_jx_electric',
      ],

      //光调
      lightShow: ['XQBH'],
      lightTableData: [],
      lightTableTitle: [],

      crsNodes: ['SJSH', 'SJGH', 'SJJZ', 'XQBH'],
      crsResultText: null,
      nodeExtendData: null, //各节点扩展数据

      crsResultNodes: [],
      crsTableData:[],
      crsTableTitle: [],

      siteGraphModel: {}, // 拓扑图数据

      auditActiveName: 'result',

      //吉林审核结果
      jLAuditResult: false,

      //采集对比更新信息
      crsCuResultNodes: [],
      crsCuTableData:[],
      crsCuTableTitle: [],

      showValidateNotification: false,
      validateItems: [],
      interfaceTipsVisible: false,
      interfaceTipsList: [],
      taskParams:null
    })

watch(
      () => state.currentStageFields,
      (value, prev) => {
        if (value && "" !== value) {
        console.log('state.currentStageFields','change')
        //const stack = new Error().stack
        // console.log('修改来源调用栈:', stack)
        }
      },
      { immediate: true,deep:true, },
    )
watch(
      { immediate: true ,deep:true,},
      () => state.recordOriginStageFields,
      (value, prev) => {
        if (value && "" !== value) {
        console.log('state.recordOriginStageFields','change')
        }
      },
    )

    // 静态数据仓库
    let orderChildStage = '' // 工单原始子阶段
    let validData = [] //校验数据
    let recordXqSiteData = {} // 需求数据备份
    const enodebTypeMap = {
      '5G': {
        enodebNamePlan: '_5G',
        networkStandard: '5G',
      },
      ANCHOR: {
        enodebNamePlan: '_MAODIAN',
        networkStandard: 'FDD-LTE',
        cellBand: '1800',
      },
      '3D MIMO': {
        enodebNamePlan: '_3DMIMO',
        networkStandard: 'TDD-LTE',
        cellBand: '2600',
      },
    }


    const sysconfig = useGetters('sysconfig')()
    const userName = useGetters('name')()
    const auditCheck = sysconfig.PLAN_AUDIT_CHECK ? JSON.parse(sysconfig.PLAN_AUDIT_CHECK) : {}

    state.crsResultNodes =  sysconfig.crsResultNodes ? sysconfig.crsResultNodes.split(",") : []

    state.crsCuResultNodes =  sysconfig.crsCuResultNodes ? sysconfig.crsCuResultNodes.split(",") : []


    const ableToHandleTask = computed(
      () =>
        !state.inResourcePool &&
        statuscode === 'WCL' &&
        state.currentChildStage === state.tabStages[0]?.childStage &&
        !state.isView,
    )
    const getEnodebList = computed(
      () =>
        getCurrentStagePlanSite.value.siteList &&
        getCurrentStagePlanSite.value.siteList[0].enodebList,
    )
    // 每个tab切换时重新请求字段和数据 初始阶段例外 需要保存用户填写状态
    // 当前阶段字段
    const getCurrentStageFields = computed(() =>
      orderChildStage === state.currentChildStage
        ? state.recordOriginStageFields
        : state.currentStageFields,
    )
    // 当前阶段数据
    const getCurrentStagePlanSite = computed(() =>
      orderChildStage === state.currentChildStage
        ? state.recordOriginStagePlanSite
        : state.currentStagePlanSite,
    )
    // 室分自动审核--图纸管理
    const isShowRoomSiteCheckDwgManager = computed(
      () =>
        sysconfig.indoorAutoCheck == 'ENABLE' &&
        getCurrentStagePlanSite.value?.xqSite?.indoorFlag === 'RoomSite' &&
        orderChildStage === 'SJSH',
    )
    // 室分自动审核
    const isShowRoomSiteCheck = computed(
      () =>
        sysconfig.indoorAutoCheck == 'ENABLE' &&
        getCurrentStagePlanSite.value?.xqSite?.indoorFlag === 'RoomSite' &&
        state.currentChildStage === 'SJSH',
    )
    // 宏站自动审核
    const isShowMacroSiteCheck = computed(
      () =>
        sysconfig.macrositeAutoCheck == 'ENABLE' &&
        getCurrentStagePlanSite.value?.xqSite?.indoorFlag !== 'RoomSite' &&
        state.currentChildStage === 'SJSH',
    )

    // AIDP DTN 两个参数控制 显示结果互斥
    const isShowAidp = computed(() => {
      return typeof state.isCovIndoorSite === 'undefined'
        ? state.isSFSite
        : state.isCovIndoorSite
    })

    // SC 开站模板功能参数
    const getOpenTemplateConfig = computed(() => {
      return isJsonResolve(sysconfig.PLAN_SC_OPEN_TEMPLATE_CONFIG) ?
        JSON.parse(sysconfig.PLAN_SC_OPEN_TEMPLATE_CONFIG) : {}
    })

    const notShowSchemeStageCodeList = computed(() => {
      let configArr = []
      try {
        configArr = sysconfig.notShowSchemeStageNodeIdList ? JSON.parse(sysconfig.notShowSchemeStageNodeIdList) : []
      } catch (e) {
        console.log('notShowSchemeStageNodeIdList格式化失败：', sysconfig.notShowSchemeStageNodeIdList)
      }
      // 不显示方案的nodeId列表 转换为childStage
      const arr = state.nodeStage?.filter(s => configArr.includes(s.nodeId)).map(s => s.childStage)
      return arr ? arr : []
    })

    const notShowTransferButStageCodeList = computed(() => {
      let configArr = []
      try {
        configArr = sysconfig.notShowTransferButNodeIdList ? JSON.parse(sysconfig.notShowTransferButNodeIdList) : []
      } catch (e) {
        console.log('notShowTransferButNodeIdList格式化失败：', sysconfig.notShowTransferButNodeIdList)
      }
      // 不显示转派按钮的nodeId列表 转换为childStage
      const arr = state.nodeStage?.filter(s => configArr.includes(s.nodeId)).map(s => s.childStage)
      return arr ? arr : []
    })

    const notShowDealButStageCodeList = computed(() => {
      let configArr = []
      try {
        configArr = sysconfig.notShowDealButNodeIdList ? JSON.parse(sysconfig.notShowDealButNodeIdList) : []
      } catch (e) {
        console.log('notShowDealButNodeIdList格式化失败：', sysconfig.notShowDealButNodeIdList)
      }
      // 不显示处理按钮的nodeId列表 转换为childStage
      const arr = state.nodeStage?.filter(s => configArr.includes(s.nodeId)).map(s => s.childStage)
      return arr ? arr : []
    })

    provide('isCurrentStage', ref(ableToHandleTask))
    provide('isSFSite', toRef(state, 'isSFSite'))
    provide('stage', toRef(state, 'currentStage'))
    provide('siteCmccId', toRef(state, 'siteCmccId'))
    provide('childStage', toRef(state, 'currentChildStage'))
    provide('reRenderPage', () => reRenderPage())
    provide(
      'xqSite',
      computed(() => {
        const xqSiteData = deepClone(getCurrentStagePlanSite.value.xqSite) || {}
        getCurrentStageFields.value?.XQJD_site.forEach((field) => {
          if (field.children) {
            const name = field.children.find(
              (item) => item.value === xqSiteData[field.name],
            )
            name && $set(xqSiteData, field.name + '_text', name.name)
          }
        })
        return xqSiteData
      }),
    )


    onMounted(async () => {
      await getValidData()
      window.addEventListener('scroll', handleScroll)

      commonQuery("getViewByNode",{nodeId:nodeId,userId:userId}).then(result =>{
        // console.log("state.isView",result?.data[0]?.isView)
        state.isView =result?.data[0]?.isView
      })
      const stages = await qryNodeStageList(state.flowKey, taskId)
      state.nodeStage = stages.data || []

      // 给未处理列表做兼容
      state.currentStage = state.nodeStage.find((i) => i.nodeId == nodeId)?.stage ?? stage
      store.state.plan.currentStage = state.flowKey === 'cmcc_5g_plan' ? state.currentStage : null;//设置规划全局状态
      nodeId === state.nodeStage[0]?.nodeId && (state.inResourcePool = (state.flowKey === 'cmcc_5g_plan'))
      store.state.plan.currentNodeId = nodeId;
      // console.log('当前阶段:',state.currentStage,',是否启用仿真：',sysconfig.NM_DTNSREvaluation_ReqConfig,',nodeId:',nodeId);
      orderChildStage = state.currentChildStage = nodeId
        ? state.nodeStage.find((i) => i.nodeId == nodeId)?.childStage
        : state.nodeStage[0]?.childStage

      // 新增工单时的判断条件 type === new && inResourcePool === true
      // 需求库 处理工单时 type === handle && inResourcePool === true
      if (type === 'new') {
        const sysDefaultValue = isJsonResolve(sysconfig.PIC_INTERVAL) ?
          JSON.parse(sysconfig.PIC_INTERVAL) : {}

        state.nodeId = state.nodeStage[0]?.nodeId
        state.recordOriginStagePlanSite = state.currentStagePlanSite = {
          xqSite: {}, // 基本信息
          siteList: [
            {
              sort: 0,
              enodebList: enodebList(false),
              roomList: roomList(),
              roofList: roofList({roofIntervalPic: sysDefaultValue.plan_roof}),
              rpList: rpList(),
            },
          ], // 方案信息
          stageCode: state.currentStage, // 当前阶段
          newDemand: true,
          attachments: [], // 附件列表
        }
      }

      await init()
    })
    const handleScroll = () => {
      clickItem.value = null
    }

    const init = async () => {
      const { nodeId, currentStage, currentChildStage, siteCmccId } = state
      siteCmccId && rewriteStages()

      // 等待全量工单字段数据字典返回 如vuex内已存在数据 立即返回
      await useOrderDictionary('GH')
      await requestFields(currentStage, nodeId, true)
      state.loading = false

      siteCmccId &&  requestPlanSite(currentStage, nodeId, true)
      ableToHandleTask.value &&  requestHandleButtons()

      // 不同省,不同阶段执行前置操作
      preHandleProvince()
      hiddenSchemeCollapse()
      taskId && (state.ck = strSum(strCutNumber(taskId)))
      state.advanceRequestHistoryStages.includes(currentChildStage) &&
       requestHistory()

      switchTabTrigger(state.currentChildStage, state.nodeId)
       requestSiteEnchosuerData(nodeId)

      isShowMacroSiteCheck.value && await canRender()


    }


    const rewriteStages = () => {
      const currentStageIndex = state.nodeStage.findIndex(
        (stage) => stage.childStage === state.currentChildStage,
      )
      state.tabStages = [...state.nodeStage]
        .slice(0, currentStageIndex + 1)
        .reverse()

      if (sysconfig.PLAN_FLOW_FIXED_TABS) {
        try {
          const fixedTabs = JSON.parse(sysconfig.PLAN_FLOW_FIXED_TABS)
          Array.isArray(fixedTabs) &&
          (state.tabStages = [
            ...state.tabStages,
            ...fixedTabs.filter((i) => i.show),
          ])
        } catch {
        }
      }

      // 为currentStage 做兼容处理
      if (!state.currentStage) {
        state.currentStage = state.tabStages[0].stage
      }
    }
    const reRenderPage = async () => {
      await requestPlanSite(state.currentStage, state.nodeId,true)
    }

    const preHandleProvince = () => {
      switch (sysconfig.CURRENT_PROVINCE_CODE) {
        case 'HN': {
          // SGGK显示天线数据对比
          // DZYZ显示单验测试结果
          state.isShowAntCompareStages.push('SGGK')
          state.isShowSingleCheckStages.push('DZYZ')
          state.advanceRequestHistoryStages = [
            ...state.advanceRequestHistoryStages,
            'SJJZ',
            'GJHC',
            'SGYY',
            'RWPG',
          ]
        }
          break
        case 'JL': {
          state.isShowAntCompareStages.push('DYSH')
        }
        break
        case 'ZJ': {
          state.isShowAntCompareStages = []
        }
        break
        case 'BJ': {
          state.isShowAntCompareStages = []
        }
      }
    }
    const hiddenSchemeCollapse = () => {
      const hiddenSchemeStages = ['LXPF', 'SGYY']
      if (hiddenSchemeStages.includes(state.currentChildStage)) {
        const schemeIndex = state.collapses.findIndex(
          (collapse) => collapse === 'scheme',
        )
        state.collapses.splice(schemeIndex, 1)
      } else {
        !state.collapses.includes('scheme') && state.collapses.push('scheme')
      }
    }

    //附件上传结果
    const uploadRes = (data) => {
      data.fileType = null
      if (data.classify) {
        data.classify = state.filesUploadClassify.find(
          (i) => i.value === data.classify,
        )?.name
      }
      state.filesTableData.push(data)
      getCurrentStagePlanSite.value.attachments?.push(data)
    }
    //删除附件
    const delAttachment = async (row) => {
      const { id } = row.item
      if (id) {
        const data = await attachmentApi.delete(id)
        data.code == HTTP_CODE.SUCCESS && delFiles(row.index)
      } else {
        delFiles(row.index)
      }
    }
    const delFiles = (index) => {
      state.filesTableData.splice(index, 1)
      getCurrentStagePlanSite.value.attachments?.splice(index, 1)
    }
    const downloadFields = async (row) => {
      const {id} = row.item
      await attachmentApi.download(id)
    }
    const useTemplateDownload = (command) => {
      const name = command.split('/')
      name?.length && attachmentApi.downloadUrl(command, name[name.length - 1])
    }

    // 施工预约
    const setConstructionAppointment = (data) => {
      state.constructionAppointment.constructionStartDate =
        data && data.constructionStartDate
      state.constructionAppointment.constructionEndDate =
        data && data.constructionEndDate
      data &&
      data.constructionSubscribeUser &&
      (state.constructionAppointment.constructionSubscribeUser =
        data.constructionSubscribeUser)
      state.nodeExtendData = state.constructionAppointment
    }
    const switchTabTrigger = (childStage, nodeId) => {
      childStage === 'LXYJ' && requestSchemeData() //方案选择
      childStage === 'CSDD' && requestSiteElectricData() //传输调度
      childStage === 'SJJZ' && requestAutoOpenSiteData() //自动开站

      if (childStage === 'LXPF') {
        let timer = null
        timer = setTimeout(() => {
          constructionPeroid.value?.requestPeoridList()
          clearTimeout(timer)
          timer = null
        }, 0)
      }

      auditCheck[childStage] && requestAuditCheck(nodeId) // audit审核

      //系统判断结果
      state.type !== 'new' &&
      state.showSystemStages.includes(state.currentStage) &&
      requestSiteAlarmData(nodeId)

      requestCrsCheckData();

      //采集对比更新信息
      requestCrsCuUpdateData();

      // 省份区分
      switch (sysconfig.CURRENT_PROVINCE_CODE) {
        case 'HN': {
          // 湖南光调
          childStage === 'XQBH' && requestLightData()
          state.crsNodes.includes(childStage) && requestCrsResult()
        }
          break
        case 'JX': {
          // 江西电调
          if (childStage === 'SGYY') {
            nextTick(() => {
              scheme.value.$children[2].$children[0].requestElectronicList()
            })
          }
        }
          break
      }
    }

    const submit = async (params) => {
      validData = fieldConfigValidate(getCurrentStageFields.value, getCurrentStagePlanSite.value, true)
      if (!validData.length) {
        if (state.siteCmccId && state.inResourcePool &&
          recordXqSiteData.areaCompany !==
          getCurrentStagePlanSite.value.xqSite.areaCompany &&
          !isCurrentProvince('ZJ')
        ) {
          $message.error(`该站点已超出原需求区县范围！`)
          return
        }
        demand.value.submitLoading = true
        xqFillBackSite(getCurrentStagePlanSite.value)

        try {
          getCurrentStagePlanSite.value.nodeId = state.nodeId
          getCurrentStagePlanSite.value.siteList[0].referenceInfo =
            state.reference
          getCurrentStagePlanSite.value.siteList[0].rpList =
            getCurrentStagePlanSite.value.siteList[0].rpList?.filter(
              (i) => i.type,
            )
          if(params) {
            getCurrentStagePlanSite.value.assignUserList = params.transferUserList
            getCurrentStagePlanSite.value.smsNoticeUserList = params.smsNoticeUserList
          }
          const result = await service.save(getCurrentStagePlanSite.value)
          if (result.code === HTTP_CODE.SUCCESS) {
            $message.success('需求提交成功!')
            useBackForwardRoute($route)
          }
        } catch (e) {
            $message.error('提交失败,' + (e.message ? e.message : '请刷新后重试'))
        } finally {
          demand.value.submitLoading = false
        }
      } else {
        validNotify.show({ h: $createElement, data: validData })
      }
    }
    const temporarySave = async (hideTips) => {
      try {
        !hideTips && (state.temporarySaveLoading = true)
        getCurrentStagePlanSite.value.nodeId = state.nodeId
        getCurrentStagePlanSite.value.siteList[0].rpList =
          getCurrentStagePlanSite.value.siteList[0].rpList?.filter(
            (i) => i.type,
          )

        const postData = Object.assign({}, getCurrentStagePlanSite.value, {flowKey: state.flowKey})
        const result = await service.save(postData)
        if (result.code === HTTP_CODE.SUCCESS) {
          if (result.extend) {
            $confirm(result.msg, '警告', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }).then(async () => {
                getCurrentStagePlanSite.value.overlay = true
                await temporarySave(hideTips)
              })
              .catch(() => {
                $set(taskDeal.value, 'loading', false)
                return false
              })
          } else {
            if(!hideTips) {
              $message.success('保存成功!')
              state.recordOriginStagePlanSite = result.data
            }
            return true
          }
        } else {
          state.preventTaskHandle = true
          getCurrentStagePlanSite.value.overlay = false
          $set(taskDeal.value, 'loading', false)
          $message.error('保存失败!')
          return false
        }
      } catch (error) {
        state.preventTaskHandle = true
        $set(taskDeal.value, 'loading', false)
        $message.error('保存失败!' + error.message )
        return false
      } finally {
        state.temporarySaveLoading = false
      }
    }
    const openModal = (type) => {
      state.dealTask = true
      state.dealType = type
    }
    const useFields = (code) =>
      getCurrentStageFields.value && getCurrentStageFields.value[code]

    const useShowTemporarySaveButton = (key, fields) => {
      const filterSaveChildStages = ['XQK', 'LXPF']
      if (
        ableToHandleTask.value &&
        !filterSaveChildStages.includes(state.currentChildStage) &&
        !state.specialTable.includes(key) &&
        fields.some((field) => field.formEdit)
      ) {
        state.isShowSaveChildStage = true
      }
    }

    const handleClick = (tab) => {
      if (state.currentChildStage === tab) return
      state.loading = true

      let timer = null
      timer = setTimeout(() => {
        state.switchTab = tab
        if (!['stageChangeRecord'].includes(tab)) { // non stage tabs
          state.currentChildStage = tab
          const currentStageData = state.tabStages.find(
            (stage) => stage.childStage === tab,
          )
          const {stage, nodeId} = currentStageData
          if (tab === 'handle') {
            !state.historyTableData.length && requestHistory()
          } else if (tab === 'file') {
            !state.allFilesTableData.length && requestAllFiles()
          } else if (nodeId) {
            if (tab !== orderChildStage) {
                requestFields(stage, nodeId)
                requestPlanSite(stage, nodeId)
                state.currentStage = stage
            }
            hiddenSchemeCollapse()
            switchTabTrigger(tab, nodeId)
            requestSiteEnchosuerData(nodeId)
            showDiffFieldValues(state.currentStagePlanSite)
          }

          state.currentStage = stage
        } else {
          state.currentChildStage = '';
        }
        nextTick(() => {
          state.loading = false
          clearTimeout(timer)
          timer = null
        })
      }, 20)
    }

    const validFaild = (msg) => {
      state.dealTask = false
      state.preventTaskHandle = true
      $set(taskDeal.value, 'loading', false)
      return $message.warning({message: msg, duration: 15000, showClose: true})
    }

    const handleTask = async (params) => {
      //新疆数据规划驳回施工验收
      state.taskParams = params
      state.isXjReject = false;
      if('2_XJ_jt557774589a66472a81fd4d9266dbd586' === params.sequenceCode) {
        params.sequenceCode = '2'
        state.isXjReject = true;
      }
      const {sequenceCode} = params
      // 1.驳回 = 2、挂起 = hanghup 不保存工单
      const noSubmitCode = ['2', 'hanghup']
      state.preventTaskHandle = false

      // 特殊阶段通过校验
      const beforeFlowValidResult = await beforeFlowStageValid(sequenceCode)

      if (beforeFlowValidResult) {
        state.dealTask = false
        $set(taskDeal.value, 'loading', false)
        validNotify.show({
          h: $createElement,
          data: [beforeFlowValidResult],
        })
        return
      }

      // 2.isShowSaveChildStage = true 显示暂存按钮 执行工单保存
      // 转派不保存工单
      state.isShowSaveChildStage &&
      !noSubmitCode.includes(sequenceCode) &&
      state.dealType !== 'transfer' &&
      (await beforeHandleSave())

      // 处理江西电调逻辑
      state.currentChildStage === 'SGYY' &&
      isCurrentProvince('JX') &&
      handleJxElectronic()
      // 云南需求变更审核特殊处理
      handleYNXQBGSH(params)
      if (!state.preventTaskHandle ) {
        if(!showInterfaceTips(sequenceCode)){
          await executeHandleTask(params)
        }
      }
    }
    // 云南需求变更审核特殊处理
    const handleYNXQBGSH = (params) => {
      if (!isCurrentProvince('YN')) {
        return;
      }
      // 正常流转使用当前节点id+流向code（sequenceCode）流转

      // 云南需求变更 流向字典值
      const DEMAND_CHANGE_FLOW_SEQUENCE_CODE = '2003'
      params.taskCompleteCustomMethod = ''

      // 需求变更特殊处理标志
      let handleXQBG = state.currentStage === 'GHJD' && state.currentChildStage === 'GHK'
      handleXQBG = handleXQBG || (state.currentStage === 'SGJD')
      handleXQBG = handleXQBG && DEMAND_CHANGE_FLOW_SEQUENCE_CODE === params.sequenceCode

      // 需求变更审核特殊处理标志
      let handleXQBGSH = state.currentStage === 'GHJD' && state.currentChildStage === 'XQBGSH'
      handleXQBGSH = handleXQBGSH && params.sequenceCode === '1'
      if (handleXQBG || handleXQBGSH) {
        // 自定义流转
        params.taskCompleteCustomMethod = 'taskCompleteYNXQBG'
        // 自定义流转的参数
        params.nodeId = handleXQBGSH ? state.nodeId : 'jt37833ccf884f4525abae816983e4294d'
        params.remark = handleXQBGSH ? '需求变更审核-自动流转' : '需求变更-自动流转'
        params.xqbgsh = handleXQBGSH
      }
    }
    const executeHandleTask = async (params) => {
      const {sequenceCode} = params
      // 3. 工单流转
      let method =
        state.dealType === 'transfer' ? 'transferDispatch' : 'handleTask'
      if(isCurrentProvince("BJ") && state.dealType === 'transfer') {
        method = "transferDispatch_bj"
      }
      const commonParams = {
        nodeId: state.nodeId,
        remark: params.remark,
        siteIdCmcc: state.siteCmccId,
        flowKey: state.flowKey,
      }
      let handleParams = Object.assign(
        {},
        commonParams,
        {
          taskId: state.taskId,
          nodeExtendData: state.nodeExtendData,
          stageCode: state.tabStages[0].stage,
        },
        params,
      )

      if (params.taskCompleteCustomMethod?.length > 0) {
        method = params.taskCompleteCustomMethod
        commonParams.nodeId = params.nodeId
      }

      // 单独区分挂起
      if (sequenceCode === 'hanghup') {
        method = 'orderHangUp'
        handleParams = Object.assign({}, commonParams, {
          taskInsId: state.taskId,
          flowInsId: state.formInsId,
        })
      }
      //工单流转前进行流转提示
      handleTaskMsg(method,handleParams)
    }

    const handleTaskSubmit = async (method,handleParams) => {
      //新疆数据规划驳回施工验收
      if (state.isXjReject) {
        handleParams.dealExtendData = handleParams.dealExtendData || {};
        handleParams.dealExtendData = Object.assign(handleParams.dealExtendData, { "isXJSjghRejectSgys" : "true" });
      }

      try {
        const data = await service[method](handleParams)
        if (data.code === HTTP_CODE.SUCCESS) {
          $message.success('工单处理成功!')
          if (isCurrentProvince("JL") && auditCheck[state.currentChildStage]
            && auditCheck[state.currentChildStage].showAuditResult
            && sequenceCode === '1'){
            await requestAuditCheck(state.nodeId,"qryPlanAuditResult")
            state.jLAuditResult = true
          }else{
            useBackForwardRoute($route)
          }
        } else if (data.code === HTTP_CODE.VALID_FAIL) {
          let callback = data.data && data.data.callback
          if (callback) {
            if (callback === 'requestSiteAlarmData') {
              const { nodeId } = state
              await requestSiteAlarmData(nodeId)
            }
          }
          $message.warning({message: data.msg, duration: 15000, showClose: true})
        }
      } finally {
        $set(taskDeal.value, 'loading', false)
        state.dealTask = false
      }
    }

    //流转前流转提示
    const handleTaskMsg = (method,params) => {
      qryNodeStageMsg(params).then(res =>{
        if(res.data){
          const list = res.data
          let msg = ''
          list.forEach(item =>{
            msg += item.msg +'<br>'
          })
          $confirm(msg, '流转提示', {
            confirmButtonText: '继续流转',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
            handleTaskSubmit(method,params)
          }).catch(() => {
            $message({
              type: 'warning',
              message: '已取消流转'
            });
            $set(taskDeal.value, 'loading', false)
          });
          //没有配置流转提示继续流转
        }else{
          handleTaskSubmit(method,params)
        }
      }).catch(er =>{
        handleTaskSubmit(method,params)
      })
    }
    // 特殊阶段流转前校验
    const beforeFlowStageValid = async (sequenceCode) => {
      let preventFlow = false
      if (sequenceCode == '1') {
        switch (state.currentChildStage) {
          case 'SGGK': {
            const scheme = getCurrentStagePlanSite.value.siteList[0]
            if (
              isCurrentProvince('HN') &&
              !state.isSFSite &&
              !state.isNBSite &&
              scheme.indoorFlag !== 'Picosite'
            ) {
              const share = 'Share_Sitetype'

              let isShare = true
              for (const enodeb of scheme.enodebList) {

                for (const cell of enodeb.cellList) {
                  for (const rru of cell.rruList) {
                    if (rru.rruReuse !== share) {
                      isShare = false
                      break
                    }

                    for (const ant of rru.antennaList) {
                      if (ant.antReuse !== share) {
                        isShare = false
                        break
                      }
                    }
                  }
                }
              }

              // du cu rru ant 设备都不是共用
              if (!isShare) {
                let state = false
                const antComparison = antCompare.value?.dataList

                if (antComparison) {
                  for (const item of antComparison) {
                    if (item.measure_state === 'Y') {
                      state = true
                      break
                    }
                  }

                  !state &&
                  (preventFlow = {
                    title: '姿态仪结果校验',
                    tips: '姿态仪数据未返回,不能提交下一步！',
                  })
                }
              }
            }
          }
            break
          case 'SJJZ': {
            if (!isCurrentProvince('HN')) {
              const firstEnodeb =
                getCurrentStagePlanSite.value.siteList[0].enodebList[0]
              const factory = sysconfig.AUTO_OPEN_SITE_DISPATCH_FACTORY
              if (
                factory &&
                factory.includes(firstEnodeb.duFactory) &&
                state.autoOpenSiteResultData[0]?.open_result !== '开站成功'
              ) {
                preventFlow = {
                  title: '自动开站结果校验',
                  tips: '自动开站未成功，不能提交下一步！',
                }
              }
            }
          }
            break
          case 'GJHC': {
            /**
             * 告警核查阶段存在下列条件不允许流转：
             * 1、告警核查环节有在网告警
             * 2、清除时间至今小于三天
             * 3、流转至该阶段小于三天
             */
            if (isCurrentProvince('HN') && !state.isRejected) {
              const alarmDataList = enodebAlarm.value.alarmData.flat()
              let canSubmit = true
              let msg = '存在未清除告警，不能提交下一步！'

              alarmDataList.map((item) => {
                if (item.status == '在网') canSubmit = false
                if (item.clear_time) {
                  let curTime = new Date().getTime()
                  let clearTime = new Date(item.clear_time).getTime()
                  if (curTime - clearTime < 259200000) {
                    canSubmit = false
                    msg = '告警清除时间距今小于三天，不能提交下一步！'
                  }
                }
              })
              //流转至该阶段小于三天
              if (state.historyTableData.length > 2 && !state.isRejected) {
                let curTime = new Date().getTime()
                let updateTime = new Date(
                  state.historyTableData[
                  state.historyTableData.length - 2
                    ].create_time,
                ).getTime()
                if (curTime - updateTime < 259200000) {
                  canSubmit = false
                  msg = '工单流转至该阶段至今小于三天，不能提交下一步！'
                }
              }

              !canSubmit &&
              (preventFlow = {
                title: '告警核查结果校验',
                tips: msg,
              })
            }
          }
            break
          case 'DZYZ': {
            //单站验证阶段是否返回验证结果 或者 有线下验证附件
            if (isCurrentProvince('HN')) {
              if (state.canSubmitNext || state.filesTableData.length) {
                return
              }
              preventFlow = {
                title: '单验测试结果校验',
                tips: '单验测试未完成，不能提交下一步! ',
              }
            }
          }
            break
          case 'RWPG': {
            if (isCurrentProvince('HN')) {
              //入网评估阶段判断性能是否满足流转条件
              const data = enodebPerformance.value.performanceData
              const canSubmit =
                data.length &&
                data.every((item) => item.some((i) => i.result == '指标达标'))

              !canSubmit &&
              (preventFlow = {
                title: '性能指标结果校验',
                tips: '性能指标未通过，不能提交下一步! ',
              })
            }
          }
            break
        }
      }
      return preventFlow
    }
    const clickItem = ref({})
    const beforeHandleSave = async () => {
      // 1. 保存之前校验表单数据 现阶段只校验必填项
      state.preventTaskHandle = false
      beforeHandleFormValid()

      if (!validData.length) {
        const result = await beforeHandleInterfaceValid()
        !result && (await temporarySave(true))
      } else {
        state.dealTask = false
        state.preventTaskHandle = true
        $set(taskDeal.value, 'loading', false)
        validNotify.show({ h: $createElement, data: validData ,clickItem:clickItem})
      }
    }

    const beforeHandleFormValid = () => {
      validData = fieldConfigValidate(
        getCurrentStageFields.value,
        getCurrentStagePlanSite.value,
        false,
      )
      //北京-当基带设备的设备类型=CU的时候整个表单跳过校验
      if (isCurrentProvince('BJ')) {
        validData = validData.filter(
          (item) => !(item.name === 'baseBand' && item.title?.endsWith('--CU')),
        )
      }
      // 字段自定义校验
      if (isCurrentProvince('HN')) {
        const customValid = fieldCustomValidate(
          getCurrentStageFields.value,
          getCurrentStagePlanSite.value,
        )

        customValid && (validData = [...validData, ...customValid])
      }
    }
    const beforeHandleInterfaceValid = async () => {
      // 后台接口校验
      // 在页面校验通过后再执行  避免频繁调用接口
      try {
        if (state.currentStageFields) {
          validData = await interfaceValid(
            getCurrentStageFields.value,
            getCurrentStagePlanSite.value,
          )

          if (validData?.length) {
            state.dealTask = false
            state.preventTaskHandle = true
            $set(taskDeal.value, 'loading', false)
            validNotify.show({ h: $createElement, data: validData })
            return true
          }
        }
      } catch (e) {
        state.dealTask = false
        state.preventTaskHandle = true
        $set(taskDeal.value, 'loading', false)
        $message.error('校验失败！不能流转下一步')
        return true
      }
    }
    const handleJxElectronic = () => {
      const valid = []
      const electronicList =
        scheme.value.$children[2].$children[0].electronicList

      getCurrentStagePlanSite.value.siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          const currentElectronic = electronicList.find(
            (i) => i.enodebId == enodeb.enodebId,
          )

          const electronicValid = fieldsRequiredValid(
            getCurrentStageFields.value.t_jx_electric,
            currentElectronic || {},
          )

          if (electronicValid?.length) {
            valid.push({
              title: `
              方案$
              {
                siteIndex + 1
              }
              --基站$
              {
                enodebIndex + 1
              }
              `,
              fields: electronicValid,
            })
          }
        })
      })
      if (valid.length) {
        state.dealTask = false
        validData = valid
        state.preventTaskHandle = true
        $set(taskDeal.value, 'loading', false)
        validNotify.show({ h: $createElement, data: validData })
      } else {
        service.saveJxElectronic(electronicList)
      }
    }
    //form表单值该变时触发
    const setPlanSite = (data) => {
      // console.log('handle-index,data:',data)
      // 当没有基站时才自动生成一个基站
      dynamicGenerationEnodeb(data)//动态生成enodeb
      enodebExtendsXqSite(data)//设置enodeb值
      antennaExtendsXqSite(data)
    }
    const xqFillBackSite = (data) => {
      const { siteList, xqSite } = data
      if (siteList) {
        //xqSite里面的roofList，roomList等是空的，要删除，不然被同步到siteList[0]，会覆盖
        Object.keys(xqSite).forEach((key) => {
          if (key.endsWith('List')) {
            delete xqSite[key]
          }
        })

        state.inResourcePool &&
        (siteList[0] = Object.assign(siteList[0], xqSite))

        const { siteNameCmcc, longitude, latitude, address } = xqSite
        const { roomList, roofList, enodebList } = siteList[0]

        // 需求站点信息同步 第一个机房 第一个天面
        const roomSiteMap = {
          roomNameCmcc: siteNameCmcc,
          roomLon: longitude,
          roomLat: latitude,
          address,
        }
        const roofSiteMap = {
          roofNameCmcc: siteNameCmcc,
          roofLon: longitude,
          roofLat: latitude,
          address,
        }

        if (isCurrentProvince('HN')) {
          // 湖南特别定制
          delete roomSiteMap.roomNameCmcc
          delete roofSiteMap.roofNameCmcc

          const rruSiteMap = {
            rruLongitude: longitude,
            rruLatitude: latitude,
          }

          enodebList?.forEach((enodeb) => {
            enodeb?.cellList.forEach((cell) => {
              cell?.rruList.forEach((rru) => {
                Object.keys(rruSiteMap).forEach((key) => {
                  !rru[key] && (rru[key] = rruSiteMap[key])
                })
              })
            })
          })
        }

        // 检查roomList是否存在并且长度大于0，检查roomSiteMap是否为对象
        if (Array.isArray(roomList) && roomList.length > 0 && typeof roomSiteMap === 'object' && roomSiteMap !== null) {
          Object.keys(roomSiteMap).forEach((key) => {
            // 确保isNull函数已定义
            if (typeof isNull === 'function' && isNull(roomList[0][key])) {
              roomList[0][key] = roomSiteMap[key];
            }
          });
        }

        // 检查roofList是否存在并且长度大于0，检查roofSiteMap是否为对象
        if (Array.isArray(roofList) && roofList.length > 0 && typeof roofSiteMap === 'object' && roofSiteMap !== null) {
          Object.keys(roofSiteMap).forEach((key) => {
            // 确保isNull函数已定义
            if (typeof isNull === 'function' && isNull(roofList[0][key])) {
              roofList[0][key] = roofSiteMap[key];
            }
          });
        }

      }
    }
    //动态生成enodeb
    const dynamicGenerationEnodeb = async (data) => {
      // console.log('dynamicGenerationEnodeb方法被执行.',data);
      // const { nsaAnchor, openMimo, networkStandard } = getCurrentStagePlanSite.value.xqSite//站点信息
      const enodebList = getEnodebList.value
      const enodebDataCopy = deepClone(enodebList[0], true, false, true)
      let networkStandardSite = getCurrentStagePlanSite.value.xqSite['networkStandard'];
      // console.log('networkStandardSite:',networkStandardSite,getCurrentStagePlanSite.value.xqSite);
      //反开4G
      if(data && data['openMimo'] && networkStandardSite==='5G' && (nodeId==='jt3b453adde06a4772b465c4d6fb1ccc4e' || tagsViewTitle==='新增需求')){
        //反开4G为是，添加基站
        if(data['openMimo'].toUpperCase() === 'Y'){
          $set(enodebDataCopy, 'networkStandard', 'TDD-LTE');
          $set(enodebDataCopy, 'enodebType', '3D MIMO')

          enodebDataCopy.cellList.forEach((item) => {
            $set(item, 'networkStandard', 'TDD-LTE')
            $set(item, 'freBand', 2600)
          })
          enodebDataCopy.hasTmpAdd=true;//标记此基站是否临时添加，在网络制式切换为非5G后删除，避免添加多个基站
          // const addTmpObj = _.find(enodebList,{hasTmpAdd:true});
          // if(addTmpObj === null || addTmpObj === undefined)
            enodebList.push(deepClone(enodebDataCopy))//每次点击是都增加一个基站
        }else if(data['openMimo'].toUpperCase() === 'N'){
          //为否，删除网络制式为TDD-LTE,FDD-LTE,NB-LOT的最末尾一个基站，且保留第一个基站
          for(let i=1;i<enodebList.length; i++){
            const item =enodebList[i];
            if(['TDD-LTE','FDD-LTE','NB-IoT'].includes(item['networkStandard'])){
              enodebList.splice(i,1);
              i--;
              break;
            }
          }
        }else{}
        enodebList.forEach((enodeb, index) => {
          enodeb.sort = index
          enodeb.enodebIdPlan = index + 1
        })
      }
    }
    const enodebExtendsXqSite = (data) => {
      // console.log('enodebExtendsXqSite方法被执行,data:',data);
      const { siteNameCmcc, networkMode, networkStandard, indoorFlag, configure, frequencyBand} = data
      const siteName = siteNameCmcc || getCurrentStagePlanSite.value.xqSite.siteNameCmcc

      let IS5G = false
      //判断'制式'为'5G'，且'反开4G' 为'是'
      if(getCurrentStagePlanSite.value.xqSite.networkStandard =='5G' && getCurrentStagePlanSite.value.xqSite.openMimo =='Y'){
        IS5G = true
      }
      _.forEach(getEnodebList.value,(enodeb) => {
        const currentTypeData = enodebTypeMap[enodeb.enodebType]
        data.hasOwnProperty('configure') && $set(enodeb, 'configure', configure)
        data.hasOwnProperty('indoorFlag') && $set(enodeb, 'indoorFlag', indoorFlag)
        data.hasOwnProperty('networkMode') && $set(enodeb, 'networkMode', networkMode)
        data.hasOwnProperty('networkStandard') && $set(enodeb, 'networkStandard', networkStandard)
        data.hasOwnProperty('frequencyBand') && $set(enodeb, 'cellBand',  frequencyBand)
        data.hasOwnProperty('networkStandard') && $set(enodeb, 'enodebType', getEnodebTypeByNetworkStandard(networkStandard));//根据网络制式获取基站模式

        if (data.hasOwnProperty('siteNameCmcc') || data.hasOwnProperty('networkStandard')) {
          $set(enodeb, 'enodebNamePlan', (siteName || '') + (currentTypeData?.enodebNamePlan || ''))
        }
        //当'制式'为'5G'，且'反开4G' 为'是'时进行匹配修改,基站模式为3D MIMO
        if(IS5G && getCurrentStagePlanSite.value.xqSite.frequencyBand && enodeb.enodebType =='3D MIMO'){
          switch (getCurrentStagePlanSite.value.xqSite.frequencyBand) {
            case '700':
              $set(enodeb, 'networkStandard', 'FDD-LTE')
              $set(enodeb, 'cellBand', '900')
              return;
            case '2600':
              $set(enodeb, 'networkStandard', 'TDD-LTE')
              $set(enodeb, 'cellBand', '2600')
              return;
            case '4900':
              $set(enodeb, 'networkStandard', 'TDD-LTE')
              $set(enodeb, 'cellBand', '2600')
            default:
              return;
          }
        }
      })
    }

    const antennaExtendsXqSite = (data) => {
      const { latitude, longitude, indoorFlag } = data
      if (latitude || longitude || indoorFlag) {
        getEnodebList.value.forEach((enodeb) => {
          enodeb.cellList.forEach((cell, index) => {
            cell.rruList[0].antennaList.forEach((ant) => {
              data.hasOwnProperty('latitude') && $set(ant, 'antLat', latitude)
              data.hasOwnProperty('longitude') && $set(ant, 'antLon', longitude)
              data.hasOwnProperty('indoorFlag') &&
              $set(
                ant,
                'antAngle',
                indoorFlag == 'MacroSite' ? index * 120 : 360,
              )
            })
          })
        })
      }
    }

    // request
    const requestFields = async (stage, nodeId, record) => {
      const data = await businessFieldConfig.queryFields('GH', nodeId)
      const fields = data.data
      //console.log('requestFields',data)
      if(fields){
        Object.keys(fields).forEach((key, index) => {
          const replaceKey = key.replace('t_5g_', '')
          fields[replaceKey] = fields[key]
          replaceKey !== key && delete fields[key]
         // console.log('requestFields',Object.assign({},fields))
          const currentFields = fields[replaceKey]

          if(currentFields) {
            // 取消必填
            if(sysconfig.PLAN_CANCEL_REQUIRE == 'ENABLE') {
              currentFields.forEach(field => field.required = false)
            }
           useFieldsChildren(currentFields)
          }
          useShowTemporarySaveButton(replaceKey, currentFields)
        })
      }


      // 扩展设备字段 现在使用一份有三个地方显示
      // 由于数据不同 会导致生成字段的下拉选项不一致 需要复制一份
      // if (fields.equipment_iu) {
      //   fields.equipment_enodeb_iu = deepClone(fields.equipment_iu)
      //   fields.equipment_baseBand_iu = deepClone(fields.equipment_iu)
      // }

      state.currentStageFields = fields
      record && (state.recordOriginStageFields = fields)
    }

    const thisInstance = getCurrentInstance();
    if (isCurrentProvince("JL")) {
      thisInstance.proxy.$root.$on('enableShowChangeToolTip',
        evt => enableShowChangeToolTip(evt));
    }

    function showDiffFieldValues(planSite) {
      if (isCurrentProvince("JL")) {
        stageChangeRecordApi.listLatest(state.siteCmccId).then(res => {
          usePlanSiteChangeLogCompletion(planSite, res.data || [], $set);
        })
      }
    }
    //todo 此处逻辑并没有考虑可行性研究节点的特殊情况是不返回siteList的 后续修改处理报错
    const requestPlanSite = async (stage, nodeId, record) => {
      const data = await service.planSite(state.siteCmccId, stage, nodeId);
      const planSite = data?.data
      if (planSite) {
        !planSite.siteList && (state.isShowSaveChildStage = false)
        if (!planSite.siteList) {
          planSite.siteList = [{}]
        }

        usePlanSiteCompletion(planSite)
        showDiffFieldValues(planSite);
        xqFillBackSite(planSite)
        state.currentStagePlanSite = planSite
        record && (state.recordOriginStagePlanSite = planSite)

        try {
          state.tabStages.map((i) => i.stage).includes('gplot') &&
          (state.siteGraphModel = useCanvasSiteGraphModel(
            state.recordOriginStagePlanSite.siteList[0]?.enodebList,
          ))
        } catch (e) {
          console.log('拓扑图数据结构失败,基带设备数据结构错误')
        }

        // 备份需求站点数据
        state.siteCmccId &&
        state.inResourcePool &&
        (recordXqSiteData = deepClone(planSite.xqSite))

        //判断站点类型，NB站点、室分站点
        state.isNBSite = planSite.siteList[0].networkStandard === 'NB-IoT'

        const covArea = planSite.siteList[0].covArea
        state.isCovIndoorSite =
          sysconfig.PLAN_COV_INDOOR_SITE?.split(',').includes(covArea)

        const indoorFlag = planSite.siteList[0].indoorFlag
        state.isSFSite = sysconfig.PLAN_SF_SITE
          ? sysconfig.PLAN_SF_SITE.split(',').includes(indoorFlag)
          : indoorFlag == 'RoomSite'

        // 当基站需求站型选择室分、皮基站时这3个字段在天面tab显示不必填，其他站型时默认隐藏
        if(!['RoomSite','Picosite'].includes(indoorFlag)) {
          const hideRoofFields = [
            'deploymentMode',
            'distributedSystemStreams',
            'roomConstructionMethod'
          ]
          state.currentStageFields.roof =
              state.currentStageFields.roof.filter(
                (field) => !hideRoofFields.includes(field.name),
              )
        }

        if (state.currentStageFields?.roof?.length && planSite.xqSite) {
          // 高铁字段：铁轨名称、铁轨垂直距离、铁轨高度、相对铁轨挂高
          const nameArr_highSpeedRailWay = [
            'roofRailName',
            'roofRailDist',
            'roofRailHeight',
            'roofRailRelativeHeight',
          ]
          // 室分字段：项目类型、分布系统建设类型、室分系统编号、物理射频单元数、覆盖面积、覆盖方式
          let nameArr_roomSite = []

          // 设置initHide 校验的时候会触发必填项  直接删除字段
          if (planSite.xqSite.scene !== 'High-SpeedRailway') {
            state.currentStageFields.roof =
              state.currentStageFields.roof.filter(
                (field) => !nameArr_highSpeedRailWay.includes(field.name),
              )
          }

          // 判断室分字段是否显示
          if (!state.isSFSite) {
            nameArr_roomSite = [
              //'rdProjectType',
              'rdConstructionType',
              'rdSysNo',
              'rdRruNum',
              'rdCoverage',
              'rdCoverageArea',
            ]
          } else if (isCurrentProvince('HN')) {
            // 宏站 湖南 显示省内天面名称
            nameArr_roomSite = ['sourceRoofNameCmcc']
          }

          if (nameArr_roomSite.length) {
            state.currentStageFields.roof =
              state.currentStageFields.roof.filter(
                (field) => !nameArr_roomSite.includes(field.name),
              )
          }
        }

        if (planSite.siteList[0].referenceInfo) {
          state.reference = planSite.siteList[0].referenceInfo
        }

        // 立项批复的时候，建设工期和阶段，需根据city查询，enodeb没有city，往enodeb压city
        if (state.currentChildStage === 'LXPF') {
          planSite.siteList[0].enodebList.forEach((item) => {
            $set(item, 'city', planSite.xqSite.city)
          })
        }

        //设计审核阶段查看光调是否共用
        if (state.currentChildStage == 'SJSH') {
          planSite.siteList[0].enodebList.map((item) => {
            if (item.duReuse == 'Share_Sitetype') state.lightIsPublic = true
          })
        }

        //施工预约
        if (state.currentChildStage === 'SGYY' && planSite.siteList[0]) {
          if (!planSite.siteList[0].constructionSubscribeUser) {
            // 默认当前用户
            planSite.siteList[0].constructionSubscribeUser = userName
            state.constructionAppointment.constructionSubscribeUser = userName
          } else {
            state.constructionAppointment.constructionSubscribeUser =
              planSite.siteList[0].constructionSubscribeUser
            state.constructionAppointment.constructionStartDate =
              planSite.siteList[0].constructionStartDate
            state.constructionAppointment.constructionEndDate =
              planSite.siteList[0].constructionEndDate
          }

          if (isCurrentProvince('HN')) {
            //修复旧工单驳回后已有预约人，不会显示默认预约人；修改后预约人不可改变
            const construction = await commonQuery(
              'qryCityConstructionSubscribeUser',
              {
                city: planSite.siteList[0].city,
              },
            )
            state.constructionAppointment.constructionSubscribeUser =
              construction.data[0]?.user_name
            planSite.siteList[0].constructionSubscribeUser =
              state.constructionAppointment.constructionSubscribeUser
          }
          state.nodeExtendData = state.constructionAppointment
        }

        //逻辑基站里的传输配置对象为空则补充对象进去,避免传输配置的必填校验失效
        if (getCurrentStagePlanSite.value.siteList) {
          checkEmptyEnodebTransfer(getCurrentStageFields.value, getCurrentStagePlanSite.value.siteList)
        }
        // yn_system_202512321231|naas_cmcc_5g_plan_sjjd
        const user = useGetters('user')()
        try {
          const msg = planSite.siteList[0].province + "_" + user.account + "_" + new Date().format('yyyyMMddHHmm') +"|" +"naas_" + "cmcc_5g_plan_" + planSite.stageCode
          sysconfig.open_wartermark &&
          $updateWatermark({
            text: msg.toLowerCase()
          })
        } catch (e) {
          console.log("水印更新失败")
        }
        console.log('state',state)
      }
    }

    const requestHandleButtons = async () => {
      const data = await handleButtons(state.nodeId, state.taskId)
      state.buttons = data.data

      //施工管控, 工单状态是： 非挂起时,工单可挂起
      const isShowHangup = sysconfig.PLAN_HANGUP_NODES
        ? sysconfig.PLAN_HANGUP_NODES.split(',').includes(
          state.currentChildStage,
        )
        : state.currentChildStage == 'SGGK'

      isShowHangup &&
      state.orderState != '3' &&
      (state.buttons.hanghup = '挂起')

      // 挂起工单只能驳回
      if (state.orderState == '3') {
        state.buttons = { 2 : '驳回' }
      }

      //新疆数据规划驳回施工验收,驳回按钮拆分成2个"驳回传输调度","驳回施工验收"
      if (isCurrentProvince('XJ') && state.nodeId === 'jt206c9b2e90de4e77a6710540c80d1913') {
        let enableXjSjghRejectSgys = 'true' === sysconfig.ENABLE_XJ_SJGH_REJECT_SGYS;
        if (enableXjSjghRejectSgys && state.buttons.hasOwnProperty("2") && state.buttons["2"] === "驳回") {
          state.buttons["2"] = "驳回传输调度";
          const entries = Object.entries(state.buttons);
          const index = entries.findIndex(([key]) => key === "2");
          if (index !== -1) {
            entries.splice(index + 1, 0, ["2_XJ_jt557774589a66472a81fd4d9266dbd586", "驳回施工验收"]);
          }
          state.buttons = Object.fromEntries(entries);
        }
      }

      requestDispatchOrder()
    }
    const requestHistory = async () => {
      const data = await commonQueryFieldData('qrySiteFlowHistory', {
        siteIdCmcc: state.siteCmccId,
      })
      state.historyTableTitle = data?.data?.title || []
      state.historyTableData = data?.data?.dataList || []

      // 判断当前节点是否是已驳回节点
      state.isRejected = useJudgeRejected(state.historyTableData, state.nodeId)
    }
    //派单接口
    const requestDispatchOrder = async () => {
      const data = await commonQuery('qryNodeDispatchInterface', {
        procDefKey: state.flowKey,
        nodeId: state.nodeId,
      })
      state.dispatchInterfaceData = data.data
    }

    async function requestSiteAlarmData0(nodeId, taskId, latest) {
      let qrySiteAlarmData = 'qrySiteAlarmData'
      const data = await commonQueryFieldData(qrySiteAlarmData, {
        siteIdCmcc: state.siteCmccId,
        nodeId,
        taskId
      })
      state.systemJudgeHistoryTableTitle = data.data.title
      let tableData = data.data.dataList
      let tableTitle = data.data.title
      return {tableData, tableTitle};
    }

    //系统判断结果
    const requestSiteAlarmData = async (nodeId, taskId) => {
      let {tableData, tableTitle} = await requestSiteAlarmData0(nodeId, taskId);
      state.systemJudgeTableTitle = tableTitle
      state.systemJudgeTableData = tableData
    }

    //传输调度
    const requestSiteElectricData = async () => {
      const data = await commonQueryFieldData('qrySiteElectricResult', {
        siteIdCmcc: state.siteCmccId,
      })
      state.transDispatchHeader = data.data.title
      state.transDispatchData = data.data.dataList
    }

    // 获取方案
    const requestSchemeData = async () => {
      const data = await commonQueryFieldData('qryGHCandidateSite', {
        siteIdCmcc: state.siteCmccId,
      })
      state.schemeHeader = data.data.title
      state.schemeData = data.data.dataList

      // 设置禁用并默认选中
      if (orderChildStage !== state.currentChildStage) {
        const orderStage = state.tabStages.find(
          (tab) => tab.childStage === orderChildStage,
        )
        const currentScheme = state.recordOriginStagePlanSite.siteList[0]
        state.schemeData.forEach((item) => {
          item.disabled = true
          if (item.siteCmccId === currentScheme.siteCmccId) {
            schemeTable.value.selectedRow(item)
          }
        })
      } else {
        state.schemeData.length === 1 &&
        schemeTable.value.selectedRow(state.schemeData[0])
      }
    }

    //获取光调结果
    const requestLightData = async () => {
      const data = await commonQueryFieldData('getLightBySiteId', {
        siteIdCmcc: state.siteCmccId,
      })
      if (data?.data) {
        state.lightTableTitle = data.data.title
        state.lightTableData = data.data.dataList
      }
    }

    const requestCrsCheckData = async () => {
      const data = await commonQueryFieldData('qryCRSReponseCheckInfo', {
        siteIdCmcc: state.siteCmccId,
      })
      if (data?.data) {
        state.crsTableTitle = data.data.title
        state.crsTableData = data.data.dataList
      }
    }

    //采集对比更新信息
    const requestCrsCuUpdateData = async () => {
       const data = await queryCrsCuUpdateData('qryCRSCUResponseUpdateInfo', {
        siteIdCmcc: state.siteCmccId,
      })
      if (data?.data) {
        state.crsCuTableTitle = data.data.title
        state.crsCuTableData = data.data.dataList
      }
    }

    const requestTriggerLight = async () => {
      state.lightLoading = true
      const result = await service.lightData({
        orderNo: state.siteCmccId,
        nodeId: state.nodeId,
        nodeStage: state.currentStage,
      })

      await requestLightData()
      state.lightLoading = false
    }

    //自动开站
    const requestAutoOpenSiteData = async () => {
      const data = await commonQueryFieldData('qrySiteOpenResult', {
        siteIdCmcc: state.siteCmccId,
      })
      state.autoOpenSiteResultHeader = data.data.title
      state.autoOpenSiteResultData = data.data.dataList
    }

    const requestCrsResult = async () => {
      const data = await commonQuery('queryCrsResult', {
        siteIdCmcc: state.siteCmccId,
      })

      if (data.data && Array.isArray(data.data) && data.data.length > 0) {
        state.crsResultText = data.data[0].desc
      }
    }

    // 附件
    const requestSiteEnchosuerData = async (nodeId) => {
      const data = await commonQueryFieldData('qryStageSiteEnclosure', {
        siteIdCmcc: state.siteCmccId,
        nodeId,
        procDefKey: flowKey
      })

      state.filesTableTitle = data.data.title
      state.siteCmccId && (state.filesTableData = data.data.dataList)

      const filesClassify = await commonQuery('qryDictData', {
        code: 'ATTACHMENT_CLASSIFY',
      })
      if (filesClassify?.data) {
        filesClassify.data.forEach((item) => {
          if (item.remark?.includes(state.nodeId)) {
            state.filesUploadClassify.push(item)
          }
        })
      }

      if (sysconfig.PLAN_PREV_FILES === 'ENABLE' && !state.inResourcePool) {
        const nodeStage = state.tabStages.slice(0, -3)
        const currentNodeStageIndex = nodeStage.findIndex(
          (i) => i.childStage === state.currentChildStage,
        )
        if (
          currentNodeStageIndex >= 0 &&
          currentNodeStageIndex < nodeStage.length - 1
        ) {
          const pervFiles = await commonQueryFieldData(
            'qryStageSiteEnclosure',
            {
              procDefKey: flowKey,
              siteIdCmcc: state.siteCmccId,
              nodeId: nodeStage[currentNodeStageIndex + 1].nodeId,
            },
          )

          state.prevNodeFiles = pervFiles?.data.dataList
        } else {
          state.prevNodeFiles = []
        }
      }
    }
    const requestAllFiles = async () => {
      const data = await commonQueryFieldData('qryStageSiteEnclosure', {
        siteIdCmcc: state.siteCmccId,
        procDefKey: flowKey
      })
      state.allFilesTableTitle = data.data.title
      state.allFilesTableData = data.data.dataList
    }

    const auditResultChange = async (row) => {
      const data = await service.auditStateChange(row)
      data.code == 200 && $message.success('修改成功')
    }

    const requestAuditCheck = async (nodeId,commonCode="qrySiteAuditData") => {
      const data = await commonQueryFieldData(commonCode, {
        siteIdCmcc: state.siteCmccId,
        nodeId,
      })
      $set(auditCheck[state.currentChildStage], 'tableTitle', data.data.title)
      $set(auditCheck[state.currentChildStage], 'tableData', data.data.dataList)

    }

    // 宏站审核-渲染
    const renderData = reactive({
      renderId: '',
      renderType: 'page',
    })

    const canRender = async () => {
      renderData.renderId = null
      const { data } = await commonQuery('qryOutdoorTaskByCmccId', {
        siteIdCmcc: state.siteCmccId,
      })
      if (data && data.length) {
        const { id, status, suffix } = data[0]
        if (status === 2) {
          renderData.renderId = id
          renderData.renderType = suffix
        }
      }
    }
    const setRowStyle = (row, rowIndex) => {
      if (row && 'N' === row.passStatus) {
        return 'notPass'
      }
      return ''
    }

    const colseJlAuditDialog = () => {
      state.jLAuditResult = false
      useBackForwardRoute($route)
    }

    //逻辑基站里的传输配置对象为空则补充对象进去,避免传输配置的必填校验失效
    const checkEmptyEnodebTransfer = async (fields, siteList) => {
      if (Array.isArray(siteList)) {
        const transferTabs = await useCommonQueryData('TRANSFER_TABS');
        if (!transferTabs || !Array.isArray(transferTabs)) {
          return;
        }
        const generateDynamicKey = (type) => type === 'transfer_network' ? type : `transfer_config_${type}`;
        const isFieldValid = (fields, dynamicKey) => {
          return fields && typeof fields === 'object' && fields[dynamicKey] !== undefined;
        };
        const transferTypes = transferTabs.reduce((acc, item) => {
          const type = item.code;
          const dynamicKey = generateDynamicKey(type);
          if (isFieldValid(fields, dynamicKey)) {
            acc.push(type);
          }
          return acc;
        }, []);
        if (transferTypes.length === 0) {
          return;
        }
        for (let i = 0; i < siteList.length; i++) {
          const site = siteList[i];
          if (Array.isArray(site.enodebList)) {
            for (let j = 0; j < site.enodebList.length; j++) {
              const enodeb = site.enodebList[j];
              if (typeof enodeb === 'object' && enodeb !== null) {
                const existingTypes = new Set(enodeb.transferConfigList.map(item => item.type));
                for (let k = 0; k < transferTypes.length; k++) {
                  const type = transferTypes[k];
                  if (type === 'transfer_network' && fields.transfer_network) {
                    if (enodeb.transferNetworkList && enodeb.transferNetworkList.length === 0 && !existingTypes.has(type)) {
                      const networkData = {sort: 0}
                      fields.transfer_network?.forEach(item => {
                        networkData[item.name] = null;
                      });
                      enodeb.transferNetworkList.push(networkData);
                    }
                  } else {
                    let addData = {sort: 0, type: type};
                    const dynamicKey = `transfer_config_${type}`;
                    if (enodeb.transferConfigList && fields[dynamicKey] && !existingTypes.has(type)) {
                      fields[dynamicKey]?.forEach(item => {
                        addData[item.name] = null;
                      });
                      enodeb.transferConfigList.push(addData);
                      existingTypes.add(type);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    //是否显示 规划站点合理性评估 tab
    const hasDtnShow = () => {
      if(sysconfig.CURRENT_PROVINCE_CODE==='NM'){
        const config = sysconfig.NM_DTNSREvaluation_ReqConfig
        if(config){
          const configJson = JSON.parse(config);
          const isShow = 'jt87ae499eb89b4361a6a09fefb25f1d31'===nodeId && configJson.enabled===true;//只有内蒙规划审核才显示
          return isShow;
        }
      }
      return false;
    }
    // 获取资管校验异常信息
    const getValidData = async () => {
      if (isCurrentProvince('GX,YN') && state.nodeId === 'jt557774589a66472a81fd4d9266dbd586' ) {
        const data = await getCrsErrorTips(state.siteCmccId)
        if(data.data){
          state.validateItems = data.data || []
          state.showValidateNotification = true
        }

      }
    }

    const handleValidateClose = () => {
      state.showValidateNotification = false
    }

    const handleValidateItemClick = (item) => {
      const targetElement = document.querySelector(item.target)
      if (targetElement) {
        console.log('Found target element:', targetElement)
        console.log('Element position:', targetElement.getBoundingClientRect())
      } else {
        console.log('Target element not found:', item.target)
      }
    }

    const showInterfaceTips = (sequenceCode) => {
      // 判断是否需要显示提示
      if (Array.isArray(state.dispatchInterfaceData) && state.dispatchInterfaceData.length > 0) {
    // 查找包含提示配置的接口
    const interfacesWithTips = state.dispatchInterfaceData.filter(item => item.sequenceCode==sequenceCode && item.showTip  )

    if (interfacesWithTips.length > 0) {
      state.interfaceTipsList = []
      // 遍历所有接口配置
      var shouldShowTip  = []
      shouldShowTip = interfacesWithTips.filter(interfaceItem =>{
        let tipConfigs = []

        // 尝试解析tipConfig字符串
        try {
          if (typeof interfaceItem.tipConfig === 'string') {
            tipConfigs = JSON.parse(interfaceItem.tipConfig)
          } else if (Array.isArray(interfaceItem.tipConfig)) {
            tipConfigs = interfaceItem.tipConfig
          }
        } catch (error) {
          console.error('Error parsing tipConfig:', error)
          return false
        }
        // 确保tipConfigs是数组
        if (!Array.isArray(tipConfigs)) {
          console.warn('tipConfigs is not an array after parsing:', tipConfigs)
          return false
        }
        // 遍历所有配置,判断当前节点和分支是否需要显示提示
        return shouldShowTip = tipConfigs.some(config => {
          // 检查showTipNode和showTipSeq是否为数组，如果不是则转换为数组
          const showTipNode = Array.isArray(config.showTipNode)
            ? config.showTipNode
            : [config.showTipNode]

          const showTipSeq = Array.isArray(config.showTipSeq)
            ? config.showTipSeq
            : [config.showTipSeq]

          const nodeMatch = showTipNode.includes(state.nodeId)
          const seqMatch = showTipSeq.includes(sequenceCode)

          return (nodeMatch && seqMatch && sequenceCode)
        })
      })
      if (shouldShowTip.length>0) {
          shouldShowTip.forEach(obj =>{
              state.interfaceTipsList.push({
              tipInfo: obj.tipContent,
              thirdUrl: obj.interfaceName + obj.interfaceUrl
            })
            // 显示提示信息
          state.interfaceTipsVisible = true
          })
          return true
          //直接流转
        }else{
            return false
        }
    }
      }
    }
    const closeInterfaceTips = async () => {
      $set(taskDeal.value, 'loading', false)
      state.interfaceTipsVisible = false
    }
    const confirmInterfaceTips = async () => {
      state.interfaceTipsVisible = false
      await executeHandleTask(state.taskParams)
    }

    return {
      ...toRefs(state),
      hasDtnShow,
      scheme,
      demand,
      taskDeal,
      antCompare,
      enodebAlarm,
      schemeTable,
      logicStation,
      enodebPerformance,
      crsCheckResult,
      constructionPeroid,
      sysconfig,
      ableToHandleTask,
      getEnodebList,
      getCurrentStageFields,
      getCurrentStagePlanSite,
      isShowRoomSiteCheckDwgManager,
      isShowRoomSiteCheck,
      isShowMacroSiteCheck,
      isShowAidp,
      handleClick,
      handleTask,
      submit,
      openModal,
      useFields,
      temporarySave,
      setPlanSite,
      uploadRes,
      delAttachment,
      downloadFields,
      requestTriggerLight,
      setConstructionAppointment,
      auditResultChange,
      renderData,
      auditCheck,
      useTemplateDownload,
      getOpenTemplateConfig,
      notShowTransferButStageCodeList,
      notShowSchemeStageCodeList,
      notShowDealButStageCodeList,
      setRowStyle,
      colseJlAuditDialog,
      clickItem,
      handleValidateClose,
      handleValidateItemClick,
      interfaceTipsVisible: toRef(state, 'interfaceTipsVisible'),
      interfaceTipsList: toRef(state, 'interfaceTipsList'),
      confirmInterfaceTips,
      closeInterfaceTips,
      showInterfaceTips
    }
  },
  beforeDestroy() {
    async function destroyDeep(vnode) {
      let vnodes
      if (vnode.children || vnode.componentInstance?._vnode?.children) {
        vnodes = vnode.children || vnode.componentInstance._vnode.children
        for (const vn of vnodes) {
          destroyDeep(vn)
        }
      }

      vnode.componentInstance?.$destroy()
      let timer = null
      timer = setTimeout(() => {
        vnode.componentInstance = undefined
        vnode.elm.innerHTML = ''
        clearTimeout(timer)
        timer = null
      }, 0)
    }

    destroyDeep(this._vnode)
    if (isCurrentProvince("JL")) {
      this.$root.$off('enableShowChangeToolTip');
    }
  },
  beforeRouteLeave(to, from, next) {
    validNotify?.hide()
    next()
  },
  destroyed() {
    store.state.plan.currentStage = null;
  },
  components: {
    Scheme: () => import('@/components/Plan/Scheme'),
    Antenna: () => import('@/components/Plan/Antenna'),
    History: () => import('@/components/Flow/History'),
    FileUpload: () => import('@/components/FileUpload'),
    TaskDeal: () => import('@/components/Flow/TaskDeal'),
    DemandTab: () => import('@/components/Plan/DemandTab'),
    PMAnalysis: () => import('@/components/Plan/PMAnalysis'),
    EnodebAlarm: () => import('@/components/Plan/EnodebAlarm'),
    BasicForm: () => import('@/components/BasicComponents/Form'),
    BasicTable: () => import('@/components/BasicComponents/Table'),
    Preview: () => import('@/views/devManager/queryConfig/preview'),
    SingeTestResult: () => import('@/components/Plan/SingeTestResult'),
    CollapseTab: () => import('@/components/BasicComponents/CollapseTab'),
    EnodebPerformance: () => import('@/components/Plan/EnodebPerformance'),
    PMMrDay: () => import('@/components/Plan/PMMrDay'),
    LogicBaseStation: () => import('@/components/Plan/Scheme/LogicBaseStation'),
    IndoorAutoCheck: () => import('@/components/Plan/IndoorAutoCheck'),
    macrositeCheck: () => import('@/components/MacrositeCheck'),
    OutdoorGis: () => import('@/components/Outdoor'),
    CanvasGplot: () => import('@/components/CanvasGplot'),
    AuditRule: () => import('../auditRule'),
    TabAICheck: () => import('@/components/Plan/AICheck/TabAICheck.vue'),
    stageChangeRecord: () => import('@/components/BasicComponents/StageChangeRecord'),
    PriorityRate: () => import('@/components/Plan/PriorityRate'),
    DtnSiteEvalution:()=>import('@/views/plan/DtnSiteEvalution.vue'),
    ValidateNotification: () => import('@/components/BasicComponents/ValidateNotification'),
  },
})
</script>

<style lang='scss' scoped>
@import '@/assets/styles/plan/handle.scss';

::v-deep {
  .notPass {
    color: red;
  }

  table a {
    color: blue;
    text-decoration: underline;
  }

  .errorFile {
    color: blue;
    text-decoration: underline;
  }

  .auditTable span {
    white-space: break-spaces;
  }

  .block {
    text-align: left;
    border-right: 1px solid #eff2f6;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
    vertical-align: top;
    margin-top: 5px;
  }

  .info {
    display: block;
    padding: 10px;
    font-size: 13px;
    font-weight: 700;
  }

  .radio-item {
    margin-left: 15px;
    font-weight: normal;
  }
}

.interface-tips {
  .tips-header {
    margin-bottom: 20px;
    font-size: 16px;
    color: #606266;

    i {
      color: #E6A23C;
      margin-right: 8px;
      font-size: 20px;
      vertical-align: middle;
    }
  }

  .tips-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 0 20px;
  }

  .tip-card {
    margin-bottom: 10px;

    .tip-info {
      font-size: 14px;
      color: #303133;
      margin-bottom: 8px;
    }

    .tip-url {
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }
  }
}

// 自定义滚动条样式
.tips-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }
}
</style>
