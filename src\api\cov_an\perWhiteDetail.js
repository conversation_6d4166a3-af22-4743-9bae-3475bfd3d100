import request from '@/utils/request'

// 查询白名单明细列表
export function listPerWhiteDetail(query) {
  return request({
    url: '/an/perWhiteDetail/list',
    method: 'get',
    params: query
  })
}

// 查询白名单明细详细
export function getPerWhiteDetail(id) {
  return request({
    url: '/an/perWhiteDetail/' + id,
    method: 'get'
  })
}

// 新增白名单明细
export function addPerWhiteDetail(data) {
  return request({
    url: '/an/perWhiteDetail',
    method: 'post',
    data: data
  })
}

// 修改白名单明细
export function updatePerWhiteDetail(data) {
  return request({
    url: '/an/perWhiteDetail',
    method: 'put',
    data: data
  })
}

// 删除白名单明细
export function delPerWhiteDetail(id) {
  return request({
    url: '/an/perWhiteDetail/' + id,
    method: 'delete'
  })
}