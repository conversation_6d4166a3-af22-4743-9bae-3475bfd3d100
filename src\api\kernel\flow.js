import request from '@/utils/request'

// 查询流程信息列表
export function listModel(query) {
  return request({
    url: '/kernel/flow/models',
    method: 'get',
    params: query
  })
}

// 删除流程信息
export function delModel(id) {
  return request({
    url: '/kernel/flow/models/' + id + '/delete',
    method: 'post'
  })
}

// 流转历史
export function history(procInstId){
  return request({
    url: '/kernel/flow/history/' + procInstId ,
    method: 'get',
  })
}

export function basic(data){
  return request({
    url: '/kernel/flow/instance/' +  data,
    method: 'get'
  })
}

// 获取流程信息
export function getModel(modelId) {
  return request({
    url: '/kernel/flow/models/' +  modelId,
    method: 'get'
  })
}

// 流程发布
export function deployModel(modelId,param) {
  return request({
    url: '/kernel/flow/models/' +  modelId + '/bpmn20/deploy',
    params: param,
    method: 'get'
  })
}

// 保存流程
export function saveModel(data) {
  return request({
    url: '/kernel/flow/models',
    method: 'post',
    data: data
  })
}

// 获取所有可用服务
export function getAllServices() {
  return request({
    url: '/kernel/flow/services',
    method: 'get'
  })
}

// 任务处理按钮
export function handleButtons(nodeId,taskId) {
  return request(`/kernel/flow/form/${nodeId}/buttons/${taskId}`)
}

// 获取节点
export function qryNodeStageList(procDefKey,taskId) {
  return request({
    url: '/kernel/flow/query/node/stage',
    method: 'get',
    params: {
      procDefKey: procDefKey,
      taskId: taskId?taskId:""
    }
  })
}

// 查询规划审核配置列表
export function listRule(query) {
  return request({
    url: '/kernel/flow/rule/list',
    method: 'get',
    params: query
  })
}

export function listAuditRule(query) {
  return request({
    url: '/kernel/flow/rule/audit/list',
    method: 'get',
    params: query
  })
}

export function updateAuditRule(data) {
  return request({
    url: '/kernel/flow/rule/audit',
    method: 'put',
    data: data
  })
}

// 查询规划审核配置详细
export function getRule(id) {
  return request({
    url: '/kernel/flow/rule/' + id,
    method: 'get'
  })
}

// 新增规划审核配置
export function addRule(data) {
  return request({
    url: '/kernel/flow/rule',
    method: 'post',
    data: data
  })
}

// 修改规划审核配置
export function updateRule(data) {
  return request({
    url: '/kernel/flow/rule',
    method: 'put',
    data: data
  })
}

// 修改规划审核配置状态
export function changeStatus(data) {
  return request({
    url: '/kernel/flow/rule/changeStatus',
    method: 'post',
    data: data
  })
}

// 删除规划审核配置
export function delRule(id) {
  return request({
    url: '/kernel/flow/rule/' + id,
    method: 'delete'
  })
}


export function qryNodeStageMsg(params) {
  return request({
    url: '/kernel/flow/rule/qryNodeStageMsg',
    method: 'post',
    data: params
  })
}
