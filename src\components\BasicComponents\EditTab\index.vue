<template>
	<div class='edit-tab'>
		<el-tabs :class='{"move-right": editable}' @tab-remove='removeTab' v-model='currentTabIndex'>
			<el-tab-pane :closable='isCloseAble(tab,tabIndex)' :key='tabIndex' :name='String(tabIndex)' lazy v-for='(tab, tabIndex) in tabs'>
				<span :class='{ "closable-tab": tab.openSite === false,"closed-tab": tab.closed}' slot='label'>
					<el-badge :hidden='!badge || typeof tab.badgeType === "undefined"' :is-dot='tab.badgeType === "dot"' :type='tab.badgeType === "add" ? "success" : "warning"' :value='tab.badgeType === "add" ? "new" : ""'>
            {{getTabTitle(tabIndex, tab)}}
          </el-badge>
					<!-- slot -->
					<slot :index='tabIndex' :tab='tab' />
          <el-tag @click.stop='$emit("recoverTab", tabIndex)' size='mini' style='margin-left: 4px' type='primary' v-if="tab.closed && tab.recover">
            恢复
          </el-tag>
				</span>
      </el-tab-pane>
		</el-tabs>

		<div class='right-tool'>
      <!---所有基带设备都不显示添加按钮-->
			<el-button id="button-add" @click='$emit("addTab")' v-if='getHasShowAddBtn' circle class='tab-add' icon='el-icon-plus' type='primary'/>
		</div>
	</div>
</template>

<script>
import { useTextWidth } from '@/utils/plan/useBusiness'
import store from '@/store'

export default {
	name: 'editTab',
	props: {
		index: {
			type: String,
			default: '0',
		},
		editable: {
			type: Boolean,
			default: true,
		},
		firstTabRemovable: {
			type: Boolean,
			default: false,
		},
    groupMark:{
      type: String,
      default: ''
    },
		tabs: Array,
		tabLabelTitle: String,
    tabTitleNamePrefix: {
      type:String,
      default: ''
    },
		// tabLabelTitle tab的name根据tabs的字段生成
		nameProp: {
			type: String,
			default: 'title',
		},
		namePropData: Array,
		namePropIndex: {
			// 根据nameProp生成的tabTitle是否自动添加序列化下标
			type: Boolean,
			default: true,
		},
		badge: Boolean, // badge标识

		// 新增资源配置相关props
		enableResourceConfig: {
			type: Boolean,
			default: false
		},
		libraryType: {
			type: String,
		},
		resourceType: {
			type: String,
			default: ''
		},
		canAddResource: {
			type: Boolean,
			default: true
		},
		canDeleteResource: {
			type: Boolean,
			default: true
		}

	},
	data() {
		return {
			currentTabIndex: '0',
      hasShowAddBtn: false,
		}
	},
  created() {
    this.updateTabs(this.tabs);
  },
  mounted() {
		// 折叠面板懒加载tab时 无法获取到激活tab宽度 手动设置宽度
		const activeBar = this.$el.getElementsByClassName('el-tabs__active-bar')[0]
		if (activeBar && activeBar.style.width === '0px') {
			this.$nextTick(() => {
				const firstTab = this.$el.querySelector('#tab-0 > span')
				if (firstTab) {
					const text = firstTab.innerText
					activeBar.style.width = useTextWidth(text) + 'px'
				}
			})
		}
	},
	watch: {
		index: {
			handler(newval) {
				this.currentTabIndex = newval
			},
			immediate: true,
		},
		currentTabIndex(newval) {
			this.$emit('update:index', newval)
			this.$emit('changeTabIndex', newval)
		},
    tabs: function(newTabs) {
      this.updateTabs(newTabs);
    }
	},
	methods: {
		removeTab(tabIndex) {
			this.$confirm(
				`此操作将删除【${this.getTabTitle(tabIndex, this.tabs[tabIndex],)}】, 是否继续?`, '删除提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				},
			).then(() => {
        let countTab = 0;
        //如果是基带设备，则判断名称是否有两个，如果有两个则不允许删除
        if(this.groupMark==='baseBand'){
          let tabTitleNameObj = {'BBU': 0,'CU':0,'DU':0};
          this.tabs.forEach((item) => {
            const tabName = this.getNamePropTitle(item);
            tabTitleNameObj[tabName]++;
          })
          countTab = tabTitleNameObj[this.tabs[tabIndex][this.nameProp]];
        }
        //只有countTab数量小于2时可以创建
        this.$emit('removeTab', tabIndex,countTab < 2)
        this.currentTabIndex === tabIndex && +this.currentTabIndex > 0 && (this.currentTabIndex = String(+this.currentTabIndex - 1))
        this.$message({ type: 'success', message: '删除成功!', })
      }).catch((err) => console.error(err))
		},
    //获取tab的标题名称
		getTabTitle(index, tab) {
      let titleName = '';
      //nameProp对应tab中取值key，在取出的值上更改
			if (this.nameProp && tab[this.nameProp]) {
        //基带设备的名称无需要数字，只有BBU，CU,DU
        if(this.groupMark==='baseBand'){
          titleName = this.getNamePropTitle(tab);
        }else{
          titleName = this.getNamePropTitle(tab) + this.getNamePropIndex(index, tab)
        }
      } else {
        titleName = this.tabLabelTitle + (+index + 1)
      }
      return this.tabTitleNamePrefix+titleName;
		},
		getNamePropTitle(tab) {
			if (this.namePropData) {
				const data = this.namePropData.find((i) => i.value === tab[this.nameProp] || i.code === tab[this.nameProp])
				if (data?.name) {
					return data.name
				}
			}
			return tab[this.nameProp]
		},
		getNamePropIndex(index, tab) {
			let propIndex = +index + 1
			if (this.namePropData) {
				const currentPropValue = tab[this.nameProp]
				this.tabs
					.filter((item) => item[this.nameProp] === currentPropValue)
					.forEach((item, idx) => {
						if (item.sort === tab.sort) {
							propIndex = idx + 1
						}
					})
			}
			return this.namePropIndex ? propIndex : ''
		},
    updateTabs(newTabs){
      //基带设备，新选项卡不显示关闭图标
      if(newTabs && newTabs.length > 0 && this.groupMark === 'baseBand'){
        newTabs.forEach((item) => {
          item.modifyType && item.modifyType === 'additions' && (item.closable = false);
          item.modifyType && item.modifyType === 'deletions' && (item.recover = true);
        })
      }
    },
    //是否显示关闭图标
    isCloseAble(tab,tabIndex) {
      let isClose = null;

      // 如果启用了资源配置，优先检查删除权限
      // console.log('资源配置检查', this.enableResourceConfig, this.resourceType, this.canDeleteResource)
      if (this.enableResourceConfig && this.resourceType) {
        // 资源权限控制：只有当canDeleteResource为true时才允许删除
        if (!this.canDeleteResource) {
          return false;
        }
      }

      //入网阶段、施工阶段的机架tab不允许增删
      //editable控制是否显示添加按钮，此处限制是否显示删除图标（机房-机架、天面、平台、基带设备-扩展设备、射频单元-扩展设备）
      if(['SGJD','RWJD'].includes(store.state.plan.currentStage) && ['self','roof','platform','extendDevice_rru','extendDevice_baseBand'].includes(this.groupMark)){
        return false;
      }
      const { nodeId,flowKey }= this.$route.query;
      if (tabIndex === 0) {
        //（1)）当只有一个tab时，不允许删除；（2)）当为基带设备时，不允许删除；
        if((this.tabs && this.tabs.length===1 ) || ['baseBand'].includes(this.groupMark)){
          return false
        }
        // 只在节点为需求库、规划库、设计库,且有添加按钮时显示图标按钮
        if(nodeId && ['jta54ce3bf238a44eab1c6a8ac88a33804','jt3b453adde06a4772b465c4d6fb1ccc4e','jt3ca48544a50e43ae9dfec174d28b6447'].includes(nodeId)){
          const baseResult = this.getHasShowAddBtn;

          // 如果启用了资源配置，需要同时满足原有逻辑和删除权限
          if (this.enableResourceConfig && this.resourceType) {
            return baseResult && this.canDeleteResource;
          }

          return baseResult;
        }
        isClose =  false;//原代码（首个不允许删除)）：this.firstTabRemovable || tab.badgeType === "add";
      }
      //新站
      if(flowKey==='cmcc_5g_plan'){
        const baseResult = this.getHasShowAddBtn;

        // 如果启用了资源配置，需要同时满足原有逻辑和删除权限
        if (this.enableResourceConfig && this.resourceType) {
          return baseResult && this.canDeleteResource;
        }

        return baseResult;
      }

      const baseResult = typeof tab.closable !== "undefined" ? tab.closable : this.editable;

      // 如果启用了资源配置，需要同时满足原有逻辑和删除权限
      if (this.enableResourceConfig && this.resourceType) {
        return baseResult && this.canDeleteResource;
      }

      return baseResult;
    },

	},
  computed:{
    //是否显示TAB添加按钮
    getHasShowAddBtn(){
      let hasAdd = false;

      if (this.groupMark === 'enodeb') {
        console.log('this.canAddResource',this.canAddResource)
      }
      // 如果启用了资源配置，优先使用资源权限控制
      if (this.enableResourceConfig && this.resourceType) {
        // 资源权限控制：只有当canAddResource为true时才显示添加按钮
        if (!this.canAddResource) {
          return false;
        }
      }
      //
      // 施工与入网阶段这几个tab（机房-机架、天面、平台、基带设备-扩展设备、射频单元-扩展设备）不让增删
      if(['self','roof','platform','extendDevice_rru','extendDevice_baseBand'].includes(this.groupMark) && ['SGJD','RWJD'].includes(store.state.plan.currentStage)){
        return false;
      }
      const {nodeId,flowKey} = this.$route.query;
      // console.log('groupmark:',this.groupMark,',nodeId:',nodeId,',flowkey:',flowKey,',editable:',this.editable);

      if(flowKey && flowKey === 'cmcc_5g_plan'){
        //规划——需求库、规划库、设计库显示新增按钮，其它阶段不显示。
        if(nodeId && ['jta54ce3bf238a44eab1c6a8ac88a33804','jt3b453adde06a4772b465c4d6fb1ccc4e','jt3ca48544a50e43ae9dfec174d28b6447'].includes(nodeId)){
          const baseResult = this.groupMark !== 'baseBand';//基带设备所有阶段不可添加；

          // 如果启用了资源配置，需要同时满足原有逻辑和资源权限
          if (this.enableResourceConfig && this.resourceType) {
            return baseResult && this.canAddResource;
          }

          return baseResult;
        }
        return false;
      }

      const baseResult = this.tabs && this.editable && this.groupMark !== 'baseBand';

      // 如果启用了资源配置，需要同时满足原有逻辑和资源权限
      if (this.enableResourceConfig && this.resourceType) {
        return baseResult && this.canAddResource;
      }

      return baseResult;
    },
  }
}
</script>

<style lang='scss' scope>
.edit-tab {
	display: flex;
	.el-tabs {
		width: 100%;
		.el-badge {
			.el-badge__content {
				top: 6px;
			}
		}
	}
	.move-right {
		width: calc(100% - 40px);
	}
	.right-tool {
		width: 40px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		.tab-add {
			padding: 4px;
		}
		&:after {
			content: '';
			display: inline-block;
			width: 100%;
			height: 2px;
			position: absolute;
			bottom: 0;
			background-color: #dfe4ed;
		}
	}

	.closable-tab {
		background-color: #f2f2f2;
		border-radius: 12px 12px 0 0;
		display: inline-block;
		padding: 0 20px;
	}
}
</style>
