import request from '@/utils/request'

export function list(data) {
  return request({
    url: 'cov/gisDatasource/list',
    method: 'post',
    params: data
  })
}

export function save(data) {
  return request({
    data,
    url: 'cov/gisDatasource/save',
    method: 'post'
  })
}

export function deleteSource(data) {
  return request({
    url: 'cov/gisDatasource/delete',
    method: 'delete',
    params: data
  })
}

export function update(data) {
  return request({
    data,
    url: 'cov/gisDatasource/update',
    method: 'put'
  })
}

export function detail(id) {
  return request(`cov/gisDatasource/detail/${id}`)
}

export function tree() {
  return request('cov/gisDatasource/menu')
}

export function tableFields(code) {
  return request(`cov/gisDatasource/tableFields?table=${code}`)
}

export function b3dmInit(data) {
  return request({
    data,
    url: 'cov/b3dm/init',
    method: 'post'
  })
}
