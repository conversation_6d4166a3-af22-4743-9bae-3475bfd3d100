<template>
    <div class="rules-group-container tree box">
      <el-card class="box-card" style="margin-top: 10px">
        <el-row>
          <el-row class="child group-header">
            <el-radio-group v-model="condition.rule" size="small">
              <el-radio-button label="and">且</el-radio-button>
              <el-radio-button label="or">或</el-radio-button>
            </el-radio-group>

            <div v-if="condition.rule == 'and' &&  !rootCondition">
              <el-tag v-if="!condition.editName" @click="handleEditName">{{condition.name}}</el-tag>
              <el-input ref="nameInput" v-preserve-on-blur="nameInputBlurSave" v-else="condition.editName"  style="max-width: 200px;" v-model="condition.name"></el-input>
            </div>

            <div class="right">
              <el-button
                size="small"
                style="margin-left: 10px"
                type="text"
                icon="el-icon-circle-plus-outline"
                @click="addCondition"
                v-if="condition.items.filter(item=>item.type == 'condition').length == 0"
                >新增条件</el-button
              >
              <el-button
                size="small"
                style="margin-left: 10px;color: #67C23A;"
                type="text"
                icon="el-icon-circle-plus-outline"
                @click="addGroup"
                >新增分组</el-button
              >

              <el-popconfirm title="将会删除其级联的所有规则配置!"
                @confirm='deleteGroup()'
                v-if="!rootCondition">
                <el-button
                  size="small" slot="reference"
                  style="margin-left: 10px;color: #E6A23C;"
                  type="text"
                  icon="el-icon-delete"
                  >删除分组</el-button>
              </el-popconfirm>

              <el-button
                style="margin-left: 10px"
                @click="condition.isCollapsed = !condition.isCollapsed"
                size='small'
                type='text'
              >
                {{ condition.isCollapsed ? '折叠' : '展开' }}
                <i
                  :class='"el-icon-arrow-" + (condition.isCollapsed ? "up" : "down")'
                />
              </el-button>
            </div>

          </el-row>

          <template v-if="condition.isCollapsed">
            <template v-for="(item, index) in condition.items">
              <el-row :key="'condition' + index" class="child" v-if="item.type == 'condition'">
                <el-select filterable  v-model="item.fieldType"
                  size="small" style="width: 120px"
                  @change="handleFieldTypeChange($event,item)"
                  placeholder="请选择维度">
                  <el-option :label="ftype.label" :value="ftype.value" :key="ftype.label" v-for="ftype in field.mainTypeOption"> </el-option>
                </el-select>
                <el-select filterable v-model="item.field" size="small"
                  :class="{'yn-rule-input-require': !item.field}"
                  style="margin-left: 10px;width: 180px"
                  @change="changeField($event,item)"
                  placeholder="请选择字段" >
                  <el-option :label="tField.fieldTitle" :value="tField.fieldName" :key="tField.fieldName" v-for="tField in getFieldList(item)">
                    <span style="float: left">{{ tField.fieldTitle }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ tField.fieldName }}</span>
                  </el-option>
                </el-select>
                <el-select v-model="item.operator" size="small"
                  :class="{'yn-rule-input-require': !item.operator}"
                  placeholder="请选择"
                  @change="operatorChange($event,item)"
                  style="margin-left: 10px; width: 120px">
                  <el-option :label="oper.label" :value="oper.value" :key="oper.label" v-for="oper in getOperatorOptions(item)"> </el-option>
                </el-select>
                <template v-if="!['IS_NULL','IS_NOT_NULL'].includes(item.operator)">
                  <el-select v-if="item.formType == 'select'" size="small" clearable filterable  allow-create
                    :class="{'yn-rule-input-require': !item.value || (Array.isArray(item.value) && item.value.length == 0 ) }"
                    :multiple="['~','!~','IN','NOT_IN'].includes(item.operator)" style="width: 250px; margin-left: 10px;" placeholder="请选择或输入值" v-model="item.value">
                    <el-option :label="oper.name" :value="oper.code" :key="oper.name" v-for="oper in getValOptions(item)">
                    </el-option>
                  </el-select>
                  <el-input-number :class="{'yn-rule-input-require': (item.field == null || item.field == undefined || item.field == '')}" style="width: 200px; margin-left: 10px;" :controls="false" v-else-if="item.formType == 'number'" v-model="item.value">

                  </el-input-number>
                  <el-input v-else v-model="item.value" size="small" style="width: 200px; margin-left: 10px;" placeholder="请输入规则"></el-input>
                </template>
                <el-button icon="el-icon-delete" style="margin-left: 5px;color: #E6A23C;" @click="deleteCondition(index)" size="small" v-bind:title="'删除'"></el-button>
                <el-button size="small" style="margin-left: 5px;color: #409EFF;" icon="el-icon-plus" @click="addCondition(index)" v-bind:title="'新增条件'"></el-button>
              </el-row>
              <el-row v-if="item.type == 'group'" :key="'group' + index" class="child">
                <ConditionRule @deleteGroup="()=>deleteGroupCard(index)"
                    :operators="operators"
                    :groupTags="groupTags"
                    :field="field"
                    :condition="item"
                    :tableName="tableName"
                    :engineeringId="engineeringId"
                    ></ConditionRule>
              </el-row>
            </template>
          </template>

        </el-row>
      </el-card>

      <el-dialog v-if="open" title="新增分组" :visible.sync="open"
        width="500px"
        :close-on-click-modal = "false"
        :close-on-press-escape = "false"
        append-to-body>
        <el-form ref="groupForm" :model="groupForm" :rules="rules" label-width="80px">
          <el-form-item label="且/或" prop="rule">
            <el-radio-group v-model="groupForm.rule" size="small">
              <el-radio-button label="and">且</el-radio-button>
              <el-radio-button label="or">或</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分组标签" prop="name" v-if="groupForm.rule == 'and'">
            <el-input v-model="groupForm.name"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitGroup()">保 存</el-button>
          <el-button @click="open = !open">取 消</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>

import { uniqueByField } from '@/utils/common'
import {commonQueryByPost} from "@/api/kernel/query";

export default {
  name: 'ConditionRule',
  props: {
    condition: {
        type: Object,
        default: () => {
            return {
                rule: 'and',
                isCollapsed: true,
                type: 'group',
                items: [{ field: '',fieldType: '' , operator: '=', value: ''}]
            }
        }
    },
    field: {
        type: Object,
    },
    rootCondition: {
      type: Boolean,
      default: false
    },
    tableName:{
      type: String,
    },
    engineeringId: {
      type: [String,Number],
    },
    operators:{
      type: Array,
      default: () => []
    },
    groupTags:{
      type: Array,
      default: () => []
    }
  },
  data() {
     return {
        open:false,
        groupForm:{
        },
        rules:{
          name: [
            { required: false, pattern: '[^ \x20]+', message: "分组标签不能为空", trigger: "blur" }
          ],
        },
        valOptions:{}
     }
  },
  directives: {
    // 自定义指令：失去焦点时如果值为空则保留原值
    preserveOnBlur: {
      bind(el, binding, vnode) {
        // 存储原始值
        const component = vnode.componentInstance;
        el.originalValue = component.value;
        // 绑定失去焦点事件
        component.$on('blur', () => {
          if (!component.value.trim()) {
            component.$emit('input', '');
          }
          // 触发传递的回调函数（如果有）
          if (typeof binding.value === 'function') {
            binding.value(component.value,el.originalValue);
          }
        });
      }
    }
  },
  created() {
  },
  mounted() {
    this.initValOptions()
  },
  methods: {
    handleEditName(){
      this.condition.editName = true
      this.$nextTick(() => {
        this.$refs.nameInput.focus()
      })
    },
    nameInputBlurSave(value,originalValue){
      if(value == originalValue){
        this.condition.editName = false
        return
      }

      if(this.groupTags.includes(value)){
        this.msgInfo("分组标签名称已经存在，不允许重复，请重新输入");
        return
      }

      const index = this.groupTags.indexOf(originalValue)
      if(index > -1){
        this.groupTags.splice(index,1)
      }
      value && this.groupTags.push(value)
      this.condition.editName = false
    },
    getFieldList(item){
      const fields = this.field.fieldList[item.fieldType];
      if(fields) return uniqueByField(fields,'fieldName')
      return []
    },

    getOperatorOptions(item){
      if(!item.field) return []
      const fieldItem = this.getFieldList(item).find(field => field.fieldName == item.field)
      return fieldItem ? this.operators.filter(oper => {
        return !oper.allowTyps || !fieldItem.mappingFieldType || oper.allowTyps.includes(fieldItem.mappingFieldType.toUpperCase())
      } ) : []
    },
    addGroup(){
      this.groupForm = {
        rule: 'and',
        name: ''
      }
      this.open = true

    },
    submitGroup(){
      this.$refs.groupForm.validate((valid) => {
        if (valid) {
          if(this.groupForm.name){
            if(this.groupTags.includes(this.groupForm.name) ){
              this.msgInfo("分组标签名称已经存在，不允许重复，请重新输入");
              return
            }
            this.groupTags.push(this.groupForm.name)
          }

          this.condition.items.push(
          {
              rule: this.groupForm.rule,
              type: 'group',
              editName: false,
              name: this.groupForm.name,
              isCollapsed: true,
              items: [{ field: '',fieldType: '', operator: '=', value: '', type: 'condition'}]
            }
          )
          this.open = false
        }
      })

    },
    addCondition(index){
      this.condition.items.splice(index + 1, 0, {
        type: 'condition',
        fieldType: '',
        field: '',
        operator: '=',
        value: ''
      })
    },
    deleteCondition(index){
      this.condition.items.splice(index, 1)
    },
    deleteGroup(){
      this.$emit('deleteGroup')
    },
    deleteGroupCard(index){
      this.condition.items.splice(index, 1)
    },
    initValOptions(){
      this.condition.items.forEach(item => {
        if(item.type == 'condition'){
          item.formType == 'select' && this.queryValueOptions(item)
        }
      })
    },
    changeField(event,item){
      const fieldItem = this.getFieldList(item).find(field => field.fieldName == event)
      item.fieldTitle = fieldItem.fieldTitle
      item.value = null
      if(fieldItem.mappingFieldType?.toUpperCase() == 'NUMERIC'){
        item.formType = 'number'
      }else{
        item.formType = 'select'
        this.queryValueOptions(item)
      }
    },
    operatorChange(event,item){
      if(['IN','NOT_IN'].includes(event)){
        this.$set(item,'value',[])
      }else{
        this.$set(item,'value','')
      }
    },
    queryValueOptions(item){
      commonQueryByPost("qryYnDataCenter_DataEnum",{
        tableName: this.tableName,
        key: item.field,
        projectId: this.engineeringId,
      }).then(res=>{
        this.$set(this.valOptions,item.field,res.data)
      })
    },
    getValOptions(item){
      return this.valOptions[item.field]
    },
    handleFieldTypeChange(value,item){
      const selectedItem = this.field.mainTypeOption.find(type => type.value === value);
      item.fieldTypeName = selectedItem ? selectedItem.label : '';
      item.field = null
      item.value = null
    }
  },
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;

  ::v-deep .el-card__body {
    padding: 10px 15px 10px 10px !important;
  }

  .group-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
/* 只需要左边边框线 */
.child {
  width: 100%;
  position: relative;
  border: 1px solid #d9d9d9;
  border-style: none none none solid;
  padding: 10px 0;
  padding-left: 12px;
}
/* 设置一个伪元素宽2px 高50% 用于遮挡多余的左边框线 */
.child::before {
  display: block;
  content: '';
  position: absolute;
  background-color: white;
  width: 1px;
  height: 50%;
}
/* 设置第一个子元素的伪类定位 */
.box .child:first-child::before {
  left: -1px;
  top: 0;
}
/* 设置第二个子元素的伪类定位 */
.box .child:last-child::before {
  left: -1px;
  bottom: 0;
}
/* 设置子元素的横线，定位在高度的50% */
.box .child::after {
  top: 50%;
  left: 0;
  position: absolute;
  content: '';
  display: block;
  width: 10px;
  height: 1px;
  border: 1px solid #d9d9d9;
  border-style: solid none none none;
}

.yn-rule-input-require {
  ::v-deep{
    .el-input__inner{
      border-color: red !important;

    }
  }
}
</style>
