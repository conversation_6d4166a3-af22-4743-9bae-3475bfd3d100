<template>
  <div class="test-container">
    <h2>BasicForm 动态功能全面测试</h2>

    <!-- 测试分类标签 -->
    <el-tabs v-model="activeTab" type="card">
      <!-- 必填字段测试 -->
      <el-tab-pane label="必填字段测试" name="required">
        <div class="test-section">
          <h3>条件必填字段测试</h3>
          <basic-form
            :fields="requiredTestFields"
            :form-value="requiredFormValue"
            @returnFormValue="handleRequiredFormChange"
            ref="requiredForm"
          />
          <div class="control-buttons">
            <el-button @click="validateRequiredForm">验证表单</el-button>
            <el-button @click="resetRequiredForm">重置表单</el-button>
          </div>
          <div class="test-info">
            <h4>测试说明:</h4>
            <ul>
              <li>选择"需要验证": 验证码字段变为必填(红色*)</li>
              <li>选择"VIP用户": 推荐人字段变为必填(红色*)</li>
              <li>选择"企业用户": 公司名称和联系人都变为必填(红色*)</li>
            </ul>
            <p><strong>当前状态:</strong> {{ JSON.stringify(requiredFormValue, null, 2) }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 显示隐藏测试 -->
      <el-tab-pane label="显示隐藏测试" name="visibility">
        <div class="test-section">
          <h3>条件显示隐藏测试</h3>
          <basic-form
            :fields="visibilityTestFields"
            :form-value="visibilityFormValue"
            @returnFormValue="handleVisibilityFormChange"
            ref="visibilityForm"
          />
          <div class="control-buttons">
            <el-button @click="resetVisibilityForm">重置表单</el-button>
          </div>
          <div class="test-info">
            <h4>测试说明:</h4>
            <ul>
              <li>选择"在线支付": 显示支付方式选择</li>
              <li>选择"银行转账": 显示银行信息字段</li>
              <li>选择"货到付款": 隐藏所有支付相关字段</li>
              <li>启用"需要发票": 显示发票信息字段</li>
            </ul>
            <p><strong>当前状态:</strong> {{ JSON.stringify(visibilityFormValue, null, 2) }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 字段类型测试 -->
      <el-tab-pane label="字段类型测试" name="type">
        <div class="test-section">
          <h3>动态字段类型测试</h3>
          <basic-form
            :fields="typeTestFields"
            :form-value="typeFormValue"
            @returnFormValue="handleTypeFormChange"
            ref="typeForm"
          />
          <div class="control-buttons">
            <el-button @click="resetTypeForm">重置表单</el-button>
          </div>
          <div class="test-info">
            <h4>测试说明:</h4>
            <ul>
              <li>选择"文本输入": 内容字段变为普通输入框</li>
              <li>选择"多行文本": 内容字段变为文本域</li>
              <li>选择"数字输入": 内容字段变为数字输入框</li>
              <li>选择"日期选择": 内容字段变为日期选择器</li>
            </ul>
            <p><strong>当前状态:</strong> {{ JSON.stringify(typeFormValue, null, 2) }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 级联选择测试 -->
      <el-tab-pane label="级联选择测试" name="cascade">
        <div class="test-section">
          <h3>级联选择测试</h3>
          <basic-form
            :fields="cascadeTestFields"
            :form-value="cascadeFormValue"
            @returnFormValue="handleCascadeFormChange"
            ref="cascadeForm"
          />
          <div class="control-buttons">
            <el-button @click="resetCascadeForm">重置表单</el-button>
          </div>
          <div class="test-info">
            <h4>测试说明:</h4>
            <ul>
              <li>选择省份后，城市选项会动态更新（测试防抖和缓存）</li>
              <li>选择城市后，区县选项会动态更新（测试级联清空）</li>
              <li>选择行业后，职位选项会动态更新（测试异步加载）</li>
              <li>快速切换选项测试防抖效果</li>
              <li>重复选择相同选项测试缓存效果</li>
            </ul>
            <div class="performance-info">
              <h5>性能监控:</h5>
              <p>级联请求次数: {{ cascadeRequestCount }}</p>
              <p>缓存命中次数: {{ cacheHitCount }}</p>
              <p>当前加载状态: {{ loadingFields.join(', ') || '无' }}</p>
            </div>
            <p><strong>当前状态:</strong> {{ JSON.stringify(cascadeFormValue, null, 2) }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 综合测试 -->
      <el-tab-pane label="综合测试" name="comprehensive">
        <div class="test-section">
          <h3>综合功能测试</h3>
          <basic-form
            :fields="comprehensiveTestFields"
            :form-value="comprehensiveFormValue"
            @returnFormValue="handleComprehensiveFormChange"
            ref="comprehensiveForm"
          />
          <div class="control-buttons">
            <el-button @click="validateComprehensiveForm">验证表单</el-button>
            <el-button @click="resetComprehensiveForm">重置表单</el-button>
          </div>
          <div class="test-info">
            <h4>测试说明:</h4>
            <ul>
              <li>选择不同用户类型会影响多个字段的必填、显示隐藏状态</li>
              <li>选择不同业务类型会改变字段类型和选项</li>
              <li>测试复杂的多字段联动效果</li>
            </ul>
            <p><strong>当前状态:</strong> {{ JSON.stringify(comprehensiveFormValue, null, 2) }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 初始化状态检查 -->
      <el-tab-pane label="初始化检查" name="initialization">
        <div class="test-section">
          <h3>初始化状态检查</h3>
          <div class="control-buttons">
            <el-button @click="checkInitializationStatus">检查初始化状态</el-button>
            <el-button @click="triggerManualInitialization">手动触发初始化</el-button>
            <el-button @click="clearInitializationLog">清空日志</el-button>
          </div>

          <div class="initialization-status">
            <h4>初始化状态报告:</h4>
            <div class="status-grid">
              <div class="status-item" v-for="(status, formName) in initializationStatus" :key="formName">
                <h5>{{ formName }}</h5>
                <div class="status-details">
                  <p><span class="label">必填字段:</span> {{ status.requiredFields.join(', ') || '无' }}</p>
                  <p><span class="label">隐藏字段:</span> {{ status.hiddenFields.join(', ') || '无' }}</p>
                  <p><span class="label">级联字段:</span> {{ status.cascadeFields.join(', ') || '无' }}</p>
                  <p><span class="label">条件字段:</span> {{ status.conditionalFields.join(', ') || '无' }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="initialization-log">
            <h4>初始化日志:</h4>
            <div class="log-container">
              <div v-for="(log, index) in initializationLogs" :key="index" class="log-item">
                <span class="log-time">{{ log.time }}</span>
                <span class="log-level" :class="log.level">{{ log.level }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BasicForm from './index.vue'

export default {
  name: 'TestRequiredUpdate',
  components: {
    BasicForm
  },
  data() {
    return {
      activeTab: 'required',

      // 必填字段测试数据
      requiredFormValue: {
        userType: '普通用户',
        username: '',
        email: '',
        verifyCode: '',
        referrer: '',
        companyName: '',
        contactPerson: ''
      },
      // 显示隐藏测试数据
      visibilityFormValue: {
        paymentMethod: '在线支付',
        paymentType: '',
        bankName: '',
        accountNumber: '',
        needInvoice: false,
        invoiceTitle: '',
        taxNumber: ''
      },

      // 字段类型测试数据
      typeFormValue: {
        inputType: '文本输入',
        content: ''
      },

      // 级联选择测试数据
      cascadeFormValue: {
        province: '',
        city: '',
        district: '',
        industry: '',
        position: ''
      },

      // 综合测试数据
      comprehensiveFormValue: {
        userType: '个人用户',
        businessType: '标准业务',
        name: '',
        phone: '',
        email: '',
        companyName: '',
        businessLicense: '',
        specialRequirement: ''
      },

      // 性能监控数据
      cascadeRequestCount: 0,
      cacheHitCount: 0,
      loadingFields: [],

      // 初始化状态检查数据
      initializationStatus: {},
      initializationLogs: [],

      // 必填字段测试配置
      requiredTestFields: [
        {
          name: 'userType',
          title: '用户类型',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '普通用户', value: '普通用户' },
            { name: '需要验证', value: '需要验证' },
            { name: 'VIP用户', value: 'VIP用户' },
            { name: '企业用户', value: '企业用户' }
          ]
        },
        {
          name: 'username',
          title: '用户名',
          formType: 'input',
          required: true,
          formEdit: true,
          show: true,
          initHide: false
        },
        {
          name: 'email',
          title: '邮箱',
          formType: 'input',
          required: true,
          formEdit: true,
          show: true,
          initHide: false
        },
        {
          name: 'verifyCode',
          title: '验证码',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '需要验证'",
            tipMsg: '需要验证用户必须填写验证码'
          })
        },
        {
          name: 'referrer',
          title: '推荐人',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.userType === 'VIP用户'",
            tipMsg: 'VIP用户必须填写推荐人'
          })
        },
        {
          name: 'companyName',
          title: '公司名称',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '企业用户'",
            tipMsg: '企业用户必须填写公司名称'
          })
        },
        {
          name: 'contactPerson',
          title: '联系人',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '企业用户'",
            tipMsg: '企业用户必须填写联系人'
          })
        }
      ],

      // 显示隐藏测试配置
      visibilityTestFields: [
        {
          name: 'paymentMethod',
          title: '支付方式',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '在线支付', value: '在线支付' },
            { name: '银行转账', value: '银行转账' },
            { name: '货到付款', value: '货到付款' }
          ],
          param2: JSON.stringify({
            '在线支付': { show: 'paymentType', hide: 'bankName,accountNumber' },
            '银行转账': { show: 'bankName,accountNumber', hide: 'paymentType' },
            '货到付款': { hide: 'paymentType,bankName,accountNumber' }
          })
        },
        {
          name: 'paymentType',
          title: '支付类型',
          formType: 'select',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '支付宝', value: '支付宝' },
            { name: '微信支付', value: '微信支付' },
            { name: '信用卡', value: '信用卡' }
          ]
        },
        {
          name: 'bankName',
          title: '银行名称',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true
        },
        {
          name: 'accountNumber',
          title: '账户号码',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true
        },
        {
          name: 'needInvoice',
          title: '需要发票',
          formType: 'switch',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          param2: JSON.stringify({
            'true': { show: 'invoiceTitle,taxNumber' },
            'false': { hide: 'invoiceTitle,taxNumber' }
          })
        },
        {
          name: 'invoiceTitle',
          title: '发票抬头',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true
        },
        {
          name: 'taxNumber',
          title: '税号',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true
        }
      ],

      // 字段类型测试配置
      typeTestFields: [
        {
          name: 'inputType',
          title: '输入类型',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '文本输入', value: '文本输入' },
            { name: '多行文本', value: '多行文本' },
            { name: '数字输入', value: '数字输入' },
            { name: '日期选择', value: '日期选择' }
          ],
          param3: JSON.stringify({
            '文本输入': [{ name: 'content', formType: 'input' }],
            '多行文本': [{ name: 'content', formType: 'textarea' }],
            '数字输入': [{ name: 'content', formType: 'number' }],
            '日期选择': [{ name: 'content', formType: 'date' }]
          })
        },
        {
          name: 'content',
          title: '内容',
          formType: 'input',
          required: true,
          formEdit: true,
          show: true,
          initHide: false
        }
      ],

      // 级联选择测试配置
      cascadeTestFields: [
        {
          name: 'province',
          title: '省份',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '北京市', value: 'beijing' },
            { name: '上海市', value: 'shanghai' },
            { name: '广东省', value: 'guangdong' },
            { name: '江苏省', value: 'jiangsu' }
          ]
        },
        {
          name: 'city',
          title: '城市',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          cascadeCode: 'province',
          children: []
        },
        {
          name: 'district',
          title: '区县',
          formType: 'select',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          cascadeCode: 'city',
          children: []
        },
        {
          name: 'industry',
          title: '行业',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: 'IT互联网', value: 'it' },
            { name: '金融', value: 'finance' },
            { name: '教育', value: 'education' },
            { name: '制造业', value: 'manufacturing' }
          ]
        },
        {
          name: 'position',
          title: '职位',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          cascadeCode: 'industry',
          children: []
        }
      ],

      // 综合测试配置
      comprehensiveTestFields: [
        {
          name: 'userType',
          title: '用户类型',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '个人用户', value: '个人用户' },
            { name: '企业用户', value: '企业用户' },
            { name: 'VIP用户', value: 'VIP用户' }
          ],
          param2: JSON.stringify({
            '个人用户': { hide: 'companyName,businessLicense', show: 'name,phone' },
            '企业用户': { show: 'companyName,businessLicense', hide: 'name' },
            'VIP用户': { show: 'name,phone,specialRequirement', hide: 'companyName,businessLicense' }
          })
        },
        {
          name: 'businessType',
          title: '业务类型',
          formType: 'select',
          required: true,
          formEdit: true,
          show: true,
          initHide: false,
          children: [
            { name: '标准业务', value: '标准业务' },
            { name: '定制业务', value: '定制业务' },
            { name: '紧急业务', value: '紧急业务' }
          ],
          param3: JSON.stringify({
            '标准业务': [{ name: 'specialRequirement', formType: 'input' }],
            '定制业务': [{ name: 'specialRequirement', formType: 'textarea' }],
            '紧急业务': [{ name: 'specialRequirement', formType: 'textarea' }]
          })
        },
        {
          name: 'name',
          title: '姓名',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '个人用户' || data.userType === 'VIP用户'",
            tipMsg: '个人用户和VIP用户必须填写姓名'
          })
        },
        {
          name: 'phone',
          title: '手机号',
          formType: 'input',
          required: true,
          formEdit: true,
          show: true,
          initHide: false
        },
        {
          name: 'email',
          title: '邮箱',
          formType: 'input',
          required: true,
          formEdit: true,
          show: true,
          initHide: false
        },
        {
          name: 'companyName',
          title: '公司名称',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '企业用户'",
            tipMsg: '企业用户必须填写公司名称'
          })
        },
        {
          name: 'businessLicense',
          title: '营业执照号',
          formType: 'input',
          required: false,
          formEdit: true,
          show: false,
          initHide: true,
          conditionRequired: JSON.stringify({
            rule: "data.userType === '企业用户'",
            tipMsg: '企业用户必须填写营业执照号'
          })
        },
        {
          name: 'specialRequirement',
          title: '特殊需求',
          formType: 'input',
          required: false,
          formEdit: true,
          show: true,
          initHide: false,
          conditionRequired: JSON.stringify({
            rule: "data.businessType === '定制业务' || data.businessType === '紧急业务'",
            tipMsg: '定制业务和紧急业务必须填写特殊需求'
          })
        }
      ]
    }
  },
  computed: {
    // 模拟级联数据
    cityOptions() {
      const cityMap = {
        'beijing': [
          { name: '东城区', value: 'dongcheng' },
          { name: '西城区', value: 'xicheng' },
          { name: '朝阳区', value: 'chaoyang' }
        ],
        'shanghai': [
          { name: '黄浦区', value: 'huangpu' },
          { name: '徐汇区', value: 'xuhui' },
          { name: '长宁区', value: 'changning' }
        ],
        'guangdong': [
          { name: '广州市', value: 'guangzhou' },
          { name: '深圳市', value: 'shenzhen' },
          { name: '珠海市', value: 'zhuhai' }
        ],
        'jiangsu': [
          { name: '南京市', value: 'nanjing' },
          { name: '苏州市', value: 'suzhou' },
          { name: '无锡市', value: 'wuxi' }
        ]
      }
      return cityMap[this.cascadeFormValue.province] || []
    },

    districtOptions() {
      const districtMap = {
        'dongcheng': [{ name: '东华门街道', value: 'donghuamen' }],
        'xicheng': [{ name: '西长安街街道', value: 'xichanganjie' }],
        'chaoyang': [{ name: '建国门外街道', value: 'jianguomenwai' }],
        'guangzhou': [{ name: '越秀区', value: 'yuexiu' }],
        'shenzhen': [{ name: '福田区', value: 'futian' }]
      }
      return districtMap[this.cascadeFormValue.city] || []
    },

    positionOptions() {
      const positionMap = {
        'it': [
          { name: '前端工程师', value: 'frontend' },
          { name: '后端工程师', value: 'backend' },
          { name: '产品经理', value: 'pm' }
        ],
        'finance': [
          { name: '投资顾问', value: 'advisor' },
          { name: '风控专员', value: 'risk' },
          { name: '财务分析师', value: 'analyst' }
        ],
        'education': [
          { name: '教师', value: 'teacher' },
          { name: '教研员', value: 'researcher' },
          { name: '教务管理', value: 'admin' }
        ],
        'manufacturing': [
          { name: '工程师', value: 'engineer' },
          { name: '质检员', value: 'qc' },
          { name: '生产主管', value: 'supervisor' }
        ]
      }
      return positionMap[this.cascadeFormValue.industry] || []
    }
  },
  watch: {
    // 监听级联选择变化
    'cascadeFormValue.province'(newVal) {
      if (newVal) {
        this.simulateCascadeRequest('province', 'city')
        // 更新城市选项
        const cityField = this.cascadeTestFields.find(f => f.name === 'city')
        if (cityField) {
          this.$set(cityField, 'children', this.cityOptions)
          this.cascadeFormValue.city = ''
          this.cascadeFormValue.district = ''
        }
      } else {
        // 清空级联字段
        const cityField = this.cascadeTestFields.find(f => f.name === 'city')
        const districtField = this.cascadeTestFields.find(f => f.name === 'district')
        if (cityField) this.$set(cityField, 'children', [])
        if (districtField) this.$set(districtField, 'children', [])
      }
    },

    'cascadeFormValue.city'(newVal) {
      if (newVal) {
        this.simulateCascadeRequest('city', 'district')
        // 更新区县选项
        const districtField = this.cascadeTestFields.find(f => f.name === 'district')
        if (districtField) {
          this.$set(districtField, 'children', this.districtOptions)
          this.cascadeFormValue.district = ''
        }
      } else {
        // 清空区县选项
        const districtField = this.cascadeTestFields.find(f => f.name === 'district')
        if (districtField) this.$set(districtField, 'children', [])
      }
    },

    'cascadeFormValue.industry'(newVal) {
      if (newVal) {
        this.simulateCascadeRequest('industry', 'position')
        // 更新职位选项
        const positionField = this.cascadeTestFields.find(f => f.name === 'position')
        if (positionField) {
          this.$set(positionField, 'children', this.positionOptions)
          this.cascadeFormValue.position = ''
        }
      } else {
        // 清空职位选项
        const positionField = this.cascadeTestFields.find(f => f.name === 'position')
        if (positionField) this.$set(positionField, 'children', [])
      }
    }
  },

  methods: {
    // 必填字段测试方法
    handleRequiredFormChange(formData) {
      console.log('必填字段测试数据变化:', formData)
      this.requiredFormValue = { ...this.requiredFormValue, ...formData }
    },

    validateRequiredForm() {
      const isValid = this.$refs.requiredForm.validateForm()
      this.$message({
        type: isValid === true ? 'success' : 'error',
        message: isValid === true ? '必填字段验证通过' : '必填字段验证失败'
      })
      console.log('必填字段验证结果:', isValid)
    },

    resetRequiredForm() {
      this.$refs.requiredForm.resetForm()
      this.requiredFormValue = {
        userType: '普通用户',
        username: '',
        email: '',
        verifyCode: '',
        referrer: '',
        companyName: '',
        contactPerson: ''
      }
    },

    // 显示隐藏测试方法
    handleVisibilityFormChange(formData) {
      console.log('显示隐藏测试数据变化:', formData)
      this.visibilityFormValue = { ...this.visibilityFormValue, ...formData }
    },

    resetVisibilityForm() {
      this.$refs.visibilityForm.resetForm()
      this.visibilityFormValue = {
        paymentMethod: '在线支付',
        paymentType: '',
        bankName: '',
        accountNumber: '',
        needInvoice: false,
        invoiceTitle: '',
        taxNumber: ''
      }
    },

    // 字段类型测试方法
    handleTypeFormChange(formData) {
      console.log('字段类型测试数据变化:', formData)
      this.typeFormValue = { ...this.typeFormValue, ...formData }
    },

    resetTypeForm() {
      this.$refs.typeForm.resetForm()
      this.typeFormValue = {
        inputType: '文本输入',
        content: ''
      }
    },

    // 级联选择测试方法
    handleCascadeFormChange(formData) {
      console.log('级联选择测试数据变化:', formData)
      this.cascadeFormValue = { ...this.cascadeFormValue, ...formData }
    },

    resetCascadeForm() {
      this.$refs.cascadeForm.resetForm()
      this.cascadeFormValue = {
        province: '',
        city: '',
        district: '',
        industry: '',
        position: ''
      }
    },

    // 综合测试方法
    handleComprehensiveFormChange(formData) {
      console.log('综合测试数据变化:', formData)
      this.comprehensiveFormValue = { ...this.comprehensiveFormValue, ...formData }
    },

    validateComprehensiveForm() {
      const isValid = this.$refs.comprehensiveForm.validateForm()
      this.$message({
        type: isValid === true ? 'success' : 'error',
        message: isValid === true ? '综合测试验证通过' : '综合测试验证失败'
      })
      console.log('综合测试验证结果:', isValid)
    },

    resetComprehensiveForm() {
      this.$refs.comprehensiveForm.resetForm()
      this.comprehensiveFormValue = {
        userType: '个人用户',
        businessType: '标准业务',
        name: '',
        phone: '',
        email: '',
        companyName: '',
        businessLicense: '',
        specialRequirement: ''
      }
    },

    // 模拟级联请求性能监控
    simulateCascadeRequest(parentField, childField) {
      const cacheKey = `${parentField}_${this.cascadeFormValue[parentField]}`

      // 模拟加载状态
      if (!this.loadingFields.includes(childField)) {
        this.loadingFields.push(childField)

        setTimeout(() => {
          this.loadingFields = this.loadingFields.filter(f => f !== childField)
        }, 300) // 模拟300ms加载时间
      }

      // 检查缓存
      if (this.cascadeCache && this.cascadeCache[cacheKey]) {
        this.cacheHitCount++
        console.log(`缓存命中: ${cacheKey}`)
      } else {
        this.cascadeRequestCount++
        console.log(`发起请求: ${cacheKey}`)

        // 模拟缓存
        if (!this.cascadeCache) {
          this.cascadeCache = {}
        }
        this.cascadeCache[cacheKey] = true
      }
    },

    // 重置性能监控数据
    resetPerformanceData() {
      this.cascadeRequestCount = 0
      this.cacheHitCount = 0
      this.loadingFields = []
      this.cascadeCache = {}
    },

    // 初始化字段状态
    initializeFieldStates() {
      // 确保所有字段都有正确的初始状态
      this.$nextTick(() => {
        // 初始化必填字段测试的状态
        if (this.$refs.requiredForm) {
          this.$refs.requiredForm.updateRequiredStatus()
        }

        // 初始化显示隐藏测试的状态
        if (this.$refs.visibilityForm) {
          this.$refs.visibilityForm.updateRequiredStatus()
        }

        // 初始化字段类型测试的状态
        if (this.$refs.typeForm) {
          this.$refs.typeForm.updateRequiredStatus()
        }

        // 初始化级联选择测试的状态
        if (this.$refs.cascadeForm) {
          this.$refs.cascadeForm.updateRequiredStatus()
        }

        // 初始化综合测试的状态
        if (this.$refs.comprehensiveForm) {
          this.$refs.comprehensiveForm.updateRequiredStatus()
        }

        // 检查初始化状态
        this.checkInitializationStatus()
      })
    },

    // 检查初始化状态
    checkInitializationStatus() {
      this.addLog('INFO', '开始检查初始化状态')

      const forms = {
        '必填字段测试': this.$refs.requiredForm,
        '显示隐藏测试': this.$refs.visibilityForm,
        '字段类型测试': this.$refs.typeForm,
        '级联选择测试': this.$refs.cascadeForm,
        '综合测试': this.$refs.comprehensiveForm
      }

      this.initializationStatus = {}

      Object.keys(forms).forEach(formName => {
        const formRef = forms[formName]
        if (formRef && formRef.fields) {
          const status = this.analyzeFormStatus(formRef)
          this.initializationStatus[formName] = status
          this.addLog('INFO', `${formName} 状态分析完成`)
        }
      })

      this.addLog('SUCCESS', '初始化状态检查完成')
    },

    // 分析表单状态
    analyzeFormStatus(formRef) {
      const status = {
        requiredFields: [],
        hiddenFields: [],
        cascadeFields: [],
        conditionalFields: []
      }

      formRef.fields.forEach(field => {
        // 检查必填字段
        if (field.required || (formRef.computedFieldsRequired && formRef.computedFieldsRequired[field.name])) {
          status.requiredFields.push(field.name)
        }

        // 检查隐藏字段
        if (field.initHide || !formRef.controlShowField(field)) {
          status.hiddenFields.push(field.name)
        }

        // 检查级联字段
        if (field.cascadeCode) {
          status.cascadeFields.push(`${field.name}(依赖:${field.cascadeCode})`)
        }

        // 检查条件字段
        if (field.conditionRequired || field.param2 || field.param3) {
          const conditions = []
          if (field.conditionRequired) conditions.push('必填')
          if (field.param2) conditions.push('显示')
          if (field.param3) conditions.push('类型')
          status.conditionalFields.push(`${field.name}(${conditions.join(',')})`)
        }
      })

      return status
    },

    // 手动触发初始化
    triggerManualInitialization() {
      this.addLog('INFO', '手动触发初始化逻辑')

      const forms = [
        this.$refs.requiredForm,
        this.$refs.visibilityForm,
        this.$refs.typeForm,
        this.$refs.cascadeForm,
        this.$refs.comprehensiveForm
      ]

      forms.forEach(formRef => {
        if (formRef && formRef.triggerAllInitialLogic) {
          formRef.triggerAllInitialLogic()
          this.addLog('SUCCESS', '表单初始化逻辑触发完成')
        }
      })

      // 重新检查状态
      this.$nextTick(() => {
        this.checkInitializationStatus()
      })
    },

    // 添加日志
    addLog(level, message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

      this.initializationLogs.unshift({
        time,
        level,
        message
      })

      // 限制日志数量
      if (this.initializationLogs.length > 50) {
        this.initializationLogs = this.initializationLogs.slice(0, 50)
      }
    },

    // 清空初始化日志
    clearInitializationLog() {
      this.initializationLogs = []
      this.addLog('INFO', '日志已清空')
    }
  },

  mounted() {
    // 组件挂载后初始化字段状态
    this.initializeFieldStates()
  }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.test-section {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #409EFF;
  margin-bottom: 20px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 10px;
}

.control-buttons {
  margin: 20px 0;
  text-align: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.control-buttons .el-button {
  margin: 0 10px;
}

.test-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.test-info h4 {
  margin-top: 0;
  color: #333;
  font-size: 16px;
}

.test-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-info li {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.test-info p {
  margin: 10px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-info strong {
  color: #409EFF;
}

/* 性能监控样式 */
.performance-info {
  margin: 15px 0;
  padding: 12px;
  background-color: #f0f9ff;
  border-left: 4px solid #67C23A;
  border-radius: 4px;
}

.performance-info h5 {
  margin: 0 0 8px 0;
  color: #67C23A;
  font-size: 14px;
  font-weight: 600;
}

.performance-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #333;
  background: none;
  border: none;
  padding: 0;
}

.performance-info p:last-child {
  margin-bottom: 0;
}

/* 初始化状态检查样式 */
.initialization-status {
  margin: 20px 0;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.status-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.status-item h5 {
  margin: 0 0 10px 0;
  color: #409EFF;
  font-size: 14px;
  font-weight: 600;
}

.status-details p {
  margin: 5px 0;
  font-size: 12px;
  line-height: 1.4;
}

.status-details .label {
  font-weight: 600;
  color: #333;
  min-width: 70px;
  display: inline-block;
}

.initialization-log {
  margin-top: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #1e1e1e;
  border-radius: 6px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  line-height: 1.4;
}

.log-time {
  color: #888;
  margin-right: 10px;
  min-width: 60px;
}

.log-level {
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

.log-level.INFO {
  background-color: #409EFF;
  color: white;
}

.log-level.SUCCESS {
  background-color: #67C23A;
  color: white;
}

.log-level.WARN {
  background-color: #E6A23C;
  color: white;
}

.log-level.ERROR {
  background-color: #F56C6C;
  color: white;
}

.log-message {
  color: #fff;
  flex: 1;
}

/* 标签页样式优化 */
::v-deep .el-tabs__header {
  margin-bottom: 20px;
}

::v-deep .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
}

::v-deep .el-tabs__item.is-active {
  color: #409EFF;
}

/* 表单样式优化 */
::v-deep .el-form {
  background-color: #fafbfc;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-container {
    padding: 10px;
  }

  .test-section {
    padding: 15px;
  }

  .control-buttons .el-button {
    margin: 5px;
    display: block;
    width: 100%;
  }
}
</style>
