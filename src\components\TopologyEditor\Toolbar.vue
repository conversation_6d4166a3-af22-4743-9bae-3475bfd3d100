<template>
  <div class="topology-toolbar">
    <el-button-group>
      <el-button icon="el-icon-refresh-left" :disabled="!canUndo" @click="$emit('undo')">撤销</el-button>
      <el-button icon="el-icon-refresh-right" :disabled="!canRedo" @click="$emit('redo')">重做</el-button>
      <el-button icon="el-icon-zoom-in" @click="$emit('zoom-in')">放大</el-button>
      <el-button icon="el-icon-zoom-out" @click="$emit('zoom-out')">缩小</el-button>
      <el-button icon="el-icon-sort" @click="$emit('auto-layout')">自动布局</el-button>
      <el-button icon="el-icon-upload" @click="$emit('import-json')">导入</el-button>
      <el-button icon="el-icon-download" @click="$emit('export-json')">导出</el-button>
      <el-button icon="el-icon-plus" @click="$emit('add-node')">添加节点</el-button>
      <el-button icon="el-icon-delete" @click="$emit('delete-selected')">删除选中</el-button>
    </el-button-group>
  </div>
</template>

<script>
export default {
  name: 'Toolbar',
  props: {
    canUndo: { type: Boolean, default: false },
    canRedo: { type: Boolean, default: false }
  }
}
</script>

<style scoped>
.topology-toolbar {
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  z-index: 2;
}
</style>
