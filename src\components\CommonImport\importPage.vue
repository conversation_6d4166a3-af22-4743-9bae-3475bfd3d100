<template>
    <div style="display: inline-block;">
       <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="close"
        title="数据导入"
        :visible.sync="openView"
        width="80%"
      >
        <el-steps :active="activeStep" align-center finish-status="success">
            <el-step title="数据文件"></el-step>
            <el-step title="数据导入"></el-step>
        </el-steps>

        <div v-show="activeStep == 1">
            <div class="step-top">
              <p v-if="showDownloadTemplate">请选择有数据的标准行列表格文件 ，文件内容请查看
                <el-link type="primary" @click="downLoadTemplate()"> 示例模版</el-link>
              </p>
              <div>
                <el-link @click="qryImportRecord" type="primary">导入历史</el-link>
              </div>
            </div>
                
            <p>导入过程会在后台执行，离开导入数据页面不受影响。</p>  
            
            <el-upload ref="upload"
                class="upload-demo"
                :drag="fileList.length == 0"
                :limit="1"
                :disabled="fileList.length > 0"
                :auto-upload="false"
                :file-list="fileList"
                :data='extendParam'
                :headers='headers'
                :accept='accept'
                :on-progress="handleProgress"
                :before-upload='handleBeforeUpload'
                :on-change="handleChange"
                :on-remove="handleRemove"
                :on-success="handleSuccess"
                :show-file-list="false"
                :action="uploadFileUrl"
            >
              <template v-if="fileList.length === 0">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </template>

              <!-- 上传后显示文件信息 -->
              <template v-else>
                <div class="custom-file-display">
                  <div style="display: flex;justify-content: flex-end;">                
                    <el-link @click.stop="handleRemoveFile(fileList[0])" type="danger">删除</el-link>
                  </div>
                  <div class="file-info">
                    <i class="el-icon-document" style="font-size: 100px;"></i>
                    <span class="file-name" style="font-size: 20px;">{{ fileList[0].name }}</span>
                  </div>
                </div>
              </template>
            </el-upload>
         </div>

         <div v-if="activeStep == 2 " class="progress-content">
            <div class="header" style="padding: 20px;">
              文件上传进度:<el-progress :percentage="uploadProgress" />
            </div>
            <div class="header" style="padding: 20px;">
              数据导入进度：
              <el-progress :percentage="importProgress" />
            </div>
            <div v-if="importDetails && importDetails.length" class="header" ref="messageContainer" style="height: 150px;overflow-y: auto;">
                <p v-for="item in importDetails">
                {{ item.time }}：{{ item.message }}
              </p>
              </div>
            <div class="content" style="padding: 20px;" v-if="importRes.completed && importRes.status == 'success' ">
              <div class="info-box">
                <div class="item">
                  <i class="el-icon-s-order" style="font-size: 30px;color: blue;"></i>
                  <div class="data">
                    <div class="title">数据总数</div>
                    <div class="total">{{importDataInfo.readRowCount}}</div>
                  </div>
                </div>
                <div class="item">
                  <i class="el-icon-success" style="font-size: 30px;color: green;"></i>
                  <div class="data">
                    <div class="title">新增数据</div>
                    <div class="total">{{importDataInfo.insertCount}}</div>
                  </div>
                </div>
                <div class="item">
                  <i class="el-icon-success" style="font-size: 30px;color: green;"></i>
                  <div class="data">
                    <div class="title">更新数据</div>
                    <div class="total">{{importDataInfo.updateCount}}</div>
                  </div>
                </div>
                <div class="item">
                  <i class="el-icon-error" style="font-size: 30px;color: red;"></i>
                  <div class="data">
                    <div class="title">失败数据</div>
                    <div class="total">{{importDataInfo.errorCount}}</div>
                  </div>
                </div>
                <div class="item" v-if="importDataInfo.error_file">
                  <i class="el-icon-document" style="font-size: 30px;color: red;"></i>
                  <div class="data">
                    <div class="title">错误文件</div>
                    <div class="total">
                      <el-link style="color: #1156de;" @click="downloadErrorFile()" target="_blank">下载</el-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="content" style="padding: 20px;" v-if="importRes.completed">
              <el-result :icon="importRes.status" :title="importRes.msg"></el-result>
            </div>
         </div>

         <span slot="footer" class="dialog-footer">
          <el-button :loading="loading" @click="startImport()" v-if="activeStep == 1 && fileList.length " type="primary">开始导入</el-button>
          <el-button @click="close">关闭</el-button>
         </span>
      </el-dialog>

      <common-import-record
        ref="importRecordRef"
        :openView="openImportView"
        :importCode="importCode"
        showCurImportRecord
        @close="openImportView = false"
      />
    </div>
  </template>
  
  <script>
  import BasicTable from '@/components/BasicComponents/Table'
  import attachmentApi from '@/api/system/attachmentApi'
  import commonImportRecord from './record'
  import {getToken} from '@/utils/auth'
  import { EventSourcePolyfill } from 'event-source-polyfill'

  export default {
    name: 'commonImportPage',
    props: {
      openView: Boolean,
      showCurImportRecord: {
        type: Boolean,
        default: false
      },
      showDownloadTemplate: {
        type: Boolean,
        default: true
      },
      importCode:{
          type: String,
          require
      },
      uploadUrl: {
        type: String,
        default: null
      },
      accept: {
        type: String,
        default: '*/*'
      },
      fileType: {
      // 文件类型白名单
        type: Array,
        default: () => ["csv","xls", "xlsx","zip"],
      },
      // 大小限制(MB)
      fileSize: {
        type: Number,
        default: 300,
      },
      extendParam: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        loading: false,
        openImportView: false,
        openImportDetailView: false,
        // tab显示的全阶段附件
        allFilesTableTitle: [],
        allFilesTableData: [],
        // 总条数
        total: 0,
        activeStep: 1,
        uploadFileUrl: process.env.VUE_APP_BASE_API + '/common/upload',
        pageParams: {
          pageNum: 1,
          pageSize: 10,
        },
        headers: {
          Authorization: 'Bearer ' + getToken(),
          reqType: 'back',
          ip: process.env.VUE_APP_LOCAL_IP,
        },
        fileList: [],
        uploadProgress: 0,
        importProgress: 0,
        importDetails:[],
        importRes:{
          completed: false,
          status: 'success',
          msg: '导入完成'
        },
        eventSource: null,
        importDataInfo:{
          readRowCount: 0,
          insertCount: 0,
          updateCount: 0,
          toerrorCounttal: 0,
          error_file: null,
          error_file_name: null
        },
        isTaskCompleted: false,
        reconnectCount: 0,
      }
    },

    created() {
      if(this.uploadUrl) this.uploadFileUrl=process.env.VUE_APP_BASE_API+  this.uploadUrl
    },
    mounted() {
    },
    computed: {
      showNext(){
        if(this.activeStep == 0 && this.fileList.length) return true
        if(this.activeStep == 1) return true
        return false
      }
    },
    methods: {
        downLoadTemplate() {
          attachmentApi.downloadTemplate(this.importCode)
        },
        handleBeforeUpload(file){
          const fileSize = file.size / 1024 / 1024
          let fileExtension = ''

          // 校检文件类型
          if (this.fileType) {
            if (file.name.lastIndexOf('.') > -1) {
              fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
              fileExtension = fileExtension && fileExtension.toLowerCase()
            }
            const isTypeOk = this.fileType.some((type) => {
              if (file.type.indexOf(type) > -1) return true
              if (fileExtension && fileExtension.indexOf(type) > -1) return true
              return false
            })
            if (!isTypeOk) {
              this.$message.error(
                `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
              )
              return false
            }
          }

          // 校检文件大小
          if (this.fileSize) {
            const isLt = fileSize < this.fileSize
            if (!isLt) {
              this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
              return false
            }
          }
        },
        close() {
          this.importRes.completed &&  this.$emit('refresh')
          this.$emit('close')
        },

        handleChange(file, fileList) {
          this.fileList = fileList.slice(-1); 
        },
        handleRemove(file, fileList) {
          this.fileList = fileList;
        },
        handleSuccess(response, file) {
          // 如果需要根据接口返回的url更新文件信息
          if(response.code != 200){
            this.importRes.completed = true
            this.importRes.status = 'error'
            this.importRes.msg = response.msg
          }else{
            this.importProgress = 1
            this.subscribeToProgress(response.data)
          }
        },
        handleRemoveFile(file) {
          this.$refs.upload.handleRemove(file); // 调用组件内部方法移除文件
        },
        async startImport(){
            this.activeStep = 2
            this.$refs.upload.submit();
        },
        handleProgress(event, file, fileList) {
          let progress = parseInt(event.percent); // 获取进度百分比
          this.uploadProgress = progress
        },
        async qryImportRecord() {
          this.openImportView = true
          await this.$nextTick()
          await this.$refs.importRecordRef?.init()
        },
        updateProgress(data){
          this.importProgress = data.percentage
          this.importDetails.push(data)

          if(this.importProgress == 100){
            this.importRes.completed = true
            this.importRes.status = data.success ? 'success' :'error'
            this.importRes.msg = data.message
            data.result && (this.importDataInfo = data.result)
            this.importDataInfo.error_file = data.error_file
            this.importDataInfo.error_file_name = data.error_file_name
          }

           this.$nextTick(() => {
            const container = this.$refs.messageContainer;
            container.scrollTop = container.scrollHeight;
          });
        },

        subscribeToProgress(taskId){
          if(this.isTaskCompleted){
            console.log("任务已完成，无需连接");
            return;
          }
          let that = this;
          this.eventSource?.close()

          const baseUrl = process.env.NODE_ENV === 'development' ? process.env.VUE_APP_DEV_URL : process.env.VUE_APP_BASE_API
          this.eventSource = new EventSourcePolyfill(baseUrl + `/kernel/common/import/sse/${taskId}`, {
            heartbeatTimeout: 60 * 60 * 1000,
            headers: {
              Authorization: 'Bearer ' + getToken(),
              ip: process.env.VUE_APP_LOCAL_IP,
              reqType: 'back',
            }
          })
          this.eventSource.onopen = event => {
            console.log("连接建立成功")
          }

          this.eventSource.addEventListener('sse-message', event => {
              try {
                const data = JSON.parse(event.data)
                console.log("sse-message：", data)
                that.updateProgress(data)
              } catch (error) {
                console.log(event)
              }
          });

          this.eventSource.addEventListener('sse-completed', event => {
            console.log("收到任务完成事件:", event.data);
            this.isTaskCompleted = true;
            this.eventSource?.close();
            this.reconnectCount = 0
          });

          //如果发生通信错误（比如连接中断），就会触发error事件
          this.eventSource.onerror =  event =>{
            if (this.eventSource.readyState === EventSource.CLOSED) {
              if (!this.isTaskCompleted && this.reconnectCount < 5) {
                  this.reconnectCount++;
                  console.log(`尝试重新连接 (${this.reconnectCount}/5)...`);
                  setTimeout(() => this.subscribeToProgress(taskId), 3000);
              }
            }else{
              console.error('SSE连接错误:', event);
            }
          }
      },
      downloadErrorFile(){
        attachmentApi.downloadUrl(this.importDataInfo.error_file,this.importDataInfo.error_file_name)
      }
    },
    components: {
      BasicTable,
      commonImportRecord,
    },
  }
  </script>

<style lang="scss" scoped>
::v-deep .el-upload{
    width: 100%;
}

::v-deep .el-upload-dragger{
    width: 100%;
}

.step-top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info{
  display: flex;
  flex-direction: column;
  align-items: center;
}

.custom-file-display{
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.progress-content{
  width: 100%;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.header{
  padding: 10px 20px;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
}

.content {
  .info-box{
    display: flex;
    align-items: center;
  }
  .item{
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .total{
    padding-top: 5px;
  }
}

.import-result{
  flex: 1;
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.result-error{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
  