<template>
  <div>
    <el-row v-if='dealType != "transfer" && dealType != "xqsubmit"'>
      <el-col :span='24'>
        <el-form>
          <!-- 为了不影响原本的按钮样式,v-if对应新疆数据规划驳回施工验收的按钮样式,v-else是其他情况原始的按钮样式-->
          <el-radio-group
            v-if="isCurrentProvince('XJ') && sysconfig.ENABLE_XJ_SJGH_REJECT_SGYS === 'true' && this.$route.query.nodeId === 'jt206c9b2e90de4e77a6710540c80d1913' "
            size='mini'
            style='padding-bottom: 15px;'
            v-model='checkBtn'
          >
            <template v-for='button in Object.entries(buttons).filter(buttonsFilter).sort(this.buttonsSort)'>
              <div style="display: inline-flex; flex-direction: column; align-items: center; margin-right: 20px;">
                <el-radio
                  :key='button[0]'
                  :label='button[0]'
                  :disabled='disabledButtons.includes[button[0]]'
                  v-if='!permissions.includes("plan:ban:button-" + currentChildStage.toLowerCase() + "-" + button[0])'
                >{{ button[1] }}
                </el-radio>
                <div v-if="button[1] === '驳回传输调度'" style="font-size: 15px;margin-top: 10px">
                  (传输数据电路制作异常驳回仅删除传输数据)
                </div>
                <div v-if="button[1] === '驳回施工验收'" style="font-size: 15px;margin-top: 10px">
                  (无线基站数据填写错误驳回删除传输数据和资管数据)
                </div>
              </div>
            </template>
          </el-radio-group>
          <el-radio-group
            v-else
            size='mini'
            style='padding-bottom: 15px;'
            v-model='checkBtn'
          >
            <template v-for='button in Object.entries(buttons).filter(buttonsFilter).sort(this.buttonsSort)'>
              <el-radio
                :key='button[0]'
                :label='button[0]'
                :disabled='disabledButtons.includes[button[0]]'
                v-if='!permissions.includes("plan:ban:button-" + currentChildStage.toLowerCase() + "-" + button[0])'
              >{{ button[1] }}
              </el-radio>
            </template>
          </el-radio-group>
          <el-form-item prop="sameOrderNo" v-if="showSameOrderNo">
            <el-radio-group size='mini' v-model='sameOrderNo'>
              <el-radio
                label="1"
              >
                同工单变更
              </el-radio>
              <el-radio
                label="0"
              >
                异工单变更
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="sameOrderNo == '0'" prop="changeOrderNo">
            <el-input placeholder="请输入变更后工单号" v-model="changeOrderNo"></el-input>
          </el-form-item>
          <div
            v-if="checkBtn && planRejectReasonOptions && planRejectReasonOptions.length >0 && !['0','1','hanghup'].includes(checkBtn)">
            <el-divider content-position="left">驳回原因</el-divider>
            <el-form-item
              prop="rejectReason">
              <el-checkbox-group
                size='mini'
                style='padding-bottom: 15px;'
                v-model='rejectReason'
              >
                <el-checkbox
                  :key='dict.code'
                  :label='dict.name'
                  v-for='dict in planRejectReasonOptions'
                >{{ dict.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
<!--          <template v-if='isCurrentProvince("XJ") && checkBtn == 2 && isShowRecircuit'>-->
<!--            <el-divider content-position="left">是否重新制作电路</el-divider>-->
<!--            <el-switch-->
<!--              v-model="notifyExtendParam.recircuit"-->
<!--              active-text="是"-->
<!--              style='margin-bottom: 15px;'-->
<!--              inactive-text="否">-->
<!--            </el-switch>-->
<!--          </template>-->
          <el-input
            :rows='4'
            placeholder='请输入内容'
            type='textarea'
            v-model='remark'
            @input="handleInput"
          >
          </el-input>
        </el-form>
      </el-col>
    </el-row>

    <el-tabs
      class="top-tabs"
      type="border-card"
      style="padding-top: 5px;"
      v-if='dealType != "transfer"'
      v-model="currentTaskTab"
    >
      <el-tab-pane
        :key="taskTab.code"
        :label="taskTab.name"
        :name="taskTab.code"
        v-for="taskTab in dealTaskTab"
      ></el-tab-pane>
    </el-tabs>

    <div v-if='currentTaskTab == "beforeReply" && dealType != "transfer"' >
      <el-tabs v-model="siteRelocation"  style="padding: 20px;">
        <el-tab-pane label="非异址变更" name="N">
          <commonQueryTable checkbox radio
                            :queryParams="extendQueryParam"
                            @handleSelectionChange='handleBeforeReplayChange'
                            :fixedHeight="500"
                            code="qryBeforeReplyPeorid"></commonQueryTable>
        </el-tab-pane>
        <el-tab-pane label="异址变更" name="Y">

          <commonQueryTable checkbox radio
                            :queryParams="extendQueryParam"
                            @handleSelectionChange='handleBeforeReplayChange'
                            :fixedHeight="500"
                            code="qryHangupPeroidList"></commonQueryTable>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if='currentTaskTab == "planAuditByManual" && dealType != "transfer"' >
          <commonQueryTable  radio
                            :queryParams="extendQueryParam"
                            @handleSelectionChange='handleBeforeReplayChange'
                            :fixedHeight="500"
                            ref="planAuditTable"
                            code="qrySiteAuditDataZj"></commonQueryTable>
    </div>
    <div v-if='currentTaskTab == "hangupOrder" && dealType != "transfer"'>
      <commonQueryTable checkbox radio
                        :queryParams="extendQueryParam"
                        @handleSelectionChange='handleBeforeReplayChange'
                        :fixedHeight="500"
                        code="qryHangupPeroidList"></commonQueryTable>
    </div>
    <div
      v-show='currentTaskTab == "orderDispatch" && dealType != "transfer" &&  dispatchInterfaceData && dispatchInterfaceData.length > 0'>
      <BasicTable
        :insertIndex='0'
        :tableData='dispatchInterfaceData'
        :tableTitle='orderDistributionHeader'
        ref='distribTable'
        style='padding-top: 10px;'
      >
        <template slot-scope='row'>
          <el-checkbox :disabled="disableInterfaceSelection"
            v-model='row.item.checked'
          ></el-checkbox>
        </template>
      </BasicTable>

      <keep-alive>
        <SingeTestUser
          v-if="!isCurrentProvince('XJ')"
          :siteIdCmcc="siteIdCmcc"
          @singeTestRes='singeTestRes'
          v-show="singeTestDispatchCheck"></SingeTestUser>
      </keep-alive>
    </div>

    <el-row class='select-user' v-show=' (currentTaskTab == "nextDealUser" ) && sysconfig.ASSIGN_SWITCH == "true" '>
      <el-col :span='8'>
        <el-tree
          :data='deptOptions'
          :filter-node-method='filterNode'
          :props='defaultProps'
          @node-click='handleNodeClick'
        />
      </el-col>
      <el-col :span='16'>
        <div>
          <el-form :inline='true' @submit.native.prevent>
            <el-form-item>
              <el-input
                placeholder='姓名/账号/手机号'
                style='width: 240px'
                v-model='searchParam'
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button @click='getList' type='primary'>搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table
          :data='userList'
          @selection-change='handleSelectionChange'
          ref='multipleTable'
        >
          <el-table-column type='selection'
                           width='55'
                           :selectable='selectableCheckbox'
          ></el-table-column>
          <el-table-column
            align='center'
            key='account'
            label='用户账号'
            prop='account'
          />
          <!-- 北京增加发送短信提醒       -->
          <el-table-column label="短信提醒" v-if="isCurrentProvince('BJ')">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.smsRemind" :disabled="scope.row.disbledSms"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column align='center' key='name' label='用户名称' prop='name'/>
          <el-table-column
            align='center'
            key='phone'
            label='手机号码'
            prop='phone'
          />
          <el-table-column
            align='center'
            key='pnames'
            label='部门'
            prop='pnames'
          />
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :pageSizes="isCurrentProvince('BJ') ? [100] : [10, 20, 30, 50, 100]"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <el-row></el-row>
    <section class='task-handle'>
      <el-button
        @click='handleTask'
        element-loading-background='rgba(255, 255, 255, 0.3)'
        size='large'
        type='primary'
        v-loading.fullscreen.lock='loading'
      >确定
      </el-button>
      <el-button
        @click='$emit("update:dealTask", false)'
        size='large'
      >取消
      </el-button>
    </section>
  </div>
</template>
<script>
import * as service from '@/api/site/plan/handle'
import { commonQuery } from '@/api/kernel/query'
import { cityCompanyDeptTree} from "@/api/system/cityCompany";
import { mapGetters, mapState } from 'vuex'
import BasicTable from '@/components/BasicComponents/Table'
import {isEmpty, isCurrentProvince} from '@/utils/common'
import { executeAuditRule } from '@/api/site/plan/auditRule'

// 云南需求变更 流向字典值
const DEMAND_CHANGE_FLOW_SEQUENCE_CODE = '2003'
const DATA_CHANGE_FLOW_SEQUENCE_CODE = '2009'

export default {
  name: 'taskDeal',
  props: {
    buttons: {
      require: true,
    },
    buttonsFilter: {
      require: false,
      type: Function,
      default: ([key, value]) => true
    },
    siteData: {
      type: Object,
      required: false,
    },
    siteIdCmcc: {
      type: String,
      required: true,
    },
    dealType: {
      type: String,
      required: true,
    },
    currentChildStage: {
      type: String,
      required: false,
    },
    dispatchAllInterfaceData: {
      type: Array,
      require: false,
      default: () => []
    },
    isRejected: {
      type: Boolean,
      default: false,
    },
    isNBSite: {
      type: Boolean,
      default: false,
    },
    isSFSite: {
      type: Boolean,
      default: false,
    },
    ck: {
      type: Number,
      required: false,
    },
    relType: {
      type: String,
      default: 'plan',
    },
    disabledButtons: {
      type: Array,
      default: () => []
    },
    disableInterfaceSelection: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['sysconfig', 'permissions']),

    singeTestDispatchCheck() {
      let findDy = this.dispatchInterfaceData?.find((item) => {
        return item.checked == true && item.businessCode == 'DY'
      })
      if (findDy) return true
      return false
    },
  },
  data() {
    return {
      protectedText: '【已确认审核结果】',
      searchParam: '',
      checkBtn: null,
      remark: null,
      deptOptions: [],
      // 总条数
      total: 0,
      userList: [],
      extendQueryParam: {},
      defaultCheckedUserList: [],
      rejectReason: [],
      siteRelocation: 'N',
      planRejectReasonOptions: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      orderDistributionHeader: [
        {name: 'checked', title: '是否派单'},
        {name: 'interfaceName', title: '派单类型'},
      ],
      currentTaskTab: "nextDealUser",
      dealTaskTab: [{
        code: "nextDealUser",
        name: "下一步处理人员"
      }],
      // 需求变更 异/同工单变更
      showSameOrderNo: false,
      sameOrderNo: '1',
      // 异工单变更的工单号
      changeOrderNo: '',
      nodeExtendData: null,
      multipleSelection: [],
      // 查询参数
      queryParams: {
        seqCode: '',
        deptId: '',
        pageNum: 1,
        pageSize: isCurrentProvince('BJ') ? 100 : 20,
      },

      loading: false,
      notifyExtendParam: {
        recircuit: false, //是否重新制作电路
      },
      isShowRecircuit: false, // 新疆数据规划节点驳回才显示重新制作电路
      dispatchInterfaceData: []
    }
  },
  created() {
    this.isShowRecircuit = this.$route.query.nodeId === 'jt206c9b2e90de4e77a6710540c80d1913'
    this.extendQueryParam.siteIdCmcc = this.siteIdCmcc
    this.extendQueryParam.nodeId = this.$route.query.nodeId

    //驳回原因字典
    this.getDicts("PLAN_REJECT_REASON").then(response => {
      if (response.data && response.data.length > 0) {
        this.planRejectReasonOptions = response.data.filter((s) => s.remark && s.remark.includes(this.currentChildStage));
      }
    });

  },
  mounted() {
    this.getTreeselect()
    this.getList()

  },
  watch: {
    checkBtn() {
      this.rejectReason = []
      this.findSeqDispatchInterface()
      this.getList()
      this.showBeforehandReply()

    },
  },
  methods: {
    isCurrentProvince,
    handleInput(value) {
      if (isCurrentProvince("ZJ")) {
        if (!value.startsWith(this.protectedText)) {
          // 提取用户输入的非保护部分（移除任何可能的部分保护文本）
          const userInput = value.replace(this.protectedText, '');
          // 拼接保护文本和用户输入
          this.remark = this.protectedText + userInput;
        }
      }
    },
    buttonsSort(a, b) {
      const [aCode,] = a;
      const [bCode,] = b;
      return +`${aCode[0]}` - +(`${bCode[0]}`);
    },
    handleSelectionChange(data) {
      this.multipleSelection = data
      // 北京处理短信提醒框选禁用状态
      if(isCurrentProvince("BJ")) {
        const ids = data.map(i => i.user_id);
        this.userList.forEach(item => {
          item.disbledSms = !(item.phone && ids.includes(item.user_id));
        })
      }
    },
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.$nextTick(() => {
        this.getList()
      })
    },
    findSeqDispatchInterface(){
      let checkBtn = this.checkBtn;
      if('2_XJ_jt557774589a66472a81fd4d9266dbd586' === checkBtn) {
        //新疆数据规划驳回施工验收
        checkBtn = '2'
      }
      // 云南需求变更
      this.showSameOrderNo = false;
      if (DEMAND_CHANGE_FLOW_SEQUENCE_CODE === checkBtn) {
        this.sameOrderNo = '1'
        this.showSameOrderNo = true;
      }
      const findOrderDispatch = this.dealTaskTab.find(dtt=>dtt.code == 'orderDispatch')
      this.dispatchInterfaceData = this.dispatchAllInterfaceData.filter(daid=>daid.sequenceCode == checkBtn)
      if (this.dispatchInterfaceData && this.dispatchInterfaceData.length > 0) {
        this.currentTaskTab = "orderDispatch"
        if (this.currentChildStage == 'SJSH') {
          this.dispatchInterfaceData.forEach(di => {
            if (di.businessCode == "ZTY") di.checked = !(this.isNBSite || this.isSFSite)
            if (di.businessCode == "DD") di.checked = !this.isNBSite
            if (di.businessCode == "GD") di.checked = !this.isRejected
          })
        }
        !findOrderDispatch && this.dealTaskTab.unshift({
          code: "orderDispatch",
          name: "工单派发"
        });
      }else{
        findOrderDispatch && this.dealTaskTab.splice(this.dealTaskTab.findIndex(item => item.code === 'orderDispatch'), 1)
        this.currentTaskTab = 'nextDealUser'
      }
    },
    async requestAuditResult() {
      if (this.currentChildStage === 'GHK' && isCurrentProvince("ZJ")) {
        this.loading = true
        executeAuditRule({
          nodeId: this.$route.query.nodeId,
          sequenceCode: this.checkBtn,
          orderNo: this.siteIdCmcc,
          siteData: this.siteData,
          stageCode: 'GHJD'
        }).then(() => {
          this.loading = false
          this.$refs.planAuditTable.search()
        }).catch(e => {
          this.loading = false
          this.$refs.planAuditTable.search()
        })
      }
      },
    showBeforehandReply() {
      if (this.currentChildStage == 'XQSH') {
        if (this.checkBtn == '1') {
          this.dealTaskTab.unshift({
            code: "beforeReply",
            name: "预批复"
          })
          this.currentTaskTab = 'beforeReply'
        } else {
          this.dealTaskTab.splice(this.dealTaskTab.findIndex(item => item.code === 'beforeReply'), 1)
          this.currentTaskTab = 'nextDealUser'
        }
      }
      if (this.currentChildStage === 'GHK' && isCurrentProvince("ZJ")) {
        if (this.checkBtn === '1') {
          this.dealTaskTab.unshift({
            code: "planAuditByManual",
            name: "审核结果"
          })
          this.currentTaskTab = 'planAuditByManual'
          this.requestAuditResult()
        } else {
          this.dealTaskTab.splice(this.dealTaskTab.findIndex(item => item.code === 'planAuditByManual'), 1)
          this.currentTaskTab = 'nextDealUser'
        }
      }

    },

    handleBeforeReplayChange(val) {
      this.nodeExtendData = val[0]
      this.nodeExtendData.siteRelocation = this.siteRelocation
    },
    //用户列表
    getList() {
      const params = {
        nodeId: this.$route.query.nodeId,
        siteIdCmcc: this.$route.query.tagsViewTitle,
        operType: this.dealType,
        seqCode: this.checkBtn,
        searchParam: this.searchParam,
        relType: this.relType
      }
      if('2_XJ_jt557774589a66472a81fd4d9266dbd586' === params.seqCode) {
        //新疆数据规划驳回施工验收
        params.seqCode = '2'
      }
      commonQuery(
        'qryAssignTransferUser',
        Object.assign(this.queryParams, params)
      ).then((response) => {
        this.userList = response.data
        this.total = response.total
        this.defaultCheckedUserList = response.data.filter((item) => item.checked)
        // 北京，接口返回无默认选中时，手动触发一次多选框数据改变，用于初始化短信提醒选择框的禁用状态
        if(isCurrentProvince("BJ") && this.defaultCheckedUserList.length < 1) {
          this.handleSelectionChange([])
        }
        this.$nextTick(() => {
          this.toggleSelection(this.defaultCheckedUserList)
        })
      })
    },

    selectableCheckbox(row, index) {
      // true: 可以选
      return !(row.disabled && this.dealType != "transfer")
    },

    //选择的单验测试用户
    singeTestRes(singeTestData) {
      this.nodeExtendData = singeTestData
    },

    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      cityCompanyDeptTree().then((response) => {
        this.deptOptions = response.data
      })
    },

    handleTask() {
      if (!this.checkBtn && this.dealType != 'transfer' && this.dealType != 'xqsubmit') {
        this.msgInfo('请选择处理分支！')
        return
      }

      if (
        // 通过、通过(赔补)、需求变更（云南规划流程 ）、数据变更（云南规划流程 ） 无需输入原因
        (this.checkBtn != 1 && this.checkBtn !== '10008' && (isCurrentProvince('YN') && this.checkBtn!== DEMAND_CHANGE_FLOW_SEQUENCE_CODE && this.checkBtn!== DATA_CHANGE_FLOW_SEQUENCE_CODE))
        && !this.remark
        && this.dealType != 'transfer'
      ) {
        if (this.rejectReason.length == 0) {
          this.msgInfo(`请输入处理意见！`)
          return
        }

      }
      if (this.userList.length < 1) {
        this.msgInfo(`无下一步处理人员！`)
        return
      }
      let ids = []
      if(isCurrentProvince('BJ')) {
        // 北京
        ids = this.multipleSelection.map((item) => item.user_id)
        if (ids.length < 1) {
          this.msgInfo(this.dealType === 'transfer' ? '请勾选转派人员' : `请勾选处理人员！`)
          return
        }
      } else {
        this.multipleSelection.map((item) => {
          if (!this.defaultCheckedUserList.find((ele) => ele.user_id == item.user_id)) {
            ids.push(item.user_id)
          }
        })
      }
      const params = {
        sequenceCode: this.checkBtn,
        remark: this.remark,
        assignUserList: ids,
        transferUserList: ids,
        dispatchList: this.dispatchInterfaceData
      }

      if(isCurrentProvince("BJ")) {
        params.smsNoticeUserList = this.userList.filter(item => item.smsRemind).map(item => item.user_id)
      }
      this.dealType != 'transfer' && this.ck && params.assignUserList.push(this.ck)

      //驳回原因
      if (this.rejectReason.length > 0) {
        params.dealExtendData = {rejectReason: this.rejectReason.join(",")}
        if (!params.remark) params.remark = params.dealExtendData.rejectReason
      }

      if (!isEmpty(this.nodeExtendData)) {
        params.nodeExtendData = this.nodeExtendData
      }
      params.notifyExtendParam = this.notifyExtendParam

      // 云南：流转到需求变更审核
      if (isCurrentProvince('YN') && DEMAND_CHANGE_FLOW_SEQUENCE_CODE === this.checkBtn) {
        if (this.sameOrderNo === '0') {
          if (this.changeOrderNo.length == 0) {
            this.msgInfo(`请输入变更后工单号！`)
            return
          }
        }
        params.sameOrderNo = this.sameOrderNo
        params.changeOrderNo = this.changeOrderNo
      }


      if(this.dealType != 'xqsubmit') {
        this.loading = true
      }
      this.$emit('handleTask', params)
    },
  },
  components: {
    BasicTable,
    SingeTestUser: () => import('@/components/Plan/SingeTestUser'),
    commonQueryTable: () => import('@/components/CommonQuery/commonQueryTable'),
  },
}
</script>
<style lang='scss' scoped>
.select-user {
  height: 35vh;
  overflow-y: auto;
  padding-top: 5px;
}

.task-handle {
  text-align: right;
  padding-top: 15px;
}

::v-deep .el-form-item__content {
  width: 100%;
}

::v-deep .el-radio__inner {
  width: 25px;
  height: 25px;
}

::v-deep .el-radio__label {
  font-size: 18px;
}
</style>
