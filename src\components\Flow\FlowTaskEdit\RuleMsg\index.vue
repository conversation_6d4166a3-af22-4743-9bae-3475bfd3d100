<template>
  <div class="app-container">
    <el-button type="success" icon="el-icon-plus"  @click="handleAdd" v-hasPermi="['audit:rule:add']">新增</el-button>
    <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="流出分支" align="center" prop="sequenceCode" :formatter="sequenceCodeFormat"/>
      <el-table-column label="通用查询编码" align="center" prop="queryCode" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="ENABLE"
                inactive-value="DISABLE"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['audit:rule:edit']"
          >修改</el-button>
          <el-popconfirm
            @confirm="() => handleDelete(scope.row)"
            :style="{ 'margin-left': '12px' }"
            title="确定删除吗?"
            >
            <el-button
                icon="el-icon-delete"
                size="mini"
                slot="reference"
                type="text"
                >删除</el-button
            >
            </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <p style="color:red;font-size:18px">此配置无需发布流程</p>

    <!-- 添加或修改规划审核配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="90%" append-to-body>
        <ruleEdit
            ref="ruleEdit"
            v-if="open"
            :ruleId="editRuleId"
            :nodeId="taskInfo.nodeId"
            :procDefKey="taskInfo.procDefKey"
            :processBranch="taskInfo.completeNotifyUrl"
            :statusOptions="statusOptions"
            @cancel="cancel"
            @cancelLoding="()=>{saveLoading = false}">
        </ruleEdit>
        <div slot="footer" class="dialog-footer">
            <el-button :loading="saveLoading" type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, changeStatus, delRule } from "@/api/kernel/flow";
import ruleEdit from "./ruleEdit";

export default {
  name: "Rule",
  components: {
      ruleEdit
  },
  props:{
    taskInfo:{
      type: Object
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      saveLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规划审核配置表格数据
      ruleList: [],
      statusOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeId: this.taskInfo.nodeId,
        ruleType:"2"
      },
      editRuleId: null,
    };
  },
  created() {
    this.getList();
    this.getDicts("SYS_NORMAL_STATUS").then(response => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    /** 查询规划审核配置列表 */
    getList() {
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel(isGetList) {
      this.open = false;
      this.saveLoading = false
      isGetList && this.getList()
    },
    sequenceCodeFormat(row, column){
      var actions = [];
      Object.keys(this.taskInfo.completeNotifyUrl).some((key) => {
        if (this.taskInfo.completeNotifyUrl[key].condition == ('' + row.sequenceCode)) {
          actions.push(this.taskInfo.completeNotifyUrl[key].conditionName);
          return true;
        }
      })
	    return actions.join('');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.title = "添加配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids
      this.editRuleId = id;
      this.open = true;
      this.title = "编辑配置";
    },
    /** 提交按钮 */
    submitForm() {
      this.saveLoading  = true
      this.$refs.ruleEdit.submitForm()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      delRule(ids).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
      })
    },
    handleStatusChange(row) {
      let text = row.status === "ENABLE" ? "启用" : "停用";
      this.$confirm('确认要"' + text + '""' + row.name + '"吗?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return changeStatus(row);
        }).then(() => {
          this.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === "ENABLE" ? "DISABLE" : "ENABLE";
        });
    },
  }
};
</script>
