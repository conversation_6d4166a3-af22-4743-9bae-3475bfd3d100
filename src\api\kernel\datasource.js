import request from '@/utils/request'

/** 通用查询数据源 **/
export const datasource_list = data => {
  return request({
    url: 'kernel/datasource/list', data})
}

// 生产环境换成post
export const datasource_save = data => {
  return request({
    url: 'kernel/datasource/update',
    method: 'post',
    data
  })
}

export const datasource_delete = data => {
  return request({
    url: 'kernel/datasource/delete',
    method: 'post',
    data
  })
}

export const datasource_detail = id => {
  return request({
    url: 'kernel/datasource/detail/' + id,
    method: 'get',
  })
}


