import request from '@/utils/request'

// 查询分析-方案列表
export function listScheme(query) {
  return request({
    url: '/an/scheme/list',
    method: 'get',
    params: query
  })
}

// 查询分析-方案详细
export function getScheme(id) {
  return request({
    url: '/an/scheme/' + id,
    method: 'get'
  })
}

// 新增分析-方案
export function addScheme(data) {
  return request({
    url: '/an/scheme',
    method: 'post',
    data: data
  })
}

// 修改分析-方案
export function updateScheme(data) {
  return request({
    url: '/an/scheme',
    method: 'put',
    data: data
  })
}


export function updateSchemeById(data) {
  return request({
    url: '/an/scheme/updateSchemeById',
    method: 'post',
    data: data
  })
}
// 删除分析-方案
export function delScheme(id) {
  return request({
    url: '/an/scheme/' + id,
    method: 'delete'
  })
}

export function getSchemeTypes() {
  return request({
    url: '/an/scheme/getSchemeType',
    method: 'get'
  })
}

export function qrySchemeTypes(query) {
  return request({
    url: '/an/scheme/qryTypes',
    method: 'get',
    params: query
  })
}


export function qryOptimizeSchemeTypes(query) {
  return request({
    url: '/an/scheme/qryOptimizeTypes',
    method: 'get',
    params:query
  })
}


export function newPerScheme() {
  return request({
    url: '/an/scheme/getNew',
    method: 'get'
  })
}

export function getListByParentId(id) {
  return request({
    url: '/an/scheme/getListByParentId/' + id ,
    method: 'get'
  })
}




