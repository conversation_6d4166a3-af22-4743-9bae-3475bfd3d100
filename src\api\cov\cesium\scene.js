import request from '@/utils/request'
import axios from "@/utils/request";

export function list(data) {
    return request('cov/scene/list?account=' + data)
}

export function getSceneLevel() {
    return request('cov/scene/getSceneLevel')
}

export function save(data) {
    return request({
      data,
      url: 'cov/scene/save',
      method: 'post'
    })
}

export function deleteScene(data) {
    return request('cov/scene/delete?dbId=' + data)
}

export function uploadTabFile(data) {
  return axios.get('cov/scene/uploadTabFile', { params: data })
}

export function exportShp(data) {
  return request({
    headers: {
      'Content-Type': "application/json"
    },
    url: 'cov/scene/exportShp',
    method: 'post',
    data
  })
}

export function exportCsv(data) {
  return request({
    headers: {
      'Content-Type': "application/json"
    },
    url: 'cov/scene/exportCsv',
    method: 'post',
    data
  })
}

export function tarnsGeo(shape) {
  return request('cov/scene/transGeo?shape='+shape)
}

export const readShp = path => {
  return request({
    url: 'cov/scene/readShp',
    method: 'get',
    params: {filePath: path}
  })
}
