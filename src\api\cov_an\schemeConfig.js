import request from '@/utils/request'

// 查询问题分析及优化方案配置列表
export function listConfig(query) {
  return request({
    url: '/an/schemeConfig/list',
    method: 'get',
    params: query
  })
}

// 查询问题分析及优化方案配置详细
export function getConfig(id) {
  return request({
    url: '/an/schemeConfig/' + id,
    method: 'get'
  })
}

// 新增问题分析及优化方案配置
export function addConfig(data) {
  return request({
    url: '/an/schemeConfig',
    method: 'post',
    data: data
  })
}

// 修改问题分析及优化方案配置
export function updateConfig(data) {
  return request({
    url: '/an/schemeConfig',
    method: 'put',
    data: data
  })
}

// 删除问题分析及优化方案配置
export function delConfig(id) {
  return request({
    url: '/an/schemeConfig/' + id,
    method: 'delete'
  })
}

// 查询方案列表
export function listAnalysisScheme(data) {
  return request({
    url: '/an/schemeConfig/scheme/analysis/list',
    method: 'get',
    data: data
  })
}

// 查询方案列表
export function listOptimizeScheme(data) {
  return request({
    url: '/an/schemeConfig/scheme/optimize/list',
    method: 'get',
    data: data
  })
}

// 查询问题分析及优化方案配置列表
export function listRuleType(query) {
  return request({
    url: '/an/schemeConfig/ruleType/list',
    method: 'get',
    params: query
  })
}
