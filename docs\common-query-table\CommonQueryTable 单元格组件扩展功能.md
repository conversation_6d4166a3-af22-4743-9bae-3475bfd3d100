# CommonQueryTable 单元格组件扩展功能

## 功能概述

为 CommonQueryTable 组件添加了单元格自定义组件支持，允许通过配置的方式为特定列渲染自定义组件（如勾选框、开关、按钮等），并支持事件回调处理。

## 已实现的功能

### 1. 核心扩展
- ✅ 在 `CommonQueryTable` 组件中添加 `cellComponents` 属性
- ✅ 在 `basic-table` 组件中扩展单元格渲染逻辑
- ✅ 实现事件传递机制 `cellComponentEvent`

### 2. 内置组件
- ✅ `CheckboxCell` - 勾选框组件
- ✅ `SwitchCell` - 开关组件  
- ✅ `ButtonCell` - 按钮组件

### 3. 示例和文档
- ✅ 完整的使用文档和API说明
- ✅ 逻辑基站退网资源管理示例
- ✅ 功能测试页面

## 快速开始

### 1. 基本使用

```vue
<template>
  <common-query-table
    :code="queryCode"
    :cell-components="cellComponentsConfig"
    @cellComponentEvent="handleCellEvent"
  />
</template>

<script>
export default {
  data() {
    return {
      queryCode: 'your_query_code',
      cellComponentsConfig: {
        'equipment_remove': {
          component: 'CheckboxCell',
          props: {
            trueValue: 1,
            falseValue: 0
          }
        },
        'resource_delete': {
          component: 'CheckboxCell',
          props: {
            trueValue: 1,
            falseValue: 0
          }
        }
      }
    }
  },
  methods: {
    handleCellEvent(eventName, data, row, column) {
      if (eventName === 'checkboxChange') {
        // 处理勾选框变化，调用保存API
        this.saveCellValue(row.id, data.fieldName, data.value);
      }
    },
    
    async saveCellValue(id, fieldName, value) {
      try {
        await this.$api.updateRecord(id, { [fieldName]: value });
        this.$message.success('保存成功');
      } catch (error) {
        this.$message.error('保存失败');
      }
    }
  }
}
</script>
```

### 2. 针对逻辑基站退网资源的具体配置

```javascript
// 逻辑基站列表的设备拆除和资源删除字段配置
cellComponentsConfig: {
  'equipment_remove': {
    component: 'CheckboxCell',
    props: {
      label: '',           // 不显示标签
      trueValue: 1,        // 选中时的值
      falseValue: 0        // 未选中时的值
    }
  },
  'resource_delete': {
    component: 'CheckboxCell',
    props: {
      label: '',
      trueValue: 1,
      falseValue: 0
    }
  }
}
```

## 文件结构

```
src/
├── components/
│   ├── CommonQuery/
│   │   ├── commonQueryTable.vue          # 主组件（已扩展）
│   │   ├── cellComponents/               # 单元格组件目录
│   │   │   ├── CheckboxCell.vue         # 勾选框组件
│   │   │   ├── SwitchCell.vue           # 开关组件
│   │   │   ├── ButtonCell.vue           # 按钮组件
│   │   │   ├── index.js                 # 组件导出
│   │   │   └── README.md                # 详细文档
│   │   └── examples/
│   │       └── LogicBaseStationExample.vue  # 使用示例
│   └── BasicComponents/
│       └── Table/
│           └── index.vue                # 基础表格组件（已扩展）
└── views/
    └── test/
        └── CellComponentsTest.vue       # 功能测试页面
```

## 主要修改点

### 1. CommonQueryTable 组件
- 添加 `cellComponents` 属性
- 添加 `handleCellComponentEvent` 方法
- 将配置传递给 basic-table

### 2. BasicTable 组件
- 添加 `cellComponents` 属性
- 扩展单元格渲染逻辑，支持动态组件
- 注册内置单元格组件
- 添加事件传递机制

### 3. 新增单元格组件
- 统一的组件接口（value, row, column, index）
- 统一的事件机制（cellEvent）
- 灵活的属性配置

## 使用场景

### 1. 逻辑基站退网资源管理
- 设备拆除字段：勾选框控制
- 资源删除字段：勾选框控制
- 勾选后触发保存逻辑

### 2. 其他适用场景
- 状态开关控制
- 批量操作选择
- 快速操作按钮
- 数据验证和编辑

## 扩展性

### 1. 自定义组件
可以轻松创建新的单元格组件：

```vue
<template>
  <div class="custom-cell">
    <!-- 自定义内容 -->
  </div>
</template>

<script>
export default {
  name: 'CustomCell',
  props: {
    value: [String, Number, Boolean],
    row: Object,
    column: Object,
    index: Number
  },
  methods: {
    handleEvent() {
      this.$emit('cellEvent', 'customEvent', data, this.row, this.column);
    }
  }
}
</script>
```

### 2. 组件注册
在 `src/components/CommonQuery/cellComponents/index.js` 中添加新组件即可。

## 测试

运行测试页面：
```
访问 /test/cell-components-test 查看功能演示
```

## 注意事项

1. 确保组件名称在 basic-table 中正确注册
2. 所有单元格组件都会接收标准的 props
3. 事件统一通过 `cellEvent` 触发
4. 建议在事件处理中进行数据验证
5. 可以通过 `props` 配置传递额外属性

## 兼容性

- ✅ 完全向后兼容现有功能
- ✅ 不影响现有表格渲染
- ✅ 可选择性使用新功能
