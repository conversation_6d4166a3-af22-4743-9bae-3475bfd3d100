<template>
  <el-table
    :border='border'
    :cell-class-name='setCellClass'
    :data='tableData'
    :default-sort='{prop: "sort", order: "ascending"}'
    :height='height'
    :row-class-name='setRowStyles'
    :row-key='getRowKey'
    @cell-click='handleCellClick'
    @current-change='handleCurrentChange'
    @row-click='clickHandle'
    @row-contextmenu='handleRightClick'
    @row-dblclick='dbClickHandle'
    @selection-change='handleSelectionChange'
    @sort-change='handleSortChange'
    @expand-change="handleExpandChange"
    :expand-row-keys="expandedRows"
    highlight-current-row
    ref='table'
    size='mini'
    class="custom-table"
    :show-header="true"
    :header-cell-class-name="headerCellClassName"
    :header-row-class-name="headerRowClassName"
    :span-method="spanMethod"
  >
    <!-- 展开列 -->
    <el-table-column
      v-if="isExpand || expandQueryCode || subTableTitle.length"
      type="expand"
      :key="uuid()"
    >
      <template slot-scope="props">
        <component :is="expandComponent" v-if="isExpand" :row-data="props.row"
                   @contentUpdated="updateTableHeight"></component>
        <commonQueryTable v-else-if="expandQueryCode"
                          :class="`td_${props.row.id}`"
                          :style="{width: expandTableWith}"
                          :code='expandQueryCode'
                          :fixed-height="250"
                          :queryParams='Object.assign({},expandQueryParam, {parentId: props.row.parentId || props.row.id})'
                          :showExport="false"
                          :showDetail="false"
                          :showPage="true"></commonQueryTable>
        <el-table :data="props.row.subTable" v-else-if="subTableTitle.length" border>
          <el-table-column
            :fixed='item.fixed'
            :key='item.name'
            :label='item.title'
            :min-width='item.width || 180'
            :prop='item.name'
            :sortable='item.sortable'
            align='center'
            show-overflow-tooltip
            v-for='(item,index) in subTableTitle'
          >
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>

    <!-- 选择列 -->
    <el-table-column
      :reserve-selection='reserveSelection'
      :selectable='selectableCheckbox'
      type='selection'
      v-if='checkbox && tableTitle.length'
      width='45'
    />

    <!-- 序号列 -->
    <el-table-column
      align='center'
      label='序号'
      type='index'
      v-if='rowIndex'
      width='60'
    ></el-table-column>

    <!-- 数据列 -->
    <el-table-column
      v-if="!item.hasOwnProperty('fieldShow') || item.fieldShow"
      :fixed='item.fixed'
      :key='`item.name_${uuid()}`'
      :label='item.title'
      :min-width='item.width || 180'
      :width="type === 'expand' ? item.width || 'auto' : 'auto'"
      :prop='item.name'
      :sortable='item.sortable'
      align='center'
      :show-overflow-tooltip="!['wkt','geom', 'cover_geom'].includes(item.name)"
      v-for='(item,index) in tableTitle'
    >
      <template v-if='multiHeader && item.children'>
        <el-table-column
          :key='child.name'
          :label='child.title'
          :prop='child.name'
          align='center'
          v-for='child in item.children'
        ></el-table-column>
      </template>

      <template slot='header' slot-scope='scope'>
        {{item.title}}
        <el-tooltip
          effect='light'
          placement='bottom-start'
          popper-class='form-label-tooltip'
          v-if='item.tipInfo'
        >
          <div class='content-tip' slot='content'>
            <pre>{{ filterTipInfo(item.tipInfo) }}</pre>
          </div>
          <svg-icon icon-class='question1'></svg-icon>
        </el-tooltip>
      </template>

      <template slot-scope='scope'>
        <div v-if='insertColumn(index) || insertColumnName(item)'>
          <slot
            :column='index'
            :columnName='item.name'
            :index='scope.$index'
            :item='scope.row'
          ></slot>
        </div>
        <!-- 自定义单元格组件 -->
        <div v-else-if="cellComponents[item.name]">
          <component
            :is="cellComponents[item.name].component"
            :value="scope.row[item.name]"
            :row="scope.row"
            :column="item"
            :index="scope.$index"
            v-bind="cellComponents[item.name].props || {}"
            @cellEvent="handleCellComponentEvent"
          />
        </div>
        <a v-else-if="isFilePath(scope.row[item.name]) && showDownload"
           class="download-link"
           @click="updateLinkUrl(scope.row[item.name])">下载</a>

        <div v-else-if="['wkt','geom'].includes(item.name) || item.javaType == 'geometry' "
          @click="handleCovClick(scope.row,item.name)" class="wkt">
          <i class="el-icon-add-location" style="font-size: 16px; ; margin-right: 5px"></i>
          {{ scope.row[item.name] }}
        </div>
        <div v-else class="cell-content">
          <!-- 使用图标替换状态文字 -->
          <svg-icon
            :icon-class="getStatusIcon(scope.row[item.name + '_cell_status'])"
            class="status-icon"
          />
          <span v-if="item.dataType == 'date'">{{ parseTime(scope.row[item.name],'{y}-{m}-{d}') }}</span>
          <span v-else>{{ scope.row[item.name] }}</span>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import attachmentApi from '@/api/system/attachmentApi'
import {uuid} from "vue-contextmenujs/src/util";
import {commonQuery} from "@/api/kernel/query";
import cellComponents from '@/components/CommonQuery/cellComponents';

const dynamicComponents = {}
const components = require.context('./components', true, /\.vue$/)
components.keys().forEach((key) => {
  const component = components(key).default
  dynamicComponents[component.name] = component
})
export default {
  name: 'basicTable',
  components: {
    ...dynamicComponents,
    ...cellComponents,
    commonQueryTable: () => import('@/components/CommonQuery/commonQueryTable'),
  },
  props: {
    type: {
      type: String,
      default: '',
      required: false
    },
    showDownload: {
      type: Boolean,
      default: false,
    },
    expandComponent: {
      type: String,
      default: 'div',
    },
    expandQueryCode: {
      type: String
    },
    expandQueryParam:{
      type: Object,
      default: () => {},
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    tableTitle: {
      type: Array,
      default: () => [],
    },
    subTableTitle: {
      type: Array,
      default: () => [],
    },
    height: Number,
    insertIndex: [Number, Array],
    insertName: [String, Array],
    checkbox: Boolean,
    radio: Boolean,
    border: Boolean,
    isExpand: {
      type: Boolean,
      default: false,
    },
    rowIndex: {
      type: Boolean,
      default: false,
    },
    multiHeader: {
      type: Boolean,
      default: false,
    },
    reserveSelection: {
      type: Boolean,
      default: false,
    },
    rowKeyId: {
      type: String,
    },
    'set-row-style': {
      type: Function,
    },
    showCov: {
      type: Boolean,
      default: false
    },
    cellComponents: {
      type: Object,
      default: () => ({})
    },
    spanMethod: {
      type: Function
    }
  },
  data() {
    return {
      expandTableWith : 'auto',
      expandedRows: [],
      // 添加状态配置
      statusConfig: {
        hot: { text: '热', class: 'hot' },
        new: { text: '新', class: 'new' },
        urgent: { text: '急', class: 'urgent' },
        boom: { text: '爆', class: 'boom' }
      }
    }
  },
  created() {
    this.queryExpandTableWidth()
  },
  computed: {
    filteredTips() {
      return this.tableTitle.reduce((acc, item) => {
        acc[item.name] = this.filterTipInfo(item.tipInfo);
        return acc;
      }, {});
    }
  },
  methods: {
    uuid,
    filterTipInfo(tipInfo) {
      if (!tipInfo) return '';

      // 定义需要移除的状态文本（可扩展）
      const statusTexts = [
        ...Object.values(this.statusConfig).map(status => status.text),
        // 如果有其他需要移除的特定词，可以在这里添加
        // 例如：'紧急', '重要'
      ];

      // 使用正则表达式移除状态文本
      const regex = new RegExp(statusTexts.join('|'), 'g');
      let filtered = tipInfo.replace(regex, '');

      // 清理多余的空格和换行
      filtered = filtered.replace(/\s+/g, ' ').trim();

      return filtered;
    },
    // 添加表头行类名方法
    headerRowClassName() {
      return 'table-header-row'
    },

    // 添加表头单元格类名方法
    headerCellClassName() {
      return 'table-header-cell'
    },

    // 优化滚动处理
    setupScrollOptimization() {
      const tableBody = this.$el.querySelector('.el-table__body-wrapper')
      const tableHeader = this.$el.querySelector('.el-table__header-wrapper')

      if (!tableBody) return

      let scrollTimeout
      let isHorizontalScrolling = false

      // 处理横向滚动
      tableBody.addEventListener('scroll', () => {
        if (isHorizontalScrolling) return

        const scrollLeft = tableBody.scrollLeft
        if (tableHeader && tableHeader.scrollLeft !== scrollLeft) {
          isHorizontalScrolling = true
          tableHeader.scrollLeft = scrollLeft

          // 添加滚动中的类名
          this.$el.classList.add('is-scrolling-horizontally')

          clearTimeout(scrollTimeout)
          scrollTimeout = setTimeout(() => {
            isHorizontalScrolling = false
            this.$el.classList.remove('is-scrolling-horizontally')
            this.debouncedStyleUpdate()
          }, 150)
        }
      }, { passive: true }) // 添加 passive 标志提高性能
    },
    handleExpandChange(row, expandedRows) {
      if (expandedRows.length > 1) {
        this.$refs.table.toggleRowExpansion(expandedRows[0])
      }
    },
    // 获取状态图标
    getStatusIcon(status) {
      return this.statusConfig[status]?.class || '';
    },


    queryExpandTableWidth() {
      if (this.expandQueryCode) {
        commonQuery('queryExpandTableWith', { queryCode: this.expandQueryCode }).then(res => {
          if (res.data[0].tablewith === 0) {
            this.expandTableWith = 'auto'
          } else {
            this.expandTableWith = `${res.data[0].tablewith + 70}px`
          }
        })
      }
    },
    updateTableHeight() {
      console.log('updateTableHeight')
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    },
    updateLinkUrl(preUrl) {
      attachmentApi.downloadUrl(preUrl, "a36bd3df629242c4802ffe7defb211a9_nip_templates.zip")
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    handleSortChange(column) {
      this.$emit('handleSortChange', column)
    },
    handleRightClick(row, column, event) {
      this.$emit('handleRightClick', row, column, event)
    },
    setRowStyles({ row, rowIndex }) {
      let style = '';
      if (typeof this.setRowStyle === 'function') {
        style = this.setRowStyle(row, rowIndex);
      }
      if (row.row_bg_color) {
        style += ` custom-row-${rowIndex}`;
        this.addCustomStyle(rowIndex, row.row_bg_color, row.row_font_color);
      }
      return style;
    },
    setCellClass({ row, column, rowIndex, columnIndex }) {
      row.index = rowIndex;
      column.index = columnIndex;

      if (row.row_font_color) {
        return `custom-cell-${rowIndex}`;
      }
      return '';
    },
    addCustomStyle(rowIndex, bgColor, fontColor) {
      const styleId = `custom-row-style-${rowIndex}`;
      let styleElement = document.getElementById(styleId);

      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = styleId;
        document.head.appendChild(styleElement);
      }

      // 计算hover时的背景色和字体颜色
      const hoverBgColor = this.adjustColor(bgColor, -10); // 背景色暗化10%
      const hoverFontColor = this.adjustColor(fontColor || '#606266', -15); // 字体颜色暗化15%

      const style = `
        .el-table .custom-row-${rowIndex} {
          background-color: ${bgColor} !important;
        }
        .el-table .custom-cell-${rowIndex} {
          color: ${fontColor || '#606266'} !important;
        }
        .el-table .custom-row-${rowIndex}:hover > td {
          background-color: ${hoverBgColor} !important;
          color: ${hoverFontColor} !important;
        }
        .el-table .custom-row-${rowIndex}:hover .cell-content span {
          color: ${hoverFontColor} !important;
        }
      `;

      styleElement.innerHTML = style;
    },

    // 改进的颜色调整方法
    adjustColor(color, percent) {
      if (!color) return color;

      // 处理rgba颜色
      if (color.startsWith('rgba')) {
        const rgba = color.match(/[\d.]+/g);
        if (rgba.length === 4) {
          const r = this.adjustColorValue(parseInt(rgba[0]), percent);
          const g = this.adjustColorValue(parseInt(rgba[1]), percent);
          const b = this.adjustColorValue(parseInt(rgba[2]), percent);
          return `rgba(${r},${g},${b},${rgba[3]})`;
        }
        return color;
      }

      // 处理十六进制颜色
      if (color.startsWith('#')) {
        let R = parseInt(color.substring(1,3), 16);
        let G = parseInt(color.substring(3,5), 16);
        let B = parseInt(color.substring(5,7), 16);

        R = this.adjustColorValue(R, percent);
        G = this.adjustColorValue(G, percent);
        B = this.adjustColorValue(B, percent);

        const RR = ((R.toString(16).length==1) ? "0"+R.toString(16) : R.toString(16));
        const GG = ((G.toString(16).length==1) ? "0"+G.toString(16) : G.toString(16));
        const BB = ((B.toString(16).length==1) ? "0"+B.toString(16) : B.toString(16));

        return "#"+RR+GG+BB;
      }

      return color;
    },

    // 辅助方法：调整单个颜色值
    adjustColorValue(color, percent) {
      const newValue = Math.floor(color * (100 + percent) / 100);
      return Math.min(255, Math.max(0, newValue));
    },
    handleCellClick(row, column, cell, event) {
      this.$emit('cellClick', row, column, cell)
    },
    handleSelectionChange(val) {
      if (val.length > 1 && this.radio) {
        this.$refs.table.clearSelection()
        this.$refs.table.toggleRowSelection(val.pop())
      } else {
        this.$emit('handleSelectionChange', val)
      }
    },
    dbClickHandle(row, column, event) {
      this.$emit('dblclick', row)
    },
    clickHandle(row, column, event) {
      this.$emit('rclick', row, column, event)
    },
    isFilePath(str) {
      const fileRegex = /^\/attement.*\/[^/]+$/;
      return fileRegex.test(str);
    },
    insertColumn(index) {
      if (Array.isArray(this.insertIndex)) {
        return this.insertIndex.includes(index)
      } else if (typeof this.insertIndex == 'number') {
        return index === this.insertIndex
      }
    },
    insertColumnName(item) {
      if (Array.isArray(this.insertName)) {
        return this.insertName.includes(item.name)
      } else if (typeof this.insertName == 'string') {
        return item.name === this.insertName
      }
    },
    selectableCheckbox(row, index) {
      return !row.disabled
    },
    selectedRow(row, selected = true) {
      this.$nextTick(() => {
        row && this.$refs.table.toggleRowSelection(row, selected)
      })
    },
    getRowKey(row) {
      if (this.rowKeyId) return row[this.rowKeyId]
      if (row.id) return row.id
      return Object.keys(row)[0] + Math.random() * 10000 + ''
    },

    clearSelection() {
      this.$refs.table.clearSelection()
    },
    handleCovClick(row,key) {
      this.$emit('covclick', row, key)
    },
    // 处理单元格组件事件
    handleCellComponentEvent(eventName, data, row, column) {
      this.$emit('cellComponentEvent', eventName, data, row, column);
    },
  },
}
</script>

<style lang='scss' scoped>
.custom-table {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  // 优化滚动时的性能
  &.is-scrolling {
    pointer-events: none;

    ::v-deep {
      .cell-content {
        transition: none !important;
      }

      //.status-tag {
      //  animation: none !important;
      //}
    }
  }
  // 添加骨架屏效果
  &.is-loading {
    ::v-deep {
      .el-table__body-wrapper {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
              90deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.6) 50%,
              rgba(255, 255, 255, 0) 100%
          );
          animation: shimmer 1.5s infinite;
        }
      }
    }
  }
  ::v-deep {
    // 优化表头固定效果
    .el-table__header-wrapper {
      z-index: 3; // 确保表头在最上层

      th {
        background-color: #f5f7fa !important;
        position: relative; // 添加相对定位
        z-index: 2;

        // 优化表头单元格
        &.table-header-cell {
          transition: none; // 禁用过渡效果
          will-change: transform; // 提示浏览器优化
          transform: translateZ(0); // 强制硬件加速
        }
      }
    }

    // 优化固定列
    .el-table__fixed,
    .el-table__fixed-right {
      z-index: 2;
      background-color: #fff;

      // 固定列的表头
      .el-table__fixed-header-wrapper {
        z-index: 3;
      }

      // 防止固定列闪烁
      &::before {
        display: none;
      }
    }

    // 滚动时的优化
    &.is-scrolling-horizontally {
      // 滚动时禁用所有动画
      * {
        transition: none !important;
      }

      // 滚动时的表头样式
      .el-table__header-wrapper {
        th {
          background-color: #f5f7fa !important;
        }
      }
    }

    // 优化表格内容区域
    .el-table__body-wrapper {
      overflow-x: auto;
      overflow-y: auto;

      // 优化滚动条样式
      &::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 3px;

        &:hover {
          background: #c0c4cc;
        }
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
      }
    }

    // 确保边框对齐
    .el-table__cell {
      border-right: 1px solid #EBEEF5;

      .cell {
        white-space: pre-line;
      }
    }
    // 表头样式
    th {
      background-color: #f5f7fa !important;
      color: #606266;
      font-weight: 600;
      padding: 12px 0;
      border-bottom: 1px solid #EBEEF5;
      transition: background-color 0.3s;

      &:hover {
        background-color: #eef1f6 !important;
      }
    }

    // 优化hover效果的过渡
    td, td .cell-content span {
      transition: all 0.3s ease;
    }

    //// 确保状态标签在hover时保持原样
    //.status-tag {
    //  transition: none;
    //}
    // 单元格内容布局
    .cell-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    // 单元格样式
    td {
      padding: 8px 0;
      transition: all 0.3s;
    }

    // 斑马纹
    .el-table__row--striped td {
      background-color: #FAFAFA;
    }

    // 鼠标悬停效果
    .el-table__row:hover td {
      background-color: #f5f7fa;
    }

    // 选中行样式
    .current-row td {
      background-color: #ecf5ff !important;
    }

    // 固定列样式
    .el-table__fixed-right::before,
    .el-table__fixed::before {
      background-color: #EBEEF5;
    }

    // 表格边框
    .el-table--border {
      border: 1px solid #EBEEF5;

      &::after {
        background-color: #EBEEF5;
      }
    }
  }
}

// 状态标识基础样式
//.status-tag {
//  padding: 2px 6px;
//  border-radius: 10px;
//  font-size: 12px;
//  font-weight: bold;
//  line-height: 1;
//  transform: scale(0.8);
//  white-space: nowrap;
//}


// 热门状态
//.status-hot {
//  background: linear-gradient(135deg, #ff4e50, #f9d423);
//  color: white;
//}
//
//// 新内容状态
//.status-new {
//  background: linear-gradient(135deg, #56ab2f, #a8e063);
//  color: white;
//}
//
//// 紧急状态
//.status-urgent {
//  background: linear-gradient(135deg, #cb2d3e, #ef473a);
//  color: white;
//}
//
//// 爆热状态
//.status-boom {
//  background: linear-gradient(135deg, #f85032, #e73827);
//  color: white;
//}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}


// 添加打印媒体查询
@media print {
  .custom-table {
    ::v-deep {
      .el-table__header-wrapper,
      .el-table__fixed-header-wrapper {
        position: static !important;
      }

      .el-table__fixed,
      .el-table__fixed-right {
        position: static !important;
      }
    }
  }
}

// 下载链接样式
.download-link {
  color: #409EFF;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
}

// 工具提示样式
.form-label-tooltip {
  max-width: 200px;
  line-height: 1.4;
}

.content-tip {
  white-space: pre-wrap;
  word-break: break-all;
}

// 对齐修正
.is-left {
  text-align: center;
}

.el-table-column--selection .cell {
  padding-left: 14px;
}

.wkt {
  color: #1890ff;
  cursor: pointer;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 限制行数为3 */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 调整展开内容的 z-index 使其高于 fixed 列 */
.el-table__expanded-cell {
  z-index: 10;
  position: relative;
  padding: 0 !important;
  background: #fff;
}

/* 调整展开表格的 fixed 列 z-index */
.el-table__expanded-cell .el-table__fixed {
  z-index: 11;
}

/* 展开表格样式 */
.expand-table {
  width: 100%;
  margin: 0px;
}
</style>
