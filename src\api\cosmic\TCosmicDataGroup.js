import request from '@/utils/request'

// 查询cosmic数据组列表
export function listTCosmicDataGroup(query) {
  return request({
    url: '/cosmic/dataGroup/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic数据组详细
export function getTCosmicDataGroup(id) {
  return request({
    url: '/cosmic/dataGroup/' + id,
    method: 'get'
  })
}

// 新增cosmic数据组
export function addTCosmicDataGroup(data) {
  return request({
    url: '/cosmic/dataGroup',
    method: 'post',
    data: data
  })
}

// 修改cosmic数据组
export function updateTCosmicDataGroup(data) {
  return request({
    url: '/cosmic/dataGroup',
    method: 'put',
    data: data
  })
}

// 删除cosmic数据组
export function delTCosmicDataGroup(id) {
  return request({
    url: '/cosmic/dataGroup/' + id,
    method: 'delete'
  })
}