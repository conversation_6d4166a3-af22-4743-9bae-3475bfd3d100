<template>
  <div class='upload-file'>
    <el-upload
      :action='uploadFileUrl'
      :before-upload='handleBeforeUpload'
      :data='extendParam'
      :disabled='disabled'
      :file-list='fileList'
      :headers='headers'
      :limit='limit'
      :multiple='multiple'
      :on-error='handleUploadError'
      :on-exceed='handleExceed'
      :on-success='handleUploadSuccess'
      :show-file-list='false'
      :accept='accept'
      class='upload-file-uploader'
      ref='upload'
    >
      <!-- 上传按钮 -->
      <div class='el-upload__hooker' slot='trigger' v-if="!isUploadPic">
        <el-button v-if="btnType == 'btn'" :loading='isUpload' :icon="uploadIcon" size='mini' type='primary'>{{triggerButtonTitle}}</el-button>
        <span v-if="btnType == 'span'">{{triggerButtonTitle}}</span>
      </div>
      <div class='el-upload__hooker image' slot='trigger' v-if="isUploadPic">
        <div >
          <span class='file-required' v-if='fileRequired'>*</span>
          {{fileTitle}}
        </div>
        <i class='el-icon-plus' />
        <img :src='fileUrl' v-if='fileUrl' style="display: block;z-index: 1" />
        <span class="el-upload-list__item-actions" v-show="fileUrl">
          <span class="upload-icon">
              <span class="el-uploads-span" style="margin-right: 10px;" @click.stop="dialogVisible = true">
                  <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-span" @click.stop="removeImageFile">
                  <i class="el-icon-delete"></i>
              </span>
          </span>
        </span>
      </div>

      <el-dialog :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
        <img :src="fileUrl" style="display: block; max-width: 100%; margin: 0 auto;">
      </el-dialog>

      <!-- 上传提示 -->
      <div class='el-upload__tip' slot='tip' v-if='showTip'>
        请上传
        <template v-if='fileSize'>
          大小不超过
          <b style='color: #f56c6c'>{{ fileSize }}MB</b>
      </template>
        <template v-if='fileType'>
        格式为
          <b style='color: #f56c6c'>{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      class='upload-file-list el-upload-list el-upload-list--text'
      name='el-fade-in-linear'
      tag='ul'
    >
      <li
        :key='file.uid'
        class='el-upload-list__item ele-upload-list__item-content'
        v-for='(file, index) in list'
      >
        <el-link :href='file.url' :underline='false' target='_blank'>
          <span class='el-icon-document'>{{ getFileName(file.name) }}</span>
        </el-link>
        <div class='ele-upload-list__item-content-action'>
          <el-link
            :underline='false'
            @click='handleDelete(index)'
            type='danger'
          >删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import {getToken} from '@/utils/auth'
import {compress} from 'image-conversion'
import { imagesType } from '@/utils/plan/static'
import request from '@/utils/request'
import { encryptData } from '@/utils/sm4encrypt'

export default {
  name: 'FileUpload',
  props: {
    // 值
    value: [String, Object, Array],
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 300,
    },
    //文件数量
    limit: {
      type: Number,
      default: 50,
    },
    multiple: {
      type: Boolean,
      default: false
    },
    fileType: {
      // 文件类型白名单
      type: Array,
      default: () => ["doc","docx", "csv","xls", "xlsx", "dwg", "ppt","pptx", "txt","wps", "pdf","zip","rar", ...new Set(imagesType.map(i => i.toLowerCase()))],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    isUploadPic:{
      type: Boolean,
      default: false,
    },
    extendParam: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    triggerButtonTitle: {
      type: String,
      default: '选取文件',
    },
    btnType:{
      type: String,
      default: 'btn'
    },
    fileTitle:{
      type: String,
    },
    fileRequired:{
      type: Boolean,
      default: false,
    },
    fileUrl:{
      type: String,
    },
    uploadIcon: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      default: null
    },
    accept: {
      type: String,
      default: '*/*'
    },
    // 图片上传压缩 超过(2MB)规划进行压缩
    compress: {
      type: [Boolean, Number],
      default: 2
    },
    //弹窗确认
    beforeUploadConfirm: {
      type: Function
    }
  },
  data() {
    return {
      uploadFileUrl:process.env.VUE_APP_BASE_API + '/system/common/upload', // 上传的图片服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken(),
        reqType: 'back',
        ip: process.env.VUE_APP_LOCAL_IP

      },
      isUpload: false,
      fileList: [],
      dialogVisible: false
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },
    // 列表
    list() {
      let temp = 1
      if (this.value) {
        // 首先将值转为数组
        const list = Array.isArray(this.value) ? this.value : [this.value]
        // 然后将数组转为对象数组
        return list.map((item) => {
          if (typeof item === 'string') {
            item = { name: item, url: item }
          }
          item.uid = item.uid || new Date().getTime() + temp++
          return item
        })
      } else {
        this.fileList = []
        return []
      }
    },
  },
  methods: {
    customUpload({ file, upSuccess, upError }) {
			this.loading = true
      // 读取文件内容
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => {
		// 使用FormData构造请求体
		let formData =  new FormData();
		formData.append('fileName', file.name);
    // 遍历对象的属性和值，并将它们添加到FormData中
    for (let key in this.extendParam) {
      if (this.extendParam.hasOwnProperty(key)) {
        formData.append(key, this.extendParam[key]);
      }
    }
		if(this.$store.getters.sysconfig.IS_API_ApiEncrypt === 'true'){
			// 对文件进行加密（这里以sm4加密为例）
			const encryptedFileContent =  encryptData(reader.result)
			formData.append('fileKey', new Blob([encryptedFileContent], { type: 'text/plain' }));
			// formData.append('SM4String', encryptedFileContent);

		}else{
			formData.append('file', file);
		}
        // 发送请求到服务器
        request(this.uploadFileUrl, {
			method: 'post',
			data: formData,
			}).then(response => {
				this.loading = false
				if(response.code == 200){
					this.handleUploadSuccess(response, file)
				}else{
					this.handleUploadError(response)
				}
			}).catch(err =>{
        this.$message.error('上传失败:',err)
        this.$emit('uploadError')
      })
			};
			reader.onerror = (error) => {
				this.loading = false
				console.log('文件读取失败');
			};
		},
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      //添加参数
      if(this.extendParam){
        this.extendParam.fileNameSuffix=file['name'];//带后缀的文件名，不能用fileName,会导致其它功能下载时格式后缀重复
      }
      const fileSize = file.size / 1024 / 1024
      let fileExtension = ''
      // 校检文件类型
      if (this.fileType) {
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
          fileExtension = fileExtension && fileExtension.toLowerCase()
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
          )
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = fileSize < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }

      this.isUpload = true
      this.$emit('beforeUpload')
      return new Promise((resolve, reject) => {
        if(this.beforeUploadConfirm) {
          this.beforeUploadConfirm().then(result => {
            result ? this.compressFile(file,fileSize,fileExtension).then(r=>{
              resolve()
            }) : reject(result)
          }).catch(e => {
            console.log('catch', e)
            reject(e)
          })
        }else{
          this.compressFile(file,fileSize,fileExtension).then(r=>{
            resolve()
          })
        }
      })
    },
    compressFile(file,fileSize,fileExtension){
      return new Promise((resolve, reject) => {
        if (this.compress && this.compress < fileSize &&
          fileExtension && this.imagesType.includes(fileExtension)) {
          compress(file,{
            quality: 0.6,
            type: file.type,
            scale: 0.6,
          }).then(res => resolve(res))
        }else{
          resolve()
        }
      })
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`只允许上传单个文件`)
      this.isUpload = false
    },
    // 上传失败
    handleUploadError(err) {
      this.$message.error('上传失败:',err.msg)
      this.$emit('uploadError')
      this.isUpload = false
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.isUpload = false
      if (res.code == 200) {
        this.$message.success(res.msg ? res.msg : '上传成功')
        this.$emit('input', res.data)
      } else if (res.code === 707) {
        if (!window.__bindWindowClose__) {
          window.__closeCurrentMsg__ = () => {
            let $message = window.__current_msg_dlg__;
            if (!!$message) {
              $message.close();
            }
          }
          window.__bindWindowClose__ = true;
        }
        window.__current_msg_dlg__ = this.$message({
          message: `${res.msg} ${res.data}`,
          type: 'error',
          showClose: true,
          dangerouslyUseHTMLString: true,
          duration: 0
        });
      } else if (res.code === 708) { // handle it where event received
        this.$emit('input', res)
      } else {
        this.$message.error(res.msg ? res.msg : '上传异常!')
        this.$emit('uploadError')
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
      this.$emit('input', '')
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1).toLowerCase()
      } else {
        return ''
      }
    },
    removeImageFile(){
      this.$emit('input', "")
    }
  },
  created() {
    this.fileList = this.list
    if(this.uploadUrl) this.uploadFileUrl=process.env.VUE_APP_BASE_API+  this.uploadUrl
  },
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
  ::v-deep {
    .el-upload{
      width: 100%;
    }
  }
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.el-upload-list__item-actions:hover span {
  display: inline-block;
}
// .el-icon-zoom-in:before {
//     content: "\E626";
// }
// .el-icon-delete:before {
//     content: "\E612";
// }
.el-upload-list__item-actions:hover {
    opacity: 1;
}
.el-upload-list__item-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0,0,0,.5);
    transition: opacity .3s;
    z-index: 6
}
.upload-icon {
    position: absolute;
    top: 50%;
    margin-left: -28px;
    margin-top: -18px;
}
</style>
