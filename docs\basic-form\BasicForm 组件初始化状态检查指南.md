# BasicForm 组件初始化状态检查指南

## 概述

本指南帮助检查和诊断 BasicForm 组件在初始化时各字段的渲染状态，特别是级联字段、必填字段和显示隐藏字段的初始状态。

## 初始化流程

### 1. 组件初始化顺序

```
1. created() 生命周期
   ├── createModelAndRules() - 创建表单模型和验证规则
   ├── initializeConditionalRequired() - 初始化条件必填状态
   └── updateFormRules() - 更新表单验证规则

2. mounted() 生命周期
   ├── 触发级联事件 (如果有初始值)
   └── 强制更新视图
```

### 2. 字段初始化检查点

#### 2.1 基础字段属性检查

**必需属性**:
```javascript
{
  name: 'fieldName',        // ✅ 字段名称
  title: '字段标题',         // ✅ 字段标题
  formType: 'input',        // ✅ 字段类型
  formEdit: true,           // ✅ 是否可编辑
  show: true,               // ✅ 是否显示
  initHide: false,          // ✅ 初始隐藏状态
  required: false           // ✅ 是否必填
}
```

#### 2.2 条件必填字段检查

**配置示例**:
```javascript
{
  name: 'conditionalField',
  conditionRequired: JSON.stringify({
    rule: "data.triggerField === 'value'",
    tipMsg: '当触发字段为某值时必填'
  })
}
```

**检查要点**:
- ✅ `conditionRequired` 配置格式正确
- ✅ `rule` 表达式语法正确
- ✅ 依赖的字段存在且有值
- ✅ 初始化时正确计算必填状态

#### 2.3 级联选择字段检查

**配置示例**:
```javascript
{
  name: 'childField',
  cascadeCode: 'parentField',  // 父字段名称
  children: [],                // 初始选项为空
  href: '/api/cascade',        // 数据源URL (可选)
  dataSource: '/api/data'      // 数据源URL (可选)
}
```

**检查要点**:
- ✅ `cascadeCode` 指向的父字段存在
- ✅ 父字段有初始值时，子字段选项正确加载
- ✅ 父字段无值时，子字段选项为空
- ✅ 级联层级关系正确

#### 2.4 显示隐藏字段检查

**配置示例**:
```javascript
{
  name: 'triggerField',
  param2: JSON.stringify({
    'value1': { show: 'field1,field2', hide: 'field3' },
    'value2': { show: 'field3', hide: 'field1,field2' }
  })
}
```

**检查要点**:
- ✅ `param2` 配置格式正确
- ✅ 显示/隐藏的字段名称正确
- ✅ 初始值对应的显示状态正确
- ✅ 字段的 `initHide` 状态正确

## 常见初始化问题

### 1. 必填字段问题

#### 问题现象
- 条件必填字段在初始化时不显示红色星号
- 必填状态与实际条件不符

#### 排查步骤
1. **检查字段配置**:
   ```javascript
   // 确认 conditionRequired 配置正确
   console.log('字段配置:', field.conditionRequired)
   ```

2. **检查条件表达式**:
   ```javascript
   // 确认 rule 表达式能正确执行
   const config = JSON.parse(field.conditionRequired)
   console.log('条件结果:', this.validRequire(config.rule))
   ```

3. **检查计算属性**:
   ```javascript
   // 确认计算属性返回正确值
   console.log('计算必填状态:', this.computedFieldsRequired[field.name])
   ```

#### 解决方案
- 确保 `initializeConditionalRequired()` 在初始化时被调用
- 检查 `validRequire()` 方法的实现
- 确认表单数据在计算时已经存在

### 2. 级联选择问题

#### 问题现象
- 子级联字段没有选项
- 级联数据加载失败
- 级联层级关系错误

#### 排查步骤
1. **检查级联配置**:
   ```javascript
   // 确认级联关系配置正确
   console.log('级联字段:', field.cascadeCode)
   console.log('数据源:', field.href || field.dataSource)
   ```

2. **检查父字段值**:
   ```javascript
   // 确认父字段有值
   console.log('父字段值:', this.form[field.cascadeCode])
   ```

3. **检查数据加载**:
   ```javascript
   // 确认数据源可访问
   console.log('级联数据:', field.children)
   ```

#### 解决方案
- 确保级联字段的 `cascadeCode` 正确
- 检查数据源URL是否可访问
- 确认级联数据格式正确

### 3. 显示隐藏问题

#### 问题现象
- 字段显示状态与配置不符
- 条件变化时字段不显示/隐藏

#### 排查步骤
1. **检查显示配置**:
   ```javascript
   // 确认 param2 配置正确
   console.log('显示配置:', field.param2)
   ```

2. **检查字段状态**:
   ```javascript
   // 确认字段显示状态
   console.log('字段显示:', this.controlShowField(field))
   console.log('initHide:', field.initHide)
   console.log('show:', field.show)
   ```

#### 解决方案
- 确保 `param2` 配置格式正确
- 检查字段的 `show` 和 `initHide` 属性
- 确认 `setFieldConfigHidden()` 方法正确执行

## 调试工具

### 1. 控制台调试命令

```javascript
// 检查字段配置
console.log('所有字段:', this.fields)

// 检查表单数据
console.log('表单数据:', this.form)

// 检查验证规则
console.log('验证规则:', this.rules)

// 检查计算属性
console.log('必填状态:', this.computedFieldsRequired)
console.log('显示状态:', this.computedFieldsVisible)
console.log('禁用状态:', this.computedFieldsDisabled)
console.log('字段类型:', this.computedFieldsType)
```

### 2. 测试页面调试

在测试页面中添加调试信息：

```html
<div class="debug-panel">
  <h4>字段状态调试</h4>
  <div v-for="field in fields" :key="field.name">
    <p><strong>{{ field.name }}:</strong></p>
    <p>显示: {{ controlShowField(field) }}</p>
    <p>必填: {{ getFieldRequired(field) }}</p>
    <p>禁用: {{ hasEditTwiceJudge(field) }}</p>
    <p>类型: {{ controlTypeField(field) }}</p>
  </div>
</div>
```

## 最佳实践

### 1. 字段配置规范

- 确保所有必需属性都有正确的值
- 使用标准的配置格式
- 避免循环依赖

### 2. 初始化顺序

- 先设置基础属性
- 再处理条件逻辑
- 最后更新验证规则

### 3. 错误处理

- 添加配置验证
- 提供降级方案
- 记录错误日志

### 4. 性能优化

- 避免不必要的计算
- 使用缓存机制
- 合理使用防抖

## 测试检查清单

### 初始化检查
- [ ] 所有字段正常显示
- [ ] 必填标识正确显示
- [ ] 级联字段选项正确
- [ ] 显示隐藏状态正确
- [ ] 验证规则同步

### 交互检查
- [ ] 条件必填实时更新
- [ ] 级联选择正常工作
- [ ] 显示隐藏实时响应
- [ ] 字段类型正确切换
- [ ] 表单验证准确

### 性能检查
- [ ] 初始化速度正常
- [ ] 无内存泄漏
- [ ] 无重复请求
- [ ] 响应速度良好
