import { getConfigKey } from '@/api/system/config'

/**
 * 资源配置助手类
 * 用于获取和管理资源库配置和工单按钮配置
 */
class ResourceConfigHelper {
  constructor() {
    this.resourceConfig = null
    this.buttonConfig = null
    this.configCache = new Map()
    this.cacheExpiry = 5 * 60 * 1000 // 5分钟缓存过期时间
  }

  /**
   * 获取资源库配置
   * @returns {Promise<Object>} 资源库配置对象
   */
  async getResourceLibraryConfig() {
    const cacheKey = 'RESOURCE_LIBRARY_CONFIG'

    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      const cached = this.configCache.get(cacheKey)
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data
      }
    }

    try {
      const response = await getConfigKey(cacheKey)
      if (response.code === 200 && response.msg) {
        const config = JSON.parse(response.msg)

        // 缓存配置
        this.configCache.set(cacheKey, {
          data: config,
          timestamp: Date.now()
        })

        this.resourceConfig = config
        return config
      }
    } catch (error) {
      console.error('获取资源库配置失败:', error)
    }

    // 返回默认配置
    return this.getDefaultResourceConfig()
  }

  /**
   * 获取工单按钮配置
   * @returns {Promise<Object>} 按钮配置对象
   */
  async getOrderButtonConfig() {
    const cacheKey = 'ORDER_BUTTON_CONFIG'

    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      const cached = this.configCache.get(cacheKey)
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data
      }
    }

    try {
      const response = await getConfigKey(cacheKey)
      if (response.code === 200 && response.data) {
        const config = JSON.parse(response.data)

        // 缓存配置
        this.configCache.set(cacheKey, {
          data: config,
          timestamp: Date.now()
        })

        this.buttonConfig = config
        return config
      }
    } catch (error) {
      console.error('获取按钮配置失败:', error)
    }

    // 返回默认配置
    return this.getDefaultButtonConfig()
  }

  /**
   * 检查资源是否可以添加
   * @param {string} libraryType - 库类型 (demand/planning/design)
   * @param {string} resourceType - 资源类型
   * @returns {Promise<boolean>}
   */
  async canAddResource(libraryType, resourceType) {
    const config = await this.getResourceLibraryConfig()
    return config[libraryType]?.[resourceType]?.canAdd ?? true
  }

  /**
   * 检查资源是否可以删除
   * @param {string} libraryType - 库类型 (demand/planning/design)
   * @param {string} resourceType - 资源类型
   * @returns {Promise<boolean>}
   */
  async canDeleteResource(libraryType, resourceType) {
    const config = await this.getResourceLibraryConfig()
    return config[libraryType]?.[resourceType]?.canDelete ?? true
  }

  /**
   * 检查按钮是否应该显示
   * @param {string} buttonCode - 按钮代码
   * @returns {Promise<boolean>}
   */
  async shouldShowButton(buttonCode) {
    const config = await this.getOrderButtonConfig()
    return config[buttonCode] ?? true
  }

  /**
   * 过滤按钮列表，只返回应该显示的按钮
   * @param {Object} buttons - 原始按钮对象
   * @returns {Promise<Object>} 过滤后的按钮对象
   */
  async filterButtons(buttons) {
    const config = await this.getOrderButtonConfig()
    const filteredButtons = {}

    for (const [key, value] of Object.entries(buttons)) {
      if (config[key] !== false) {
        filteredButtons[key] = value
      }
    }

    return filteredButtons
  }

  /**
   * 获取资源操作权限
   * @param {string} libraryType - 库类型
   * @param {string} resourceType - 资源类型
   * @returns {Promise<Object>} 权限对象 {canAdd, canDelete}
   */
  async getResourcePermissions(libraryType, resourceType) {
    const config = await this.getResourceLibraryConfig()
    const resourceConfig = config[libraryType]?.[resourceType]

    return {
      canAdd: resourceConfig?.canAdd ?? true,
      canDelete: resourceConfig?.canDelete ?? true
    }
  }

  /**
   * 批量获取多个资源的权限
   * @param {string} libraryType - 库类型
   * @param {Array<string>} resourceTypes - 资源类型数组
   * @returns {Promise<Object>} 权限映射对象
   */
  async getBatchResourcePermissions(libraryType, resourceTypes) {
    const config = await this.getResourceLibraryConfig()
    const permissions = {}

    resourceTypes.forEach(resourceType => {
      const resourceConfig = config[libraryType]?.[resourceType]
      permissions[resourceType] = {
        canAdd: resourceConfig?.canAdd ?? true,
        canDelete: resourceConfig?.canDelete ?? true
      }
    })

    return permissions
  }

  /**
   * 清除配置缓存
   */
  clearCache() {
    this.configCache.clear()
    this.resourceConfig = null
    this.buttonConfig = null
  }

  /**
   * 获取默认资源配置
   * @returns {Object}
   */
  getDefaultResourceConfig() {
    const libraries = ['XQK', 'GHK', 'SJK']
    const resources = [
      'enodeb', 'baseBand', 'cell', 'rru',
      'antenna', 'platform', 'equipmentRoom', 'rooftop', 'supporting',
      'rack', 'boardCard', 'extendDevice'
    ]

    const config = {}
    libraries.forEach(library => {
      config[library] = {}
      resources.forEach(resource => {
        config[library][resource] = {
          canAdd: true,
          canDelete: true
        }
      })
    })

    return config
  }

  /**
   * 获取默认按钮配置
   * @returns {Object}
   */
  getDefaultButtonConfig() {
    return {
      approve: true,
      reject: true,
      transfer: true,
      return: true,
      save: true,
      submit: true,
      cancel: true
    }
  }



}

// 创建单例实例
const resourceConfigHelper = new ResourceConfigHelper()

export default resourceConfigHelper

// 导出便捷方法
export const {
  canAddResource,
  canDeleteResource,
} = resourceConfigHelper
