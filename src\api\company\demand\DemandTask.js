import request from '@/utils/request'

// 查询03 需求任务列表
export function listDemandTask(query) {
  return request({
    url: '/doc/demandTask/list',
    method: 'get',
    params: query
  })
}

// 查询03 需求任务详细
export function getDemandTask(id) {
  return request({
    url: '/doc/demandTask/' + id,
    method: 'get'
  })
}

// 新增03 需求任务
export function addDemandTask(data) {
  return request({
    url: '/doc/demandTask',
    method: 'post',
    data: data
  })
}

// 修改03 需求任务
export function updateDemandTask(data) {
  return request({
    url: '/doc/demandTask',
    method: 'put',
    data: data
  })
}

// 删除03 需求任务
export function delDemandTask(id) {
  return request({
    url: '/doc/demandTask/' + id,
    method: 'delete'
  })
}


export function importDemandTaskByExcel(query) {
  return request({
    url: '/doc/demandTask/importDemandTaskByExcel',
    method: 'get',
    params: query
  })
}