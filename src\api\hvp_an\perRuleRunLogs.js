import request from '@/utils/request'

// 查询规则执行日志列表
export function listPerRuleRunLogs(query) {
  return request({
    url: '/hvpAn/perRuleRunLogs/list',
    method: 'get',
    params: query
  })
}

// 查询规则执行日志详细
export function getPerRuleRunLogs(id) {
  return request({
    url: '/hvpAn/perRuleRunLogs/' + id,
    method: 'get'
  })
}

// 新增规则执行日志
export function addPerRuleRunLogs(data) {
  return request({
    url: '/hvpAn/perRuleRunLogs',
    method: 'post',
    data: data
  })
}

// 修改规则执行日志
export function updatePerRuleRunLogs(data) {
  return request({
    url: '/hvpAn/perRuleRunLogs',
    method: 'put',
    data: data
  })
}

// 删除规则执行日志
export function delPerRuleRunLogs(id) {
  return request({
    url: '/hvpAn/perRuleRunLogs/' + id,
    method: 'delete'
  })
}
