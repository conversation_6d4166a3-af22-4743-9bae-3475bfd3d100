import request from '@/utils/request'

// 查询评估规则列表
export function page(query) {
  return request({
    url: '/hvpVs/score/mode/page',
    method: 'post',
    data: query
  })
}

// 查询评估规则详细
export function info(id) {
  return request({
    url: '/hvpVs/score/mode/' + id,
    method: 'get'
  })
}

// 新增评估规则
export function add(data) {
  return request({
    url: '/hvpVs/score/mode',
    method: 'post',
    data: data
  })
}

// 修改评估规则
export function update(data) {
  return request({
    url: '/hvpVs/score/mode',
    method: 'put',
    data: data
  })
}

export function remove(id) {
  return request({
    url: '/hvpVs/score/mode/' + id,
    method: 'delete'
  })
}

export function calc(id) {
  return request({
    url: '/hvpVs/score/mode/calc/' + id,
    method: 'post'
  })
}
