import request from '@/utils/request'

export const page = data => {
  return request({
    headers: {
      'Content-Type': "application/json"
    },
    data,
    url: 'cov/view/page',
    method: 'post'
  })
}

export const list = data => {
  return request({
    headers: {
      'Content-Type': "application/json"
    },
    data,
    url: 'cov/view/list',
    method: 'post'
  })
}

export const info = id => {
  return request({
    url: `cov/view/info/${id}`,
    method: 'get'
  })
}

export const valid = data => {
  return request({
    data,
    url: 'cov/view/valid',
    method: 'post'
  })
}

export const add = data => {
  return request({
    data,
    url: 'cov/view/add',
    method: 'post'
  })
}

export const drop = id => {
  return request({
    url: `cov/view/delete/${id}`,
    method: 'post'
  })
}

export const map = params => {
  return request({
    url: 'cov/view/getMap',
    params,
    method: 'get'
  })
}

export const box = params => {
  return request({
    url: 'cov/view/box',
    params,
    method: 'get'
  })
}
