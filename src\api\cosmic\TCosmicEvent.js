import request from '@/utils/request'

// 查询cosmic触发事件列表
export function listTCosmicEvent(query) {
  return request({
    url: '/cosmic/cosmic/event/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic触发事件详细
export function getTCosmicEvent(id) {
  return request({
    url: '/cosmic/event/' + id,
    method: 'get'
  })
}

// 新增cosmic触发事件
export function addTCosmicEvent(data) {
  return request({
    url: '/cosmic/event',
    method: 'post',
    data: data
  })
}

// 修改cosmic触发事件
export function updateTCosmicEvent(data) {
  return request({
    url: '/cosmic/event',
    method: 'put',
    data: data
  })
}

// 删除cosmic触发事件
export function delTCosmicEvent(id) {
  return request({
    url: '/cosmic/event/' + id,
    method: 'delete'
  })
}