import request from '@/utils/request'

// 查询评估分类列表
export function listLevel(query) {
  return request({
    url: '/hvpVs/score/level/list',
    method: 'get',
    params: query
  })
}

// 查询评估分类详细
export function getLevel(id) {
  return request({
    url: '/hvpVs/score/level/' + id,
    method: 'get'
  })
}

// 新增评估分类
export function addLevel(data) {
  return request({
    url: '/hvpVs/score/level',
    method: 'post',
    data: data
  })
}

// 修改评估分类
export function updateLevel(data) {
  return request({
    url: '/hvpVs/score/level',
    method: 'put',
    data: data
  })
}

// 删除评估分类
export function delLevel(id) {
  return request({
    url: '/hvpVs/score/level/' + id,
    method: 'delete'
  })
}
