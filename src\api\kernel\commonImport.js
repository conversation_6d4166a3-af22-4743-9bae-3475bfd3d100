import request from '@/utils/request'

// 查询通用导入模板详细
export function getTemplateDetail(id) {
  return request({
    url: '/kernel/common/import/detail/' + id,
    method: 'get'
  })
}

// 新增通用导入模板
export function addTemplate(data) {
  return request({
    url: '/kernel/common/import/add',
    method: 'post',
    data: data
  })
}

// 修改通用导入模板
export function updateTemplate(data) {
  return request({
    url: '/kernel/common/import/edit',
    method: 'put',
    data: data
  })
}

// 删除通用导入模板
export function delTemplate(ids) {
  return request({
    url: '/kernel/common/import/del/' + ids,
    method: 'delete'
  })
}

export function changeStatus(changeType,data) {
  return request({
    url: '/kernel/common/import/changeStatus/' + changeType,
    method: 'put',
    data: data
  })
}

// 通用导入数据
export function importData(data) {
  return request({
    url: '/kernel/common/import/importData',
    method: 'post',
    data: data
  })
}

// 通用导入-读取数据库表字段
export function readTableField(param) {
  return request({
    url: '/kernel/common/import/readTableField',
    method: 'get',
    params: param
  })
}

// 下载模板
export function donwloadTemplate(data) {
  return request({
    url: '/kernel/common/import/donwloadTemplate',
    method: 'post',
    data: data
  })
}
//手动执行定时任务
export function taskRun(data){
  return request({
    url: '/kernel/common/import/runTask',
    method: 'post',
    data:data
  })
}
//复制
export function copyTemplate(data){
  return request({
    url: '/kernel/common/import/copy',
    method: 'post',
    data:data
  })
}
