export default {
    init: function (ak){
    	const BMap_URL = "https://api.map.baidu.com/api?v=3.0&ak="+ ak +"&s=1&callback=onBMapCallback";
    	return new Promise((resolve, reject) => {
    	if(typeof BMap !== "undefined") {
    		resolve(BMap);
    		return true;
    	}
    	window.onBMapCallback = function () {
    		resolve(BMap);
    	};
      let scriptNode = document.createElement("script");
    	scriptNode.setAttribute("type", "text/javascript");
    	scriptNode.setAttribute("src", BMap_URL);
    	document.body.appendChild(scriptNode);
    });
  }
}
