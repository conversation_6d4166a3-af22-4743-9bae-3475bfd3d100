<template>
  <el-form
    :class='{
      "form-input-new": isNewForm,
      "form-input-delete": formValue && formValue.formType === "delete",
    }'
    :disabled='complexFormDisabled'
    :inline='inline'
    :label-width='labelWidth'
    :model='form'
    :rules='rules'
    :size='size'
    :status-icon='status'
    :validate-on-rule-change='false'
    ref='form'
  >
    <template v-for='(field, index) in fields'>
      <br v-if='formGroupColumnType(field, index)' />

      <el-form-item
        :class='[
          "form-item__" + field.name,
          {
            "form-item__textarea": ["codemirror", "textarea"].includes(controlTypeField(field)),
            "form-input-change": isShowFormChange(field),
            "form-item-switch": controlTypeField(field) === "switch"
          }
        ]'
        :key='index'
        :prop='field.name'
        :style='formGroupColumnWidth(field) '
        v-if='controlShowField(field)'
      >
        <template slot='label'>
          <el-tooltip
            effect='light'
            placement='top-start'
            popper-class='form-label-tooltip'
            v-if='showFieldTitleTooltip(field)'
          >
            <div slot='content'>{{ field.title }}</div>
            <div class='label-content'>{{ field.title }}</div>
          </el-tooltip>
          <span v-else>{{ field.title }} </span>
          <span v-if='colon'>:</span>
          <el-tooltip
            effect='light'
            placement='top-start'
            popper-class='form-label-tooltip'
            v-if='field.tipInfo && field.formEdit && !disabled'
          >
            <div class='content-tip' slot='content'>
              <pre>{{ field.tipInfo }}</pre>
            </div>
            <!-- <i class='el-icon-question'></i> -->
            <svg-icon icon-class='question1'></svg-icon>
          </el-tooltip>
        </template>

        <el-tooltip
          :disabled='showFieldValueTooltip(form[field.name],field)'
          class="item"
          effect='light'
          placement='top-start'
          popper-class='change-input-tooltip'
        >
          <div slot='content'><span v-if="isShowFormChange(field)">修改前值:{{ controlFormChange(field) }};<br /></span><span
            style="color:#67C23A;">{{ controlFormText(field) }}</span></div>
          <el-input
            :autosize='{ minRows: 1, maxRows: 20}'
            :disabled='hasEditTwiceJudge(field)'
            :rows='1'
            class="myInput"
            style='height: 100%'
            type='textarea'
            v-if='controlTypeField(field) === "textarea"'
            v-model='form[field.name]'
          ></el-input>

          <CodeMirror
            :value='form[field.name]'
            @changeTextarea='changeCodemirror($event,field.name)'
            height='400px'
            mode='javascript'
            v-else-if='controlTypeField(field) === "codemirror"'
          ></CodeMirror>

          <el-input-number
            :disabled='hasEditTwiceJudge(field)'
            clearable
            v-else-if='controlTypeField(field) === "number" && field.formEdit'
            v-model='form[field.name]'
          ></el-input-number>

          <!-- :filter-method='($event) => selectLazyLoad($event, field)' -->
          <el-select
            :allow-create='["selectInput", "selectRadioInput"].includes(controlTypeField(field))'
            :class='{"form-select-input": controlTypeField(field) === "selectRadioInput"}'
            :clearable='controlFieldClearable(field)'
            :collapse-tags='controlTypeField(field) !== "select"'
            :disabled='hasEditTwiceJudge(field)'
            :loading='remoteSearchLoading'
            :loading-text='remoteSearchLoadingText'
            :multiple='!["select", "selectRemoteSearch"].includes(controlTypeField(field))'
            :placeholder='controlTypeField(field) === "selectRemoteSearch" ? "请输入搜索关键词" : "请选择" + field.title'
            :ref='field.name+"_select"'
            :remote='controlTypeField(field) === "selectRemoteSearch"'
            :remote-method='handleRemoteMethod'
            @change='selectCascading($event, field)'
            @focus='currItem = field; markFieldAsInteracted(field.name)'
            @visible-change="(visible) => handleVisibleChange(visible,field)"
            default-first-option
            filterable
            v-else-if='selectControl.includes(controlTypeField(field))'
            v-model='form[field.name]'
          >
            <el-option
              :disabled='item.disabled'
              :key='(item.value?item.value:item.code) + index'
              :label='item.name || item.label'
              :value='item.value?item.value:item.code'
              v-for='(item, index) in [...(field.children||[]),...($root.linkedOptions[field.name+"_select"]||[])]'
            ></el-option>
          </el-select>

          <el-autocomplete
            :clearable='controlFieldClearable(field)'
            :fetch-suggestions='autocompleteSearch'
            @change='selectCascading($event, field)'
            @focus='currItem = field; markFieldAsInteracted(field.name)'
            @select='selectCascading($event, field)'
            placeholder='请输入内容'
            style='width: 100%'
            v-else-if='controlTypeField(field) === "autocomplete"'
            v-model='form[field.name]'
          ></el-autocomplete>

          <form-color-pick
            :field='field'
            @setFormData='setFormData'
            v-else-if='controlTypeField(field) === "groupColorPick"'
            v-model='form[field.name]'
          ></form-color-pick>

          <input-group
            :field='field'
            @setFormData='setFormData'
            v-else-if='controlTypeField(field) === "inputGroup"'
            v-model='form[field.name]'
          ></input-group>

          <el-date-picker
            :disabled='hasEditTwiceJudge(field)'
            format='yyyy-MM-dd'
            placeholder='请选择日期'
            type='date'
            v-else-if='controlTypeField(field) === "date"'
            v-model='form[field.name]'
            value-format='yyyy-MM-dd'
          ></el-date-picker>

          <el-date-picker
            :disabled='hasEditTwiceJudge(field)'
            format='yyyy-MM-dd HH:mm:ss'
            placeholder='请选择日期'
            type='datetime'
            v-else-if='controlTypeField(field) === "time"'
            v-model='form[field.name]'
            value-format='yyyy-MM-dd HH:mm:ss'
          ></el-date-picker>

          <el-date-picker
            :picker-options='pickerOptions'
            align='right'
            end-placeholder='结束日期'
            size='small'
            start-placeholder='开始日期'
            type='datetimerange'
            v-else-if='controlTypeField(field) === "dateTimeRange"'
            v-model='form[field.name]'
            value-format='yyyy-MM-dd HH:mm:ss'
            @change='handleRangeDatePickerChange(field.name)'
          ></el-date-picker>

          <el-radio-group
            :disabled='hasEditTwiceJudge(field)'
            @change='markFieldAsInteracted(field.name); selectCascading($event, field)'
            v-else-if='controlTypeField(field) === "radio"'
            v-model='form[field.name]'
          >
            <el-radio
              :key='(item.value?item.value:item.code) + index'
              :label='item.value?item.value:item.code'
              v-for='(item, index) in field.children'
            >{{ item.name }}
            </el-radio>
          </el-radio-group>

          <el-checkbox-group
            :disabled='hasEditTwiceJudge(field)'
            @change='markFieldAsInteracted(field.name)'
            v-else-if='controlTypeField(field) === "checkbox"'
            v-model='form[field.name]'
          >
            <el-checkbox
              :key='(item.value?item.value:item.code) + index'
              :label='item.value?item.value:item.code'
              v-for='(item, index) in field.children'
            >{{ item.name }}
            </el-checkbox>
          </el-checkbox-group>

          <el-switch
            v-else-if='controlTypeField(field) === "switch"'
            v-model='form[field.name]'
          ></el-switch>

          <el-cascader
            :options='field.children'
            :show-all-levels='!field.onlyLeaf'
            @change='markFieldAsInteracted(field.name); selectCascading($event, field)'
            @focus='markFieldAsInteracted(field.name)'
            v-else-if='controlTypeField(field) === "cascader"'
            v-model='form[field.name]'
          ></el-cascader>

          <el-input
            :readonly='controlTypeField(field) === "popup"'
            :class='{"disable-input": controlTypeField(field) === "popup"}'
            :disabled='hasEditTwiceJudge(field)'
            @click.native='markFieldAsInteracted(field.name); triggerPopupSelect(field)'
            @dblclick.native='triggerPopupEdit(field)'
            @focus='markFieldAsInteracted(field.name)'
            tabindex='-1'
            v-else-if='["popup", "popupEdit","popupMulti"].includes(controlTypeField(field))'
            v-model.trim='form[field.name]'
          ></el-input>

          <el-input
            :disabled='hasEditTwiceJudge(field)'
            @focus='markFieldAsInteracted(field.name)'
            clearable
            v-else
            v-model.trim='form[field.name]'
          >
            <el-tooltip
              effect='light'
              placement='bottom-start'
              popper-class='form-label-tooltip'
              slot='append'
              v-if='field.suffix && field.formEdit'
            >
              <div class='content-tip' slot='content'>{{ field.suffix }}</div>
              <span class='input-suffix'>{{ field.suffix }}</span>
            </el-tooltip>
          </el-input>
        </el-tooltip>
      </el-form-item>
    </template>

    <el-form-item class='form-item-slot'>
      <slot />
    </el-form-item>

    <PopupSelect
      :data='getFormFullValue()'
      :dataSource='popupSelectDataSource'
      :name='popupSelectName'
      :title='popupSelectTitle'
      :visible.sync='popupSelectVisible'
      :isMultiSelect='popupSelectField && controlTypeField(popupSelectField) === "popupMulti"'
      :displayField='popupSelectField && popupSelectField.popupMultiDisplayField || "name"'
      :uniqueKey='popupSelectField && popupSelectField.popupMultiUniqueKey || "id"'
      :initMatchField='popupSelectField && popupSelectField.popupMultiInitMatchField || ""'
      @cancelPopupSelect='popupSelectVisible = false'
      @submitPopupSelect='setFormValueFromPop'
      v-if='popupSelectVisible'
    />
  </el-form>
</template>

<script>
import { useTextWidth } from '@/utils/plan/useBusiness'
import { isJsonResolve, deepClone, getDateStr, isNumberStr } from '@/utils'
import FormColorPick from './FormColorPick'
import InputGroup from './InputGroup'
import service from '@/utils/request'
import { useGetters } from '@/utils/useMappers'
// 1. 导入事件总线
import LinkageEventBus, { LINKAGE_EVENTS } from '@/utils/linkageEventBus'

export default {
  name: 'basicForm',
  props: {
    formId: String,
    status: Boolean,
    formValue: Object,
    fields: Array,
    disabled: Boolean,
    colon: Boolean,
    groupColumn: Number,
    groupMark: String,
    inline: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium'
    },
    labelWidth: {
      type: String,
      default: '120px'
    },
    hiddenFiels: Array,
    clearHiddenFieldsValue: {
      type: Boolean,
      default: true
    },
    useFieldDefaultValue: {
      type: Boolean,
      default: true
    },
    beforePopupCondition: {
      // popup弹窗前置方法
      type: Function
    },
    showChangeToolTip: Boolean // 显示表单上次修改提示
  },

  created() {
    this.$root.linkedOptions = this.$root.linkedOptions || {}
    // 监听数据联动事件
    LinkageEventBus.$on(LINKAGE_EVENTS.VALUE_UPDATE, this.handleLinkageUpdate)
  },
  mounted() {
    this.$root.$on('selectOptionSet', ({ target, data }) => {
      this.$root.$set(this.$root.linkedOptions, `${target}_select`, data)
      this.$nextTick(() => {
        this.$root.$emit('selectOptionUpdated', {
          fieldName: target,
          form: this.form,
          options: data
        })
      })
    })
    this.$root.$emit('enableShowChangeToolTip', this)
  },
  data() {
    return {
      isFirstLoad: 0,
      form: {},
      rules: {},
      recordRules: {}, // 备份校验 避免隐藏字段时校验丢失
      recordForm: {}, // 备份表单 避免因formValue传值导致垃圾数据进入
      recordFields: [], // 备份字段
      currItem: null,
      userInteractedFields: {}, // 跟踪用户已交互的字段
      fieldsWatcherTimer: null, // fields监听器防抖定时器
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },

      selectControl: [
        'select',
        'selectMultiple',
        'selectInput',
        'selectRadioInput',
        'selectRemoteSearch'
      ], // 下拉|下拉多选|下拉多选输入|下拉单选输入|下拉远程搜索

      popupSelectVisible: false,
      popupSelectTitle: '',
      popupSelectName: '',
      popupSelectDataSource: '',
      popupSelectField: null, // 当前触发弹窗的字段配置
      triggerName: '',
      triggerLinkageName: [],
      triggerTable: {},
      triggerTimer: null, // 区分单双击延迟timer
      selectLazyDataMaxItem: 20, // select 懒加载最大选项数

      numericalLabelWidth: +this.labelWidth.slice(0, -2),
      numericalValueWidth: null,
      formChangeSuffix: '_original',
      showChangeToolTipD: false,

      remoteSearchLoading: false, //下拉远程搜索loading
      remoteSearchLoadingText: '关键词搜索中...',
      configFields:{
        networkModeField: {},
      },

      // 级联选择优化相关
      cascadeRequestCache: {}, // 级联请求缓存
      cascadeLoadingFields: new Set(), // 正在加载的级联字段
      cascadeDebounceTimers: {} // 防抖定时器
    }
  },
  computed: {
    showChangeToolTipX() {
      return this.showChangeToolTip || this.showChangeToolTipD
    },
    complexFormDisabled() {
      if (!this.formValue) return this.disabled

      // forceEdit 强制可编辑
      return this.formValue.hasOwnProperty('forceEdit')
        ? this.formValue.forceEdit
        : this.disabled
    },
    isNewForm() {
      return this.formValue?.formType === 'add'
    },
    // 计算每个字段的实时必填状态
    computedFieldsRequired() {
      const requiredMap = {}
      this.fields?.forEach((field) => {
        if (field.conditionRequired && this.isJsonResolve(field.conditionRequired)) {
          const config = JSON.parse(field.conditionRequired)
          const isRequired = this.validRequire(config.rule)
          requiredMap[field.name] = isRequired || false
        } else {
          requiredMap[field.name] = field.required || false
        }
      })
      return requiredMap
    },
    // 计算每个字段的实时显示状态
    computedFieldsVisible() {
      const visibleMap = {}
      this.fields?.forEach((field) => {
        let isHiddenByParam2 = -1
        this.fields.forEach((otherField) => {
          if (otherField.param2 && otherField.tableName=== field.tableName) {
            try {
              const param2Obj = JSON.parse(otherField.param2)
              const selectedValue = this.formValue[otherField.name] // 获取其他字段的当前值
              if (param2Obj[selectedValue] && param2Obj[selectedValue].hide) {
                const hiddenFields = param2Obj[selectedValue].hide.split(',').map((f) => f.trim())
                // 如果当前字段在隐藏列表中
                if (hiddenFields.includes(field.name)) {
                  isHiddenByParam2 = 1
                }
              }
              if (param2Obj[selectedValue] && param2Obj[selectedValue].show) {
                const hiddenFields = param2Obj[selectedValue].show.split(',').map((f) => f.trim())
                // 如果当前字段在显示列表中
                if (hiddenFields.includes(field.name)) {
                  isHiddenByParam2 = 0
                }
              }
            } catch (e) {
              console.error(`解析字段 ${otherField.name} 的 param2 失败:`, e)
            }
          }
        })
        if (isHiddenByParam2 === 1) {
          visibleMap[field.name] = false
        } else if (isHiddenByParam2 === 0) {
          visibleMap[field.name] = true
        } else {
          visibleMap[field.name] = !field.initHide && field[this.controlShowProperty(field)]
        }

      })
      return visibleMap
    },
    // 计算每个字段的实时禁用状态
    computedFieldsDisabled() {
      const disabledMap = {}
      this.fields?.forEach((field) => {
        disabledMap[field.name] = this.isNewForm ? !field.param5 : this.controlDisableField(field)
      })
      return disabledMap
    },
    // 计算每个字段的实时类型
    computedFieldsType() {
      const typeMap = {};
      this.fields?.forEach((field) => {
        // 初始化字段类型为默认值
        typeMap[field.name] = field.dataType || field.formType;

        // 检查是否有 param3 影响当前字段的类型
        let isModifiedByParam3 = false;
        this.fields.forEach((otherField) => {
          if (otherField.param3 && otherField.tableName === field.tableName) {
            try {
              const param3Obj = JSON.parse(otherField.param3);
              const selectedValue = this.formValue[otherField.name]; // 获取其他字段的当前值

              // 检查 param3 是否包含当前字段的类型修改规则
              if (param3Obj[selectedValue]) {
                const rules = param3Obj[selectedValue];
                rules.forEach((rule) => {
                  if (rule.name === field.name) {
                    typeMap[field.name] = rule.formType; // 更新字段类型
                    isModifiedByParam3 = true;
                  }
                });
              }
            } catch (e) {
              console.error(`解析字段 ${otherField.name} 的 param3 失败:`, e);
            }
          }
        });
        // 如果未被 param3 修改，保留默认类型
        if (!isModifiedByParam3) {
          typeMap[field.name] = field.dataType || field.formType;
        }
      });
      return typeMap;
    }  },
  watch: {
    form: {
      handler(newval, oldval) {
        //根据系统参数判断是否修改数据进行查询
        if (this.formValue) {
          const changeForm = {}
          Object.keys(this.recordForm).forEach((key) => {
            if (newval[key] !== this.formValue[key]) {
              const field = this.fields.find(o => o.name === key) || {}
              if (field.dataLinkage) {
                // 只有用户交互过的字段才触发数据联动修改
                this.handleDataLinkage(field, newval[key], false)
              }
              const value = Array.isArray(newval[key]) ? newval[key].join(',') : newval[key]
              this.$set(changeForm, key, value)
            }
          })
          Object.keys(changeForm).length && this.$emit('returnFormValue', changeForm, this.groupMark)
        } else {
          this.$emit('returnFormValue', deepClone(newval), this.groupMark)
        }
      },
      deep: true,
      immediate: true
    },
    formValue: {
      handler(newval) {
        if (newval) {
          // 表单数据补全, 某些情况下传入的formValue数据不全  会导致表单携带前一份数据缓存
          if (Object.keys(newval).length < this.recordFields.length) {
            this.createModelAndRules()
          }
          this.recordFields.map(field => {
            //如果输入框类型为数字，且可编辑，但值不为数字，则默认为0。
            // 解决因类型不一致而长期卡顿乃至卡死，；解决number为空，使其默认为0
            if (field.formType === 'number' && isNumberStr(newval[field.name]) === false && field.formEdit === true && newval[field.name]) {
              newval[field.name] = 0
            }
          })
          this.setFormValue(newval)

        }
      },
      deep: true,
      immediate: true
    },
    fields: {
      handler(newval, oldval) {
        // 防抖处理，避免无限循环
        if (this.fieldsWatcherTimer) {
          clearTimeout(this.fieldsWatcherTimer)
        }

        this.fieldsWatcherTimer = setTimeout(() => {
          if (newval) {
            if (this.hasFieldsChanged(newval)) {
              this.createModelAndRules()
              this.recordFields = deepClone(newval)
            }
            // this.setSelectChildrenLazy(newval)
          }

          // fixed: hiddenFiels与fields异步情况
          this.hiddenFiels && this.setHiddenFields(this.hiddenFiels)
          if(newval && this.groupMark==='siteNeed'){
            this.fieldConfigHandle(newval)
          }
        }, 50) // 50ms防抖延迟
      },
      deep: true,
      immediate: true//此处不能注释，否则所有表单皆显示为空
    },
    hiddenFiels: {
      handler(newval, oldval) {
        if (newval && this.fields) {
          this.fixedHiddenFields()
          newval.length && this.setHiddenFields(newval)
        }
      },
      deep: true,
      immediate: true
    },
    // 监听计算属性的变化，确保必填状态实时更新
    computedFieldsRequired: {
      handler(newVal, oldVal) {
        // 当必填状态发生变化时，更新表单验证规则
        this.$nextTick(() => {
          this.updateFormRules()
        })
      },
      deep: true
    },
    // 监听字段显示状态变化
    computedFieldsVisible: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      },
      deep: true
    },
    // 监听字段禁用状态变化
    computedFieldsDisabled: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      },
      deep: true
    },
    // 监听字段类型变化
    computedFieldsType: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      },
      deep: true
    }
  },
  methods: {
    //此方法单独处理部分部分字段不生效情况
    async fieldConfigHandle(fields){
      //基站需求-网络制式条件显示有问题，单独处理，以后有类似情况放在此方法单独处理
      if(Object.keys(this.configFields.networkModeField).length === 0){
        this.configFields.networkModeField = fields.find(field => field.name === 'networkMode');
      }
      if(this.configFields.networkModeField && this.configFields.networkModeField.initHide === false) return;
      const networkStandField = fields.find(field => field.name === 'networkStandard');
      networkStandField && networkStandField.param2 &&(this.setFieldConfigHidden(networkStandField, true));
    },
    // 处理来自其他表单的联动更新
    handleLinkageUpdate({ targetFormId, field, value, formShow, children, isInitHide }) {
      // 检查是否是针对当前表单的更新
      //包含当前表单ID
      if (isInitHide) {
        this.fixedInitHide()
      } else {
        if (this.formId?.includes(targetFormId)) {
          if (formShow !== null && formShow !== undefined && formShow !== "") {
            // 查找对应的字段配置
            const fieldsDate = this.fields.find((i) => i.name === field.trim())
            if (fieldsDate) {
              if (fieldsDate.relevance == null) {
                const recordInitHide = !!fieldsDate.formShow
                const recordRequired = !!fieldsDate.required
                this.triggerLinkageName.push(field)

                // this.$set(this.triggerTable, 'tableName', targetFormId)
                // this.$set(this.triggerTable, 'name', field)

                // relevance 关联字段 用于复位哪些字段
                const relevance = fieldsDate.relevance || {}
                relevance[field] = recordInitHide
                this.$set(fieldsDate, 'relevance', relevance)
              }
              this.$set(fieldsDate, 'formShow', formShow)

            }
          }
          // 查找对应的字段配置
          const fieldConfig = this.fields.find(f => f.name === field)
          if (children && children.length > 0) {
            fieldConfig.children = children
          } else {
            this.$set(this.form, field, value)
            if(fieldConfig.dataLinkage){
              // 来自其他表单的联动更新，强制执行数据联动修改
              this.handleDataLinkage(fieldConfig, value, true)
            }

          }
          if (fieldConfig) {
            // 触发级联效果
            this.selectCascading(value, fieldConfig, false, true)
          }
        }
      }
    },
    validRequire(rule) {
      try {
        if (!this.formValue) {
          console.warn('this.form 未定义，无法执行动态代码', rule)
          return null // 或抛出自定义错误
        }
        if (rule.indexOf('data.') !== -1) {
          rule = rule.replace(/data\./g, 'that.formValue.')
        }
        // 创建动态函数，确保返回值
        const func = new Function('that', `
      with(that) {
        // 如果 rule 本身没有返回值，默认返回 false
        const result = (${rule});
        return result !== undefined ? result : false;
      }
    `)
        const result = func(this)
        // console.log('validRequire', rule, result);
        return result
      } catch (error) {
        console.error('执行代码出错:', error, rule, this.form, this.form.rruReuse || '')
        return null
      }
    },
    createModelAndRules() {
      this.form = {}
      this.recordForm = {}
      this.resetUserInteraction() // 重置用户交互状态
      //此循环涉及数据初始化显示等问题，最好不要更改，否则会导致一系列问题
      this.fields?.forEach((field) => {
        const value = this.initFormFieldValue(field)
        field.name && this.$set(this.form, field.name.trim(), value)
        field.name && this.$set(this.recordForm, field.name.trim(), value)

        // 初始化验证规则 - 使用基础必填状态，后续会通过 updateFormRules 更新
        if (field.required && !this.hasEditTwiceJudge(field)) {
          this.$set(this.rules, field.name, [{ required: field.required, message: `请填写${field.title}`, trigger: ['blur', 'change'] }])
        } else {
          this.$set(this.rules, field.name, [])
        }
        //北京-当基带设备的设备类型=CU的时候整个表单跳过校验
        const sysconfig = useGetters('sysconfig')
        const { CURRENT_PROVINCE_CODE } = sysconfig()
        if (CURRENT_PROVINCE_CODE === 'BJ' && field.tableName === 't_5g_equipment'
          && field.tableSubType === 'baseBand' && this.form['subType'] === 'CU') {
          this.$set(this.rules, field.name, [])
        }
      })

      // 备份原始规则
      this.recordRules = deepClone(this.rules)

      // 在下一个tick中初始化所有条件逻辑
      this.$nextTick(() => {
        // 使用全面的初始化触发器
        this.triggerAllInitialLogic()

        // 如果有外部传入的表单值，也要触发相应逻辑
        if (this.formValue && Object.keys(this.formValue).length > 0) {
          // console.log('触发外部表单值的级联逻辑:', this.formValue)
          this.triggerCascading(this.formValue)
        }

        // 最后更新所有验证规则
        this.updateFormRules()

        // 强制更新视图确保所有状态正确显示
        this.$forceUpdate()
      })
    },


    hasFieldsChanged(fields) {
      if (fields.length !== this.recordFields.length) return true

      let changed = false
      const recordFieldsNameMap = this.recordFields.map((i) => i.name)
      fields.forEach((field) => {
        if (!recordFieldsNameMap.includes(field.name)) {
          changed = true
        }
      })
      return changed
    },
    initFormFieldValue(field) {
      // console.log('初始化字段值：',field);
      const formValue = this.formValue && this.formValue[field.name]

      const defaultValue = (this.useFieldDefaultValue && field.defaultValue != null && field.defaultValue !== '') ? field.defaultValue : ''
      let value = formValue
      // if(isNaN(value) ) value = '';
      // 修复：正确处理数字0，确保数字0不被当作空值
      if (this.formValue && formValue !== undefined && formValue !== null && formValue !== '' && field && field.name) {
        this.formValue[field.name] = value
      } else {
        value = defaultValue
      }
      if(this.controlTypeField(field) === 'number'){
        // 修复：正确处理数字0，避免被转换为undefined
        value = (value !== undefined && value !== null && value !== '') ? +value : undefined
      }
      if (this.controlTypeField(field) === 'dateTimeRange' && defaultValue) {
        return [
          getDateStr(new Date() - 86400000 * +defaultValue),
          getDateStr(new Date())
        ]
      }
      return [
        'checkbox',
        'selectMultiple',
        'selectInput',
        'selectRadioInput'
      ].includes(this.controlTypeField(field))
        ? // 修复：正确处理数字0，确保数字0不被当作空值
        (value !== undefined && value !== null && value !== '')
          ? value.split(',')
          : []
        : value
    },
    controlTypeField(field) {
      if(field && field.name){
        // 使用计算属性获取实时字段类型
        return this.computedFieldsType[field?.name] !== undefined ? this.computedFieldsType[field?.name] : (field?.dataType || field?.formType)
      }
    },
    controlFieldClearable(field) {
      const { clearable, name } = field
      return typeof clearable === 'boolean' ? clearable : name !== 'status'
    },
    controlShowProperty(field) {
      return field.hasOwnProperty('formShow') ? 'formShow' : 'show'
    },
    controlShowField(field) {
      // 使用计算属性获取实时显示状态
      return this.computedFieldsVisible[field.name] !== undefined
        ? this.computedFieldsVisible[field.name]
        : (!field.initHide && field[this.controlShowProperty(field)])
    },
    controlDisableField(field) {
      return field.formEdit === false || field.disabled
    },
    controlFormChange(field) {
      if (!this.showChangeToolTipX || !this.formValue) return false
      const { name, children, formType } = field
      const tag = name + this.formChangeSuffix
      let preChangeValue = this.formValue[tag]
      // 修复：正确处理数字0，确保数字0不被当作空值
      if (children?.length && (preChangeValue !== undefined && preChangeValue !== null && preChangeValue !== '') && formType === 'select') {
        const name = children.find(
          (i) =>
            i.value === this.formValue[tag] || i.code === this.formValue[tag]
        )?.name
        preChangeValue = name || preChangeValue
      }
      return preChangeValue
    },
    controlFormText(field) {
      if (!this.formValue) return false
      const { name, children, formType } = field
      const tag = name
      let preChangeValue = this.formValue[tag]
      // 修复：正确处理数字0，确保数字0不被当作空值
      if (children?.length && (preChangeValue !== undefined && preChangeValue !== null && preChangeValue !== '') && formType === 'select') {
        const name = children.find(
          (i) =>
            i.value === this.formValue[tag] || i.code === this.formValue[tag]
        )?.name
        preChangeValue = name || preChangeValue
      }
      return preChangeValue
    },
    isShowFormChange(field) {
      if (!this.showChangeToolTipX || !this.formValue) return false
      return this.formValue.hasOwnProperty(field.name + this.formChangeSuffix)
    },
    hasEditTwiceJudge(field) {
      // 字段是否可编辑二次判断
      // 使用计算属性获取实时禁用状态
      return this.computedFieldsDisabled[field.name] !== undefined
        ? this.computedFieldsDisabled[field.name]
        : (this.isNewForm ? !field.param5 : this.controlDisableField(field))
    },
    handleRemoteMethod(query) {
      if (query !== '') {
        this.remoteSearchLoading = true
        if (this.currItem?.dataSource) {
          service(`${this.currItem.dataSource}?key=${query}`).then((res) => {
            if (res?.data?.length > 1000) {
              this.remoteSearchLoadingText = '请精确搜索关键词!'
              this.$set(this.currItem, 'children', [])
            } else {
              this.remoteSearchLoading = false
              this.remoteSearchLoadingText = '关键词搜索中...'
              this.$set(this.currItem, 'children', res.data)
            }
          })
        }
      } else {
        this.$set(this.currItem, 'children', [])
      }
    },

    validateForm() {
      let flag = false
      this.$refs.form.validate((valid, fields) => {
        flag = valid ? valid : fields
      })
      return flag
    },
    resetForm() {
      this.$refs.form.resetFields()
      this.form['scene'] = ''//搜索时，场景字段置空后又被赋值，找了半天找不到原因，手动置空
      this.resetUserInteraction() // 重置用户交互状态
    },
    checkDict(diff) {
      try {
        Object.keys(diff).forEach((key) => {
          if (this.fields.find((item) => item.name === key && item.formType === 'select')) {
            //判断是否是字典
            const children = this.fields.find((item) => item.name === key).children
            const childrenMap = children && children.reduce((acc, cur) => {
              acc[cur.name] = cur.code? cur.code:cur.value
              return acc
            }, {})
            if (childrenMap[diff[key]]) {
              diff[key] = childrenMap[diff[key]]
            } else {
              diff[key] = null
            }
          }
        })
      } catch (error) {
        console.log('checkDict error:', error)
      }
    },
    setFormValueFromPop(value) {
      // console.log("value",value)
      this.$forceUpdate()
      const diff = this.compareTwoObjectsDiff(value, this.form)
      this.checkDict(diff)
      if (Object.keys(diff).length) {
        //判断填写字段是否是字典
        this.form = Object.assign({}, this.form, diff)
        this.triggerCascading(diff)
      }
    },
    setFormValue(value) {
      this.$forceUpdate()
      const diff = this.compareTwoObjectsDiff(value, this.form)
      // console.log('设置表单值,diff:{}',diff);
      if (Object.keys(diff).length) {
        this.form = Object.assign({}, this.form, diff)
        this.triggerCascading(diff)
      }
      this.updateRequiredStatus();
    },

    // 表单传递参数隐藏
    setHiddenFields(fields) {
      if (fields && this.fields) {
        fields.forEach((field) => {
          const currentField = this.fields.find((item) => item.name === field)
          if (currentField) {
            this.$set(
              currentField,
              this.controlShowProperty(currentField),
              false
            )

            if (this.clearHiddenFieldsValue) {
              this.form[field] = null
              this.$set(this.rules, field, null)
              this.$refs.form?.clearValidate()
            }
            // 不清空值得情况  假隐藏
          }
        })
      }
    },

    /**
     * 表单传递参数隐藏与字段配置隐藏两种方式以现在的实现方式无法同时存在，
     * 组件内在采用字段配置隐藏时 不知道该不该复位表单传递的隐藏字段
     * 字段配置隐藏时可以用字段的initHide值判断是否初始化隐藏
     */
    /**
     *  2025/5/14 15:06
     * @description:  设置某个字段隐藏,用于条件隐藏或显示
     * @param field 字段对象
     * @param autoTrigger 自动触发，为false时重置联动隐藏字段值为空（导致切换tab时值为空），为true时保留原值
     */
    // 字段配置隐藏
    setFieldConfigHidden(field, autoTrigger) {
      const { name, title, param2 } = field
      try {
        if (param2 && isJsonResolve(param2)) {
          const config = JSON.parse(param2)
          const currentValueConfig = config[this.form[name]]

          // 复位上次隐藏的字段
          this.triggerName = name
          this.fixedConfigInitHide()
          if (currentValueConfig) {
            // 解析配置：
            // 普通字符串表示默认配置隐藏字段
            // json 可配置当前值触发字段显示、隐藏
            if (typeof currentValueConfig === 'object') {
              // const { show, hide } = currentValueConfig
              // show && this.setFieldInitHide(show, false, autoTrigger)
              // hide && this.setFieldInitHide(hide, true, autoTrigger)
            } else {
              // 默认配置：隐藏字段
              this.setFieldInitHide(currentValueConfig, true, autoTrigger)
            }
          }
        }
      } catch (error) {
        console.log(`${title}配置错误:${error}`)
      }
    },
    // 字段配置类型
    setFieldConfigType(field, autoTrigger) {
      const { name, param3, title } = field
      try {
        if (param3 && isJsonResolve(param3)) {
          const config = JSON.parse(param3)
          const currentValueConfig = config[this.form[name]]
          if (currentValueConfig && Array.isArray(currentValueConfig)) {
            currentValueConfig.forEach((item) => {
              const controlField = this.fields.find((i) => i.name === item.name)
              if (controlField) {
                // controlType 关联字段 用于复位哪些字段
                // originFormType 原始字段类型
                const controlType = field.controlType || []
                const originFormType = controlField.originFormType || controlField.formType
                controlType.push(name)

                this.$set(controlField, 'controlType', controlType)
                this.$set(controlField, 'formType', item.formType)
                this.$set(controlField, 'originFormType', originFormType)
              }
            })
          } else {
            // 恢复字段原始类型
            this.fields.forEach((item) => {
              if (item.controlType?.includes(name) && item.originFormType) {
                this.$set(item, 'formType', item.originFormType)
              }
            })
          }
        }
      } catch (error) {
        console.log(`${title}配置错误:${error}`)
      }
    },
    setFieldInitHide(config, type = true, autoTrigger) {
      if (typeof config === 'string' && config) {
        const names = config.split(',')
        names.forEach((name) => {
          const field = this.fields.find((i) => i.name === name.trim())
          if (field) {
            const recordInitHide = !!field.initHide
            this.$set(field, 'initHide', type)
            this.$refs.form?.clearValidate()
            !autoTrigger && (this.form[name] = '')

            // relevance 关联字段 用于复位哪些字段
            const relevance = field.relevance || {}
            relevance[this.triggerName] = recordInitHide

            this.$set(field, 'relevance', relevance)
            this.undoFieldState(field, name, type)
          }
        })
      }
    },
    undoFieldState(field, name, type) {
      if (type) {
        this.$set(this.rules, name, undefined)
        console.log('undoFieldState',field.name,field.title,false)
        this.$set(field, 'required', false)
      } else {
        const originField = this.recordFields.find((i) => i.name === name)
        originField && this.$set(field, 'required', originField.required)
        console.log('undoFieldState record',field.name,field.title,false)

        this.$set(this.rules, name, this.recordRules[name])
      }
    },

    // initHide配置修复表单控件
    fixedConfigInitHide() {
      this.fields.forEach((field) => {
        const { name, relevance } = field
        if (relevance && typeof relevance[this.triggerName] !== 'undefined') {
          const type = relevance[this.triggerName]

          this.$set(field, 'initHide', type)
          console.log('天线综资 fixedConfigInitHide')
          this.undoFieldState(field, name, type)
        }
      })
    },
    // initHide数据联动修改
    fixedInitHide() {
      if (this.fields) {
        this.fields.forEach((field) => {
          const { name, relevance } = field
          if (this.triggerLinkageName) {
            this.triggerLinkageName.forEach(item => {
              if (relevance && typeof relevance[item] !== 'undefined') {
                const type = relevance[item]
                this.$set(field, 'formShow', type)
                console.log('天线综资 triggerLinkageName')
                this.undoFieldState(field, name, !type)
              }
            })
          }
        })
      }
    },

    // hiddenFiels 配置修复表单控件
    fixedHiddenFields() {
      this.fields.forEach((field) => {
        this.$set(field, this.controlShowProperty(field), true)
        this.$set(this.rules, field.name, this.recordRules[field.name])

        const originField = this.recordFields.find((i) => i.name === field.name)
        console.log(' hiddenFiels 配置修复表单控件',originField.required)
        // originField && this.$set(field, 'required', originField.required)
      })
    },
    // 触发级联和条件逻辑 - 用于初始化时模拟用户选择行为
    triggerCascading(value) {
      // console.log('触发初始化级联逻辑:', value)

      Object.keys(value).forEach((key) => {
        const currentValue = value[key]
        // 修复：正确处理数字0，确保数字0不被当作空值
        if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
          const currentField = this.fields?.find((field) => field.name === key)
          if (currentField) {
            // console.log(`触发字段 ${key} 的级联逻辑, 值:`, currentValue)
            // autoTrigger = true 表示这是自动触发，不是用户手动操作
            this.selectCascading(currentValue, currentField, false, true)
          }
        }
      })
    },

    // 全面的初始化触发器 - 确保所有条件逻辑都被正确初始化
    triggerAllInitialLogic() {
      // console.log('开始触发所有初始化逻辑')

      // 1. 触发条件必填逻辑
      this.triggerConditionalRequired()

      // 2. 触发显示隐藏逻辑
      this.triggerConditionalVisibility()

      // 3. 触发字段类型变更逻辑
      this.triggerConditionalTypes()

      // 4. 触发级联选择逻辑
      if (this.form && Object.keys(this.form).length > 0) {
        this.triggerCascading(this.form)
      }

      // 5. 触发数据联动逻辑
      this.triggerDataLinkage()

      // console.log('所有初始化逻辑触发完成')
    },

    // 触发条件必填逻辑
    triggerConditionalRequired() {
      this.fields?.forEach((field) => {
        if (field.conditionRequired && this.isJsonResolve(field.conditionRequired)) {
          try {
            const config = JSON.parse(field.conditionRequired)
            const isRequired = this.validRequire(config.rule)
            console.log(`触发条件必填逻辑-字段 ${field.name} 条件必填状态:`, isRequired)
            // this.$set(field, 'required', isRequired || false)
          } catch (e) {
            console.warn(`字段 ${field.name} 的条件必填配置解析失败:`, e)
          }
        }
      })
    },

    // 触发条件显示隐藏逻辑
    triggerConditionalVisibility() {
      this.fields?.forEach((field) => {
        if (field.param2 && this.isJsonResolve(field.param2)) {
          const currentValue = this.form[field.name]
          if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            // console.log(`触发字段 ${field.name} 的显示隐藏逻辑, 当前值:`, currentValue)
            // this.$nextTick(() => this.setFieldConfigHidden(field, true))
          }
        }
      })
    },

    // 触发条件类型变更逻辑
    triggerConditionalTypes() {
      this.fields?.forEach((field) => {
        if (field.param3 && this.isJsonResolve(field.param3)) {
          const currentValue = this.form[field.name]
          if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            // console.log(`触发字段 ${field.name} 的类型变更逻辑, 当前值:`, currentValue)
            this.setFieldConfigType(field, true)
          }
        }
      })
    },

    // 触发数据联动逻辑
    triggerDataLinkage() {
      this.fields?.forEach((field) => {
        if (field.dataLinkage) {
          const currentValue = this.form[field.name]
          if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            // console.log(`初始化时跳过字段 ${field.name} 的数据联动逻辑，等待用户交互`)
            // 初始化时不触发数据联动修改，等待用户实际交互
            // this.$nextTick(() => this.handleDataLinkage(field, currentValue, false))
          }
        }
      })
    },

    formGroupColumnType(field, index) {
      return index > 0 && field.modelType !== this.fields[index - 1].modelType
    },
    formGroupColumnWidth(field) {
      // 文本域独占一行
      if (this.controlTypeField(field) === 'textarea') {
        return {
          width: 'calc((100% - 10px))'
        }
      }
      return {
        width: this.groupColumn
          ? `calc((100% - ${this.groupColumn * 10}px) / ${this.groupColumn})`
          : ''
      }
    },
    handleVisibleChange(visible, field, event) {
      this.currItem = field
    },
    selectCascading(value, field, clearChildValue = true, autoTrigger = false) {
      this.$forceUpdate()
      if (this.controlTypeField(field) === 'selectRadioInput' && Array.isArray(value)) {
        value.length > 1 && value.shift()
        this.$nextTick(() => {
          this.$refs[field.name + '_select'][0]?.blur();
        });
      }

      // 处理级联选择
      const childFields = this.fields.filter((item) => item.cascadeCode?.split(',')?.includes(field.name))
      if (childFields.length) {
        // 使用防抖优化，避免频繁请求
        this.debounceCascadeRequest(field, childFields, value, clearChildValue)
      }

      // 自定义级联，所有Form中的字段都会触发
      // 表单初始化时一定回调此方法，autoTrigger表示自动触发,可解决以下场景，根据初始化信息更改字段类型，字段下拉列表,字段值等逻辑,用途广泛
      this.$emit('fieldCascading', field, value, autoTrigger, this.groupMark)


      // 通过字段配置param2设置字段隐藏
      field.param2 && this.$nextTick(() => this.setFieldConfigHidden(field, autoTrigger))

      // 通过字段配置param3设置字段类型
      field.param3 && this.setFieldConfigType(field, autoTrigger)

      // 处理数据联动
      if (field.dataLinkage) {
        // autoTrigger为true表示自动触发，需要检查用户交互状态
        // autoTrigger为false表示用户主动操作，直接执行
        this.$nextTick(() => this.handleDataLinkage(field, value, !autoTrigger))
      }

      //select框获得焦点时
      if (this.currItem) {
        this.$emit(`selectChanged`, { field, value, form: this.form, component: this })
        this.currItem = null
      }
    },

    // 防抖级联请求
    debounceCascadeRequest(parentField, childFields, value, clearChildValue) {
      const debounceKey = `${parentField.name}_${value}`

      // 清除之前的定时器
      if (this.cascadeDebounceTimers[debounceKey]) {
        clearTimeout(this.cascadeDebounceTimers[debounceKey])
      }

      // 设置新的防抖定时器
      this.cascadeDebounceTimers[debounceKey] = setTimeout(() => {
        this.processCascadeRequest(parentField, childFields, value, clearChildValue)
        delete this.cascadeDebounceTimers[debounceKey]
      }, 200) // 200ms 防抖延迟
    },

    // 处理级联请求
    async processCascadeRequest(parentField, childFields, value, clearChildValue) {
      for (const childField of childFields) {
        await this.loadCascadeData(parentField, childField, value, clearChildValue)
      }
    },

    // 加载级联数据
    async loadCascadeData(parentField, childField, value, clearChildValue) {
      const { name, href, dataSource } = childField

      if (!href && !dataSource) {
        console.warn(`${childField.name} 缺少数据源配置`)
        return
      }

      try {
        // 生成缓存键
        const url = this.replaceCascadeUrl(href || dataSource)
        const cacheKey = `${parentField.name}_${value}_${url}`

        // 检查是否正在加载
        if (this.cascadeLoadingFields.has(childField.name)) {
          return
        }

        // 检查缓存
        if (this.cascadeRequestCache[cacheKey]) {
          this.applyCascadeData(childField, this.cascadeRequestCache[cacheKey], clearChildValue)
          return
        }

        // 设置加载状态
        this.cascadeLoadingFields.add(childField.name)
        this.$set(childField, 'loading', true)

        // 发起请求
        const data = await service(url)
        const childData = data.data || data

        // 缓存结果
        this.cascadeRequestCache[cacheKey] = childData

        // 应用数据
        this.applyCascadeData(childField, childData, clearChildValue)

        // 处理子级联动
        if (clearChildValue) {
          this.clearChildCascade(childField.name)
        }

      } catch (e) {
        console.error(`${parentField.name}级联配置错误,请求失败:`, e)
        // 设置空数据，避免显示旧数据
        this.$set(childField, 'children', [])
      } finally {
        // 清除加载状态
        this.cascadeLoadingFields.delete(childField.name)
        this.$set(childField, 'loading', false)
      }
    },

    // 应用级联数据
    applyCascadeData(childField, childData, clearChildValue) {
      const filteredData = childData?.filter((i) => i) || []

      // 清空子字段值
      if (clearChildValue) {
        this.form[childField.name] = ''
      }

      // 设置选项数据
      this.$set(childField, 'children', filteredData)

      // 触发视图更新
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },

    // 清空子级联动
    clearChildCascade(parentFieldName) {
      const childChildFields = this.fields.filter((item) =>
        item.cascadeCode?.split(',')?.includes(parentFieldName)
      )

      if (childChildFields.length) {
        childChildFields.forEach(childField => {
          this.form[childField.name] = ''
          this.$set(childField, 'children', [])
          // 递归清空更深层级的级联
          this.clearChildCascade(childField.name)
        })
      }
    },

    // 清理级联缓存
    clearCascadeCache() {
      this.cascadeRequestCache = {}
      this.cascadeLoadingFields.clear()
      Object.keys(this.cascadeDebounceTimers).forEach(key => {
        clearTimeout(this.cascadeDebounceTimers[key])
      })
      this.cascadeDebounceTimers = {}
    },
    autocompleteSearch(searchStr, cb) {
      let children = []
      if (this.currItem) {
        children = this.currItem.children.map((o) => {
          return { name: o.name, value: o.value }
        })
        if (searchStr) {
          children = children.filter((o) => o.name.indexOf(searchStr) !== -1)
        }
        this.currItem = null
      }
      cb(children)
    },

    setFormData(field, data) {
      this.$set(this.form, field.name.trim(), JSON.parse(JSON.stringify(data)))
    },

    replaceCascadeUrl(url) {
      let resultUrl = url
      const replacer = url.match(/\$\{(.+?)\}/g)
      replacer.forEach((key) => {
        resultUrl = resultUrl.replace(
          [key],
          this.form[key.match(/\$\{(.+?)\}/)[1]]
        )
      })
      return resultUrl
    },

    triggerPopupSelect(field) {
      clearTimeout(this.triggerTimer)
      this.triggerTimer = setTimeout(() => {
        const { dataSource, dataSourceType } = field
        let dispatchClickEvt = !this.disabled && !this.hasEditTwiceJudge(field)
        if (
          !this.disabled &&
          !this.hasEditTwiceJudge(field) &&
          dataSource &&
          dataSourceType === 'T'
        ) {
          // 条件触发
          const isTriggerPopup =
            typeof this.beforePopupCondition === 'function'
              ? this.beforePopupCondition(field)
              : true

          if (isTriggerPopup) {
            dispatchClickEvt = false
            this.showPopupSelect(field)
          }
        }

        if (dispatchClickEvt) {
          this.$root.$emit(`inputClicked`, field)
        }
      }, 300)
    },
    triggerPopupEdit(field) {
      // 双击编辑
      if (this.controlTypeField(field) === 'popupEdit') {
        clearTimeout(this.triggerTimer)
      }
    },
    showPopupSelect(field) {
      this.popupSelectVisible = true
      this.popupSelectName = field.name
      this.popupSelectTitle = field.title + '选择'
      this.popupSelectDataSource = field.dataSource
      this.popupSelectField = field // 保存字段配置
    },
    changeCodemirror(val, fieldName) {
      this.form[fieldName] = val
    },
    compareTwoObjectsDiff(formValue, form) {
      // 以模板字段key比较formValue和form的差异
      const diff = {}
      const fieldsKey = this.recordFields.map((field) => field.name)
      formValue && fieldsKey.forEach((key) => {
        if (formValue.hasOwnProperty(key)) {
          const currentValue = form[key]
          if (Array.isArray(currentValue)) {
            if (formValue[key] !== currentValue.join(',')) {
              diff[key] = formValue[key] && formValue[key].length ? formValue[key].split(',') : []
            }
          } else {
            if (formValue[key] !== currentValue) {
              diff[key] = formValue[key]
            }
          }
        }
      })
      return diff
    },
    getFormFullValue() {
      const data = Object.assign({}, this.formValue ?? {}, this.form)
      Object.keys(data).forEach((key) => {
        if (Array.isArray(data[key]) && key.includes('List')) {
          // 移除下级表单数据
          delete data[key]
        }
      })
      return data
    },

    // select 数据太多 懒加载
    setSelectChildrenLazy(fields) {
      fields.forEach((field) => {
        if (
          this.controlTypeField(field) === 'select' &&
          Array.isArray(field.lazyData) &&
          !field.children
        ) {
          this.$set(
            field,
            'children',
            field.lazyData.slice(0, this.selectLazyDataMaxItem)
          )
        }
      })
    },

    // 计算是否需要显示字段名称提示
    showFieldTitleTooltip(field) {
      const { title, tipInfo } = field
      // 使用计算属性获取实时的必填状态,字段未显示则不必填
      const required = (this.computedFieldsRequired[field.name] || false) && this.computedFieldsVisible[field.name]
      const width = useTextWidth(title)
      return (
        width >
        this.numericalLabelWidth -
        20 - // padding
        (required ? 6 : 0) - // 必填*
        (tipInfo ? 14 : 0) // ?提示
      )
    },
    // 计算是否需要显示内容提示
    showFieldValueTooltip(label, field) {
      //判断当前组件是否为多行文本
      const type = this.controlTypeField(field) === 'textarea'
      //判断当前组件是否有值
      if (!label || type) return true
      const { tipInfo } = field
      // 使用计算属性获取实时的必填状态
      const required = (this.computedFieldsRequired[field.name] || false) && this.computedFieldsVisible[field.name]
      const elements = document.querySelectorAll('.form-item__' + field.name)
      // 注意：querySelectorAll返回的是一个NodeList
      let domWidth
      if (elements.length > 0) {
        domWidth = elements[0].offsetWidth - this.numericalLabelWidth -
          20 - // padding
          (required ? 6 : 0) - // 必填*
          (tipInfo ? 14 : 0) // ?提示; // 或使用其他获取宽度的方法
      }
      const width = useTextWidth(label)
      return !(width > domWidth)
    },



    // 更新表单验证规则
    updateFormRules() {
      this.fields?.forEach((field) => {
        const isRequired = (this.computedFieldsRequired[field.name] || false) && this.computedFieldsVisible[field.name]

        if (field.conditionRequired && this.isJsonResolve(field.conditionRequired)) {
          const config = JSON.parse(field.conditionRequired)
          this.setFieldRule(field.name, {
            required: isRequired,
            message: isRequired ? config.tipMsg : `请填写${field.title}`,
            trigger: ['blur', 'change']
          })
        } else if (!this.hasEditTwiceJudge(field)) {
          this.setFieldRule(field.name, {
            required: isRequired,
            message: `请填写${field.title}`,
            trigger: ['blur', 'change']
          })
        }

        // 同步更新字段的 required 属性
        // this.$set(field, 'required', isRequired)
      })
    },

    // 统一的验证规则设置方法
    setFieldRule(fieldName, rule) {
      if (!fieldName) return

      if (rule === null || rule === undefined) {
        // 删除验证规则
        this.$set(this.rules, fieldName, null)
      } else if (Array.isArray(rule)) {
        // 设置多个验证规则
        this.$set(this.rules, fieldName, rule)
      } else {
        // 设置单个验证规则
        this.$set(this.rules, fieldName, [rule])
      }
    },

    // 批量设置验证规则
    setFieldRules(rulesMap) {
      Object.keys(rulesMap).forEach(fieldName => {
        this.setFieldRule(fieldName, rulesMap[fieldName])
      })
    },

    // 清除字段验证规则
    clearFieldRule(fieldName) {
      this.setFieldRule(fieldName, null)
      this.$refs.form?.clearValidate(fieldName)
    },

    // 批量清除验证规则
    clearFieldRules(fieldNames) {
      fieldNames.forEach(fieldName => {
        this.clearFieldRule(fieldName)
      })
    },

    // 添加数据联动处理方法
    handleDataLinkage(field, value, forceExecute = false) {
      // 检查是否需要执行数据联动：强制执行 或 字段已被用户交互过
      if (!forceExecute && !this.isFieldInteracted(field?.name)) {
        // console.log(`字段 ${field?.name} 未被用户交互，跳过数据联动修改`)
        return
      }

      if (field?.dataLinkage) {
        try {
          const linkageConfig = JSON.parse(field.dataLinkage)
          const currentConfig = linkageConfig.dataLinkage[value]

          // console.log(`执行字段 ${field.name} 的数据联动修改，值:`, value)

          if (currentConfig && Array.isArray(currentConfig)) {
            currentConfig.forEach(config => {
              if (config.field && config.value !== undefined) {
                // 通过事件总线发送更新事件
                LinkageEventBus.$emit(LINKAGE_EVENTS.VALUE_UPDATE, {
                  targetFormId: config.tableName || this.formId,
                  field: config.field,
                  value: config.value,
                  formShow: config.formShow == null ? null : config.formShow,
                  sourceFormId: this.formId,
                  sourceField: field.name,
                  triggerValue: value,
                  formItemType: 'select',
                  children: linkageConfig.customDropdowns[config.field] || [],
                  isInitHide: false
                })
              }
            })
          } else {
            LinkageEventBus.$emit(LINKAGE_EVENTS.VALUE_UPDATE, {
              targetFormId: field.tableName,
              field: null,
              value: null,
              initHide: null,
              sourceFormId: null,
              sourceField: null,
              triggerValue: null,
              formItemType: null,
              children: null,
              isInitHide: !!value
            })
          }
        } catch (e) {
          console.error('数据联动配置解析失败:', e)
        }
      }

      // 发送字段变化事件
      LinkageEventBus.$emit(LINKAGE_EVENTS.FIELD_CHANGE, {
        formId: this.formId,
        field: field?.name,
        value
      })
    },

    // 删除日期时触发
    handleRangeDatePickerChange(fieldName) {
      // 修复：正确处理数字0，确保数字0不被当作空值
      const fieldValue = this.form[fieldName]
      if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
        this.$emit('returnFormValue', deepClone(this.form), this.groupMark)
      }
    },

    // 监听表单值变化，更新条件必填状态
    updateRequiredStatus() {
      // 直接调用新的更新方法
      this.updateFormRules()

      // 强制触发整个组件的重新渲染，确保视觉更新
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },

    isJsonResolve(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },

    // 记录用户交互的字段
    markFieldAsInteracted(fieldName) {
      if (fieldName) {
        this.$set(this.userInteractedFields, fieldName, true)
        // console.log(`字段 ${fieldName} 已被用户交互`)
      }
    },

    // 检查字段是否被用户交互过
    isFieldInteracted(fieldName) {
      return !!this.userInteractedFields[fieldName]
    },

    // 重置用户交互状态
    resetUserInteraction() {
      this.userInteractedFields = {}
      // console.log('用户交互状态已重置')
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    // 清理级联缓存和定时器
    this.clearCascadeCache()

    // 清理fields监听器防抖定时器
    if (this.fieldsWatcherTimer) {
      clearTimeout(this.fieldsWatcherTimer)
      this.fieldsWatcherTimer = null
    }

    // 清理数据联动事件监听
    if (this.formId) {
      LinkageEventBus.$off(LINKAGE_EVENTS.VALUE_UPDATE)
      LinkageEventBus.$off(LINKAGE_EVENTS.FIELD_CHANGE)
    }
  },

  components: {
    InputGroup,
    FormColorPick,
    PopupSelect: () => import('./PopupSelect'),
    CodeMirror: () => import('@/components/Editor/CodeMirror')
  }
}
</script>

<style lang='scss' scoped>
::v-deep {
  .form-input-change {
    input {
      border-color: #e6a23c;
    }

    .el-select__tags-text,
    input {
      color: #e6a23c !important;
    }
  }

  .form-item-switch {
    .el-form-item__label {
      border-right-width: 1px !important;
    }

    .el-form-item__content {
      vertical-align: middle;
    }
  }
}

.el-form {
  .el-date-editor,
  .el-input-number,
  .el-cascader,
  .el-select {
    width: 100%;
  }

  .svg-icon {
    margin-left: 0.1em;
  }

  ::v-deep.el-input .el-icon-search {
    color: transparent;
  }

  ::v-deep .el-switch__core {
    width: 50px !important;
  }

  ::v-deep {
    .form-label-tooltip,
    .content-tip {
      max-width: 300px;
      word-wrap: break-word;
    }

    .form-select-input {
      .el-tag {
        background: none;
        border: 0;
        padding-left: 9px;
        font-size: 13px;

        span {
          color: #606266;
          font-weight: 400;
        }

        i {
          display: none;
        }
      }
    }

    .el-input__inner {
      color: #606266;
    }

    .el-input-number {
      .el-input-number__decrease,
      .el-input-number__increase {
        display: none;
      }
    }

    .el-input-number--small .el-input__inner {
      padding-left: 0px;
      padding-right: 0px;
    }

    .el-form-item__label {
      padding: 0 10px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  .el-form-item:last-child {
    ::v-deep.el-form-item__content {
      width: auto;
    }
  }

  .input-prefix,
  .input-suffix {
    width: 60px;
    display: inline-block;
    background-color: #f8f8f9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  br + br {
    display: none;
  }

  .disable-input {
    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }
  }

  .label-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
