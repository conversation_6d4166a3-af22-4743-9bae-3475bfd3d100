# PopupSelect 多选功能使用说明

## 概述

PopupSelect 组件现已支持多选功能，可以在分页和搜索的情况下实时展示当前选中的数据，并支持跨页选择状态保持。

## 新增属性

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| isMultiSelect | Boolean | false | 是否启用多选模式 |
| displayField | String | 'name' | 选中数据展示的字段名 |
| uniqueKey | String | 'id' | 用于去重的唯一标识字段 |
| initMatchField | String | '' | 初始化选中时用于匹配的字段名，为空时使用displayField |

## 使用方式

### 单选模式（默认）

```vue
<PopupSelect
  :visible="showDialog"
  :data="formData"
  title="选择数据"
  dataSource="your_query_code"
  @submitPopupSelect="handleSubmit"
  @cancelPopupSelect="showDialog = false"
/>
```

### 多选模式

```vue
<PopupSelect
  :visible="showDialog"
  :data="formData"
  title="选择数据"
  dataSource="your_query_code"
  :isMultiSelect="true"
  displayField="name"
  uniqueKey="id"
  @submitPopupSelect="handleMultiSubmit"
  @cancelPopupSelect="showDialog = false"
/>
```

### 多选模式带初始值

```vue
<PopupSelect
  :visible="showDialog"
  :data="{ selectedUsers: '张三,李四,王五' }"
  name="selectedUsers"
  title="选择用户"
  dataSource="user_query_code"
  :isMultiSelect="true"
  displayField="userName"
  uniqueKey="userId"
  initMatchField="userName"
  @submitPopupSelect="handleMultiSubmit"
  @cancelPopupSelect="showDialog = false"
/>
```

## 功能特性

### 1. 实时选中数据展示
- 在表格上方显示已选中的数据
- 显示选中数量统计
- 支持删除单个选中项
- 支持清空所有选中项

### 2. 跨页选择状态保持
- 切换页面时保持之前页面的选中状态
- 搜索后保持已选中的数据
- 自动同步表格选中状态

### 3. 初始值支持
- 支持逗号分割的初始值自动选中
- 通过 `data[name]` 传入初始值
- 可配置匹配字段名
- 自动在数据加载后进行初始化选中

### 3. 数据返回格式

#### 单选模式
```javascript
// 返回单个对象
{
  id: 1,
  name: "选中项",
  // ... 其他字段
}
```

#### 多选模式
```javascript
// 返回合并后的单个对象，各字段值用逗号连接
{
  id: "1,2,3",
  name: "选中项1,选中项2,选中项3",
  type: "类型A,类型B",
  // ... 其他字段
}
```

**多选数据合并规则：**
- 所有选中项的同名字段值会被合并
- 多个值之间用逗号（,）连接
- 自动去重，避免重复值
- 忽略空值（null、undefined、空字符串）
- 最终返回一个合并后的对象，保持与单选模式相同的数据结构

## 事件处理

### submitPopupSelect 事件

```javascript
methods: {
  // 单选模式
  handleSubmit(data) {
    console.log('选中的数据:', data)
    // 处理单个选中项
  },

  // 多选模式
  handleMultiSubmit(mergedData) {
    console.log('合并后的数据:', mergedData)
    // 处理合并后的数据，格式与单选模式一致
    // 例如：mergedData.name 可能是 "张三,李四,王五"
    const names = mergedData.name ? mergedData.name.split(',') : []
    console.log('选中的姓名列表:', names)
  }
}
```

## 注意事项

1. **uniqueKey 配置**：确保 uniqueKey 指向的字段在数据中是唯一的，用于去重和状态保持
2. **displayField 配置**：选择合适的字段作为展示字段，建议使用有意义的名称字段
3. **向后兼容**：现有的单选模式代码无需修改，默认行为保持不变
4. **性能考虑**：大量数据选择时，组件会自动处理状态同步，但建议合理控制选择数量

## 样式自定义

选中数据展示区域使用了以下CSS类，可以根据需要进行样式覆盖：

- `.selected-data-area`：选中数据展示区域
- `.selected-header`：头部区域（标题和清空按钮）
- `.selected-content`：内容区域（选中项标签）
- `.selected-tag`：单个选中项标签

## 完整示例

```vue
<template>
  <div>
    <el-button @click="showDialog = true">选择数据</el-button>
    
    <PopupSelect
      :visible="showDialog"
      :data="searchParams"
      title="选择用户"
      dataSource="user_query"
      :isMultiSelect="true"
      displayField="userName"
      uniqueKey="userId"
      @submitPopupSelect="handleUserSelect"
      @cancelPopupSelect="showDialog = false"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      showDialog: false,
      searchParams: {},
      selectedUsers: []
    }
  },
  methods: {
    handleUserSelect(users) {
      this.selectedUsers = users
      this.showDialog = false
      this.$message.success(`已选择 ${users.length} 个用户`)
    }
  }
}
</script>
```
