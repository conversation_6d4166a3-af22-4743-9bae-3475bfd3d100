<template>
  <div style="min-height: 200px; padding: 20px">
    <!--    方案扩展-->
    <!--    qryEngineerScheme-->
    <commonQueryTable
      ref="commonQueryTable"
      :query-params="{ engineeringId: rowData.id, engineeringName: rowData.name }"
      :show-detail="false"
      :show-export="false"
      :show-search-btn="true"
      :fixed-height="250"
      :extend-data="rowData"
      :in-section-area="true"
      search-content="刷新"
      show-page
      :page-sizes="[5]"
      :span-method="spanMethod"
      class="queryTable"
      custom-row-buttons="schemeAction"
      @refreshTop="refresh"
      @tableDataLoaded="loaded"
      code="qryEngineerScheme"
      style="
        padding-top: 0;
        margin-top: 0;
        margin-left: 10px;
        margin-right: 10px;
        padding-bottom: 10px;
        z-index: 999;
      "
    />
  </div>
</template>

<script>
export default {
  name: 'engineerSchemeExpand',
  components: {
    // 动态引入
    commonQueryTable: () =>
      import('@/components/CommonQuery/commonQueryTable.vue'),
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {
      showTable: false,
      spanScope: {}
    };
  },
  created() {
  },
  mounted() {
    this.$emit('contentUpdated')

    //延迟加载
    setTimeout(() => {
      this.showTable = true;
    }, 1000);
  },
  methods: {
    refresh(data) {
      console.log('refreshTop', data);
      //调用子组件的方法
      this.rowData[data] = this.rowData[data] + 1;
    },
    loaded(data = []) {
      let index = 0
      this.spanScope = data.map(o => o.mode)
        .reduce((res, item) => {
          if (res[item]) {
            res[item].num++
          } else {
            res[item] = {
              index,
              num: 1
            }
          }
          index ++
          return res
        }, {})
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 && this.spanScope[row.mode]) {
        if (this.spanScope[row.mode].index === rowIndex) {
          return {
            rowspan: this.spanScope[row.mode].num,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .buttons-area {
  display: none;
}

::v-deep .el-table__expanded-cell {
  padding: 0 !important;
}

::v-deep .has-gutter .cell {
  font-size: 12px;
}

.queryTable {
  width: 100em;
  ::v-deep .table {
    min-height: 480px;
  }
}
</style>
