import request from '@/utils/request'

export function list(data) {
  return request({
    url: 'cov/gisLayerMenu/list',
    method: 'post',
    params: data
  })
}

export function save(data) {
  return request({
    data,
    url: 'cov/gisLayerMenu/save',
    method: 'post'
  })
}

export function deleteMenu(data) {
  return request({
    url: 'cov/gisLayerMenu/delete?gisLayerMenuId=' + data.gisLayerMenuId,
    method: 'delete'
  })
}

export function update(data) {
  return request({
    data,
    url: 'cov/gisLayerMenu/update',
    method: 'put'
  })
}

export function updateMenu(data) {
  return request({
    data,
    url: 'cov/gisLayerMenu/updateMenu',
    method: 'put'
  })
}

export function qryMenu(data) {
  return request({
    params: data,
    url: 'cov/gisLayerMenu/qryMenu',
    method: 'get'
  })
}

export function detail(id) {
  return request(`cov/gisLayerMenu/detail/${id}`)
}

export function tree() {
  return request('cov/gisLayerMenu/tree?status=2&userId=-1')
}

// 图层菜单
export function layerList(menuId, data) {
  return request({
    url: `cov/gisLayer/getByMenuId/${menuId}`,
    method: 'post',
    data
  })
}

// 图层菜单
export function menuLayer(data) {
  return request({
    url: 'cov/gisLayerGroup/list',
    method: 'post',
    params: data
  })
}

export function saveMenuLayer(data) {
  return request({
    data,
    url: 'cov/gisLayerGroup/saveBatch',
    method: 'post'
  })
}

export function updateMenuLayer(data) {
  return request({
    data,
    url: 'cov/gisLayerGroup/update',
    method: 'put'
  })
}
export function deleteMenuLayer(data) {
  return request({
    url: 'cov/gisLayerGroup/delete',
    method: 'delete',
    params: data
  })
}
