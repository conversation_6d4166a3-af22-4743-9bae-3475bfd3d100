<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :rules="rules" :inline="true">
      <el-form-item label="流程" prop="flowKey">
        <el-select
          v-model="queryParams.flowKey"
          placeholder="请选择流程"
          clearable
          size="small"
          style="width: 240px"
          @change="selectFlowKey"
        >
          <el-option
            v-for="item in flowKeyOptions"
            :key="item.id"
            :label="item.name"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工单号" prop="siteIdCmcc">
        <el-input
          v-model="queryParams.siteIdCmcc"
          placeholder="请输入工单号"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="表名" prop="tableName">
        <el-select
          filterable
          v-model="queryParams.tableName"
          placeholder="请选择表名"
          clearable
          size="small"
          style="width: 240px"
          @change="selectTableName"
        >
          <el-option
            v-for="item in tableNameOptions"
            :key="item.id"
            :label="item.tableRelname+'('+item.tableName+')'"
            :value="item.tableName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="字段名" prop="fieldName">
        <el-select v-model="queryParams.fieldName" filterable placeholder="请选择字段名"
                   clearable size="small" style="width: 240px" @change="selectFieldName">
          <el-option
            v-for="item in fieldNameOptions"
            :key="item.id"
            :label="item.column_comment+'('+item.column_name+')'"
            :value="item.column_name"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="阶段" prop="stageCode">
        <el-select
          v-model="queryParams.stageCode"
          placeholder="请选择阶段"
          clearable
          size="small"
          style="width: 240px"
        >
          <el-option
            v-for="item in stageCodeOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="字段值" prop="fieldValue">
        <el-select
          v-model="queryParams.fieldValue"
          placeholder="请选择值"
          clearable
          size="small"
          style="width: 240px"
          @change="selectFieldValue"
        >
          <el-option
            v-for="item in fieldValueOptions"
            :key="item.fieldValue"
            :label="item.fieldValue"
            :value="item.fieldValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :span="24" class="mb8">
      <el-button type="primary" @click="sumitUpdate">提交修改</el-button>
    </el-row>

    <el-table ref="multipleTable" :data="tableDataList" tooltip-effect="dark" style="width: 100%"
      @selection-change="handleSelectionChange" :cell-class-name="cellClassName">
      <el-table-column type="selection" width="40" />
      <el-table-column prop="siteIdCmcc" label="工单号" width="200" align="center"  />
      <el-table-column prop="stageCode" label="阶段" width="100" align="center" />
      <el-table-column prop="tableName" label="表名" width="160" align="center"/>
<!--      <el-table-column prop="fieldName" label="字段名"  width="160" align="center"/>-->
      <el-table-column prop="fieldNameText" label="字段名"  width="160" align="center"/>
      <el-table-column prop="differentiateFieldName" label="区别字段" width="160" align="center"/>
      <el-table-column prop="differentiateField" label="区别字段内容"  width="160" align="center"/>
      <el-table-column prop="sort" label="排序"  width="50" align="center"/>
      <el-table-column prop="fieldValue" label="值" width="260" fixed="right" >
        <template slot-scope="scope">
          <span v-if="!scope.row.isDict" >{{scope.row.fieldValue}}</span>
          <span v-if="scope.row.isDict">{{scope.row.fieldValueText}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="updateValue" label="修改值" width="260" fixed="right" >
        <template slot-scope="scope">
          <el-input v-if="!scope.row.isDict" v-model="scope.row.updateValue" placeholder="请输入修改值"></el-input>
          <el-select v-if="scope.row.isDict" v-model="scope.row.updateValue"  placeholder="请选择值"  clearable size="small"  >
            <el-option v-for="item in dictList" :key="item.code"
              :label="item.name"
              :value="item.code"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="remake" label="备注" width="260" fixed="right" >
        <template slot-scope="scope">
          <el-input v-model="scope.row.remake" placeholder="请输入备注"></el-input>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import   { commonQuery } from '@/api/kernel/query'
import { listModel} from "@/api/kernel/flow";
import { getOrderUpdateList,getTableRelationList,updateOrderList } from '@/api/site/plan/orderUpdate'
export default {
  computed: {},
  data() {
    return {
      queryParams: {
        flowKey: null,
        siteIdCmcc: null,
        tableName: null,
        fieldName: null,
        stageCode: null
      },
      BusinessType: ['site', 'open', 'adjustment', 'JZGH', 'GH'],
      rules: {
        flowKey: [
          { required: true, message: '请选择流程', trigger: 'blur' }
        ],
        siteIdCmcc: [
          { required: true, message: '请输入需求工单号', trigger: 'blur' }
        ],
        tableName: [
          { required: true, message: '请选择表名', trigger: 'blur' }
        ],
        fieldName: [
          { required: true, message: '请选择字段名', trigger: 'blur' }
        ],
      },
      BusinessTable: [
        { business: "GH", tableName: 't_5g_site', parent: "", parentField: "" },
        { business: "GH", tableName: 't_5g_enodeb', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_cell', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_rru', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_antenna', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_equipment', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_extend_device', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_board', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_transfer_config', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_transfer_network', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_roof', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_platform', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_room', parent: "t_5g_site", parentField: "site_uuid" },
        { business: "GH", tableName: 't_5g_shelf', parent: "t_5g_site", parentField: "site_uuid" },

      ],
      flowKeyOptions: [
        { name: '端到端覆盖流程新版本(串行版本)', code: 'cmcc_5g_plan' },
        { name: '端到端覆盖流程新版本(节点并行版)', code: 'cmcc_5g_plan_2' }
      ],
      siteIdCmccOptions: [],
      tableNameOptions: [],
      fieldNameOptions: [],
      stageCodeOptions: [],
      tableData: [],
      multipleSelection: [],
      fieldValueOptions:[],
      tableDataList:[],
      seColor:"black",
      dictList:[],
      selectStyle:'custom-cell-class',
      isSelect:false,
    };
  },
  async created() {
    const  qra ={
      pageNum:1,
      pageSize:30
    }
    listModel(qra).then(res => {
      this.flowKeyOptions = res.data
    })
    const stageCodeOptions = await commonQuery('qryBusinessStageCode', { type: 'GH' })
    this.stageCodeOptions = stageCodeOptions.data

  },
  methods: {
    toCamelCase(str) {
      return str.replace(/[-_]([a-z])/g, (match) => match[1].toUpperCase());
    },
    async validate() {
      this.$refs['queryForm'].validate(async (valid) => {
        if (valid) {
          const dict = await commonQuery('qraTabFeildDict', {
            tableName: this.queryParams.tableName,
            fieldName: this.toCamelCase(this.queryParams.fieldName),
            businessType:this.tableNameOptions[0].business
          })
          if (dict.data.length > 0) {
            await this.getDicts(dict.data[0].data_source).then(response => {
              this.dictList = response.data;
              this.getList()
            });
          }else{
            this.dictList = []
            this.getList()
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    getList(){
      getOrderUpdateList(this.queryParams).then(res => {
        this.tableData = res.data
        const fieldName = this.fieldNameOptions.filter(obj => {
          return this.queryParams.fieldName === obj.column_name
        })
        this.tableData.forEach(item => {
          this.$set(item, 'isDict', this.dictList.length > 0)
          if(this.dictList.length > 0) {
            const fieldValueText = this.dictList.filter(a => {
              return a.code === item.fieldValue
            })
            this.$set(item, 'fieldValueText', fieldValueText[0]?.name)
          }
          this.$set(item, 'fieldNameText', fieldName[0].column_comment + '(' + fieldName[0].column_name + ')')
        })
        this.tableDataList = this.tableData
        this.fieldValueOptions = []
        this.fieldValueOptions = Array.from(new Set(this.tableData.map(item => item.fieldValue)))
          .map(fieldValue => this.tableData.find(item => item.fieldValue === fieldValue));
      })
    },
    handleQuery() {

      console.log(this.queryParams)
      this.validate()
    },
    resetQuery() {
      this.queryParams = {
        flowKey: null,
        siteIdCmcc: null,
        tableName: null,
        fieldName: null,
        stageCode: null
      }
    },
    selectFlowKey() {
      const pra = {
        flowKey: this.queryParams.flowKey,
        isEdit: true
      }
      getTableRelationList(pra).then(res => {
        this.tableNameOptions = res.data
      })
    },
    async selectTableName(item) {
      const flowKeyOptions = await commonQuery('qryColumnByTab', { tableName: item })
      const list = Array.from(new Set(flowKeyOptions.data.map(item => item.column_name)))
        .map(column_name => flowKeyOptions.data.find(item => item.column_name === column_name));
      const config = JSON.parse(this.$store.getters.sysconfig.orderFieldIsEdit);
      console.log("config",config)
      this.fieldNameOptions =list.filter(item => {
        const data = config.filter(obj =>{ return obj ===item.column_name})
        return !data.length > 0
      })
    },
    async selectFieldName(item) {
      this.handleQuery()
    },
    selectFieldValue(item){
      this.tableDataList = this.tableData.filter(item=>{ return item.fieldValue === this.queryParams.fieldValue})
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      if(this.multipleSelection.length > 0){
        this.isSelect= false
        this.cellClassName()
      }
    },
    cellClassName({row,column,rowIndex,columnIndex}){
      console.log(row,column,rowIndex,columnIndex)
      if(columnIndex===0  && this.isSelect){
        return "cellName"
      }
    },
    sumitUpdate() {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length == 0) {
        this.$message.error('请选择需要更新数据的阶段')
        this.isSelect= true
        this.cellClassName()
      } else {
        this.$alert('确定修改选中字段数据吗？', '提示', {
          type: 'warning'
        }).then(() => {
          this.updateList()
        })
      }
    },
    updateList(){
      updateOrderList(this.multipleSelection).then(res=>{
        if(res.code===200){
          this.$message.success('修改成功')
        }else{
          this.$message.error('修改失败')
        }
      })
    },
  }
}
</script>
<style>
.cellName{
  border-color: #c50707 !important;
  background-color: #01baff;
}
</style>
