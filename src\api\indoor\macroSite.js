import request from "@/utils/request";

export const taskList = params => {
  return request({
    url: '/macrosite/service/task/list',
    method: 'get',
    params
  })
}

export const taskAdd = data => {
  return request({
    url: '/macrosite/service/task',
    method: 'post',
    data
  })
}

export const taskUpdate = data => {
  return request({
    url: '/macrosite/service/task',
    method: 'put',
    data
  })
}

export const taskRemove = ids => {
  return request({
    url: `/macrosite/service/task/${ids}`,
    method: 'delete',
  })
}

export const cadList = params => {
  return request({
    url: '/macrosite/service/cad/list',
    method: 'get',
    params
  })
}

export const cadAdd = data => {
  return request({
    url: '/macrosite/service/cad',
    method: 'post',
    data
  })
}

export const cadUpdate = data => {
  return request({
    url: '/macrosite/service/cad',
    method: 'put',
    data
  })
}

export const cadRemove = ids => {
  return request({
    url: `/macrosite/service/cad/${ids}`,
    method: 'delete',
  })
}

export const configList = params => {
  return request({
    url: '/macrosite/config/list',
    method: 'get',
    params
  })
}

export const configSave = data => {
  return request({
    url: '/macrosite/config/save',
    method: 'post',
    data
  })
}

export const configRemove = ids => {
  return request({
    url: `/macrosite/config/${ids}`,
    method: 'delete',
  })
}
