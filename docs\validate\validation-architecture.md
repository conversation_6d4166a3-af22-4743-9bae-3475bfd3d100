# 表单校验架构文档

## 整体架构概览

### 校验体系结构
```
fieldConfigValidate (主入口)
├── fieldsRequiredValid (必填校验)
├── siteFieldsValid (站点数据校验)
│   └── recursivePlanList (递归校验)
├── sensitiveWordsValidate (敏感词校验) [新增]
└── interfaceValid (接口校验)
    ├── enodebIdRangeValid (基站ID范围校验)
    ├── cellTacRangValid (TAC范围校验)
    └── repeatFieldsValueValid (重复值校验)
```

### 校验类型分类

#### 1. 客户端校验 (同步)
- **必填校验**: `fieldsRequiredValid`
- **格式校验**: 数字类型、正则表达式
- **自定义校验**: `fieldCustomValidate`
- **敏感词校验**: `sensitiveWordsValidate` (异步)

#### 2. 服务端校验 (异步)
- **业务规则校验**: `interfaceValid`
- **数据一致性校验**: `repeatFieldsValueValid`
- **范围校验**: `enodebIdRangeValid`, `cellTacRangValid`

## 校验执行流程

### 1. 主流程
```javascript
export async function fieldConfigValidate(fields, planSite, inResource) {
  const result = []
  
  // 1. 基础信息校验 (同步)
  if (inResource) {
    const valid = fieldsRequiredValid(fields.XQJD_site, planSite.xqSite)
    result.push({ fields: valid, name: 'basic', title: '基本信息' })
  }
  
  // 2. 站点数据校验 (同步)
  const siteValid = siteFieldsValid(fields, planSite.siteList, jump)
  
  // 3. 敏感词校验 (异步)
  const sensitiveWordsValid = await sensitiveWordsValidate(fields, planSite)
  
  // 4. 合并结果
  return [...result, ...siteValid, ...sensitiveWordsValid]
    .filter(i => i.fields.length)
}
```

### 2. 递归校验机制
```javascript
function recursivePlanList(fields, data, validKey, parentTitle, jump) {
  // 分离 List 数据和普通字段
  const validPlanList = {}
  
  // 当前层级字段校验
  const currentFields = getCurrentKeyFields(fields, validKey, data, childType)
  if (currentFields) {
    const valid = fieldsRequiredValid(currentFields, data)
    result.push({ fields: valid, name: validKey, title: parentTitle.title })
  }
  
  // 递归处理子级数据
  Object.entries(validPlanList).forEach(([key, value]) => {
    value.forEach((child, childIndex) => {
      const valid = recursivePlanList(fields, child, childKey, { title }, jump)
      result = [...result, ...valid]
    })
  })
}
```

## 字段配置系统

### 字段配置结构
```javascript
const fieldConfig = {
  name: 'fieldName',           // 字段名
  title: '字段显示名',          // 显示标题
  formType: 'input',           // 表单类型
  formShow: true,              // 是否显示
  formEdit: true,              // 是否可编辑
  required: true,              // 是否必填
  jsValid: '[]',               // JS校验规则
  conditionRequired: '{}',     // 条件必填
  param5: false                // 增删调参数
}
```

### 字段类型映射
```javascript
function getCurrentKeyFields(fields, key, data, type) {
  let currentKey = key
  
  // 特殊字段类型映射
  const keyMappings = {
    'transferNetwork': 'transfer_network',
    'transferConfig': `transfer_config_${type || 'transfer_control_plane'}`,
    'baseBand': 'equipment_baseBand',
    'peitao': 'equipment_peitao',
    'bbuExtendDevice': 'extend_device_baseband',
    'rruExtendDevice': 'extend_device_rru'
  }
  
  // 动态映射逻辑
  if (keyMappings[key]) {
    currentKey = keyMappings[key]
  } else if (['board', 'port'].includes(key)) {
    currentKey = data.equipment?.toLowerCase() === 'rru' 
      ? `${key}_rru` 
      : `${key}_baseBand`
  }
  
  return fields[currentKey]
}
```

## 校验规则配置

### 1. JS校验规则格式
```javascript
const jsValidConfig = [
  {
    type: 'reg',                    // 正则校验
    body: '^[0-9]+$',              // 正则表达式
    message: '只能输入数字'          // 错误信息
  },
  {
    type: 'range',                  // 范围校验
    body: [[0, 100], [200, 300]],  // 范围数组
    message: '值必须在指定范围内'
  },
  {
    type: 'relevance',              // 关联校验
    relevance: 'relatedField',      // 关联字段
    body: { 'value1': [0, 10] },    // 关联规则
    message: '关联字段校验失败'
  },
  {
    type: 'only',                   // 唯一性校验
    message: '值不能重复'
  }
]
```

### 2. 条件必填配置
```javascript
const conditionRequired = {
  rule: "data.fieldA === 'value1'",  // 条件表达式
  tipMsg: '当字段A为value1时此字段必填'  // 提示信息
}
```

## 错误处理机制

### 错误信息格式
```javascript
const validationResult = {
  fields: [{
    name: 'fieldName',              // 字段名
    title: '字段显示名',             // 字段标题
    message: ['错误信息1', '错误信息2'], // 错误信息数组
    required: true,                 // 是否必填错误
    errorType: 'normal'             // 错误类型
  }],
  name: 'validationGroup',          // 校验组名
  title: '方案1--基站1'             // 层级标题
}
```

### 错误类型分类
- `normal`: 普通校验错误
- `formTypeError`: 表单类型错误
- `required`: 必填校验错误
- `sensitiveWords`: 敏感词错误

## 性能优化策略

### 1. 校验优化
```javascript
// 字段过滤优化
const shouldValidate = (field, data) => {
  // 跳过隐藏字段
  if (!field.formShow) return false
  
  // 跳过只读字段
  if (!field.formEdit && data.modify !== 'add') return false
  
  // 跳过空值字段 (除必填外)
  if (!field.required && isNull(data[field.name])) return false
  
  return true
}

// 批量校验优化
const batchValidate = (fields, dataList) => {
  return dataList.map(data => 
    fields.filter(field => shouldValidate(field, data))
          .map(field => validateField(field, data))
  ).flat()
}
```

### 2. 缓存策略
```javascript
// 字段配置缓存
const fieldConfigCache = new Map()

// 校验结果缓存 (短期)
const validationCache = new Map()

// 系统参数缓存
const systemConfigCache = {
  data: new Map(),
  ttl: 5 * 60 * 1000  // 5分钟
}
```

## 扩展点设计

### 1. 自定义校验器
```javascript
// 校验器注册机制
const customValidators = new Map()

const registerValidator = (name, validator) => {
  customValidators.set(name, validator)
}

// 使用示例
registerValidator('phoneNumber', (value) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(value) ? null : '手机号格式不正确'
})
```

### 2. 校验中间件
```javascript
// 校验前置处理
const beforeValidate = (fields, data) => {
  // 数据预处理
  return { fields: processFields(fields), data: processData(data) }
}

// 校验后置处理
const afterValidate = (result) => {
  // 结果后处理
  return processResult(result)
}
```

### 3. 插件化架构
```javascript
const validationPlugins = []

const useValidationPlugin = (plugin) => {
  validationPlugins.push(plugin)
}

// 插件执行
const executePlugins = async (context) => {
  for (const plugin of validationPlugins) {
    context = await plugin.execute(context)
  }
  return context
}
```

## 最佳实践

### 1. 校验规则设计
- **渐进式校验**: 先客户端后服务端
- **错误信息友好**: 提供具体的修改建议
- **性能优先**: 优先执行轻量级校验
- **可配置性**: 支持业务规则的灵活配置

### 2. 代码组织
- **单一职责**: 每个校验函数只负责一种校验
- **可测试性**: 校验逻辑与UI逻辑分离
- **可维护性**: 使用配置驱动而非硬编码
- **可扩展性**: 预留扩展接口和插件机制

### 3. 错误处理
- **优雅降级**: 校验失败不影响其他功能
- **日志记录**: 记录校验异常便于排查
- **用户体验**: 提供清晰的错误提示和修复建议

### 4. 性能考虑
- **懒加载**: 按需加载校验规则
- **防抖处理**: 避免频繁触发校验
- **缓存机制**: 合理使用缓存减少重复计算
- **异步优化**: 合理使用异步避免阻塞UI

## 监控和调试

### 1. 性能监控
```javascript
const performanceMonitor = {
  start: (name) => console.time(name),
  end: (name) => console.timeEnd(name),
  memory: () => performance.memory
}

// 使用示例
performanceMonitor.start('validation')
await fieldConfigValidate(fields, planSite)
performanceMonitor.end('validation')
```

### 2. 调试工具
```javascript
const debugMode = process.env.NODE_ENV === 'development'

const debugLog = (message, data) => {
  if (debugMode) {
    console.group(`[Validation Debug] ${message}`)
    console.log(data)
    console.groupEnd()
  }
}
```

### 3. 错误追踪
```javascript
const errorTracker = {
  track: (error, context) => {
    console.error('Validation Error:', error)
    // 发送到错误监控服务
  }
}
```
