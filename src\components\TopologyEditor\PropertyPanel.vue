<template>
  <div class="topology-property-panel" v-if="selected">
    <el-form :model="form" label-width="60px" size="small">
      <el-form-item label="标题">
        <el-input v-model="form.title" @input="emitUpdate" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="form.description" @input="emitUpdate" />
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="form.type" @change="emitUpdate">
          <el-option label="默认" value="default" />
          <el-option label="特殊" value="special" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <div v-else class="topology-property-panel-empty">请选择节点或连线</div>
</template>

<script>
export default {
  name: 'PropertyPanel',
  props: {
    selected: { type: Object, default: null }
  },
  data() {
    return {
      form: {
        title: '',
        description: '',
        type: 'default'
      }
    }
  },
  watch: {
    selected: {
      handler(val) {
        if (val) {
          this.form = {
            title: val.title || '',
            description: val.description || '',
            type: val.type || 'default'
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    emitUpdate() {
      this.$emit('update', { ...this.form })
    }
  }
}
</script>

<style scoped>
.topology-property-panel {
  width: 260px;
  background: #fff;
  border-left: 1px solid #e0e0e0;
  padding: 16px 12px;
  box-sizing: border-box;
  min-height: 100%;
}
.topology-property-panel-empty {
  width: 260px;
  background: #fafbfc;
  border-left: 1px solid #e0e0e0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}
</style>
