<template>
  <el-collapse v-model='transferCollapse' v-if="fields && getFields">
    <el-collapse-item name='transfer' title='传输配置'>
      <div class='transfer-wrapper'>
        <el-tabs
          @tab-click='currentTransferChildIndex = "0"'
          type='border-card'
          v-model='transferActive'
        >
          <el-tab-pane
            :key='item.code'
            :label='item.name'
            :name='String(index)'
            v-for='(item, index) in transferTabs'
          ></el-tab-pane>
        </el-tabs>

        <EditTab
          :editable='tabEditable'
          :index.sync='currentTransferChildIndex'
          :tabLabelTitle='getCurrentTransfer && getCurrentTransfer.name'
          :tabs='getTransferData(getCurrentTransferCode)'
          @addTab='addTransferTabs($event, getCurrentTransferCode)'
          @changeTabIndex='resetForm($event, getCurrentTransferCode)'
          @removeTab='removeTransferTabs($event, getCurrentTransferCode)'
        />

        <BasicForm
          :disabled='!isCurrentStage'
          :fields='getFields'
          :formValue='getTransferData(getCurrentTransferCode, +currentTransferChildIndex)'
          :groupColumn='4'
          :key='transferActive'
          @returnFormValue='setTransferData'
          ref='transfer-form'
          size='small'
          :groupMark="`${getTypeCode}`"
        />
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import { deepClone } from '@/utils'
import { useCommonQueryData } from '@/utils/useDictionary'
import BasicForm from '@/components/BasicComponents/Form'
import EditTab from '@/components/BasicComponents/EditTab'
export default {
  name: 'transferConfiguration',
  props: {
    enodeb: Object,
    fields: Object,
    tabEditable: Boolean,
    isCurrentStage: Boolean,
  },
  data() {
    return {
      // transferTabs: [
      //   { title: '接入网', code: 'transfer_network' },
      //   { title: '操作维护', code: 'transfer_maintain' },
      //   { title: '业务通道', code: 'transfer_passageway' },
      //   { title: '控制面', code: 'transfer_control_plane' },
      //   { title: '用户面', code: 'transfer_user_plane' },
      //   { title: 'OSS', code: 'transfer_oss' },
      //   { title: 'NTP', code: 'transfer_ntp' },
      //   { title: '接口配置', code: 'transfer_interface' },
      // ],
      transferTabs: [],
      transferCollapse: ['transfer'],
      transferActive: '0',
      currentTransferChildIndex: '0',
    }
  },
  watch: {
    transferActive(newval, oldval) {
      this.$nextTick(() => {
        this.resetForm(null, this.getCurrentTransferCode)
      })
    },
  },
  computed: {
    getTypeCode() {
      return         this.getCurrentTransferCode === 'transfer_network'
        ? this.getCurrentTransferCode
        : 'transfer_config_' + this.getCurrentTransferCode
    },
    getFields() {
      const code =
        this.getCurrentTransferCode === 'transfer_network'
          ? this.getCurrentTransferCode
          : 'transfer_config_' + this.getCurrentTransferCode
      return this.fields && this.fields[code]
    },
    getCurrentTransfer() {
      return this.transferTabs[this.transferActive]
    },
    getCurrentTransferCode() {
      return this.getCurrentTransfer?.code
    },
  },
  async mounted() {
    const transferTabs = await useCommonQueryData('TRANSFER_TABS')
    this.transferTabs = transferTabs || []

    // 生成默认传输配置数据避免除了第一个tab的表单 没有生成数据没有完成校验就通过
    this.transferTabs?.forEach((tab) => {
      const addData = { sort: 0, type: tab.code }
      if (tab.code === 'transfer_network') {
        if (this.enodeb &&  !this.enodeb.transferNetworkList) {
          this.$set(this.enodeb, 'transferNetworkList', [addData])
        }
      } else {
        if (this.enodeb && !this.enodeb.transferConfigList) {
          this.$set(this.enodeb, 'transferConfigList', [addData])
        } else {
          if (
            this.enodeb && !this.enodeb.transferConfigList.find(
              (item) => item.type === tab.code
            )
          ) {
            this.enodeb.transferConfigList.push(addData)
          }
        }
      }
    })
  },
  methods: {
    getTransferData(type, index) {
      if (this.enodeb && type) {
        if (type === 'transfer_network'  && this.enodeb && this.enodeb.transferNetworkList) {
          const transferData = this.enodeb.transferNetworkList
          if (!transferData || !transferData.length) {
            this.$set(this.enodeb, 'transferNetworkList', [{ sort: 0, type }])
          }
          return typeof index === 'number' ? transferData[index] : transferData
        } else {
          const addData = { type, sort: 0 }
          let transferData = this.enodeb.transferConfigList
          if (!transferData) {
            this.$set(this.enodeb, 'transferConfigList', [addData])
          } else {
            if (!transferData.find((item) => item.type === type)) {
              transferData.push(addData)
            }
          }

          const resultData = transferData?.filter((item) => item.type === type)
          return typeof index === 'number' ? resultData[index] : resultData
        }
      }
    },
    setTransferData(data, mark) {
      let currentChildData = this.getTransferData(
        this.getCurrentTransferCode,
        +this.currentTransferChildIndex
      )
      currentChildData && Object.assign(currentChildData, data)
    },

    addTransferTabs($event, code) {
      if (code === 'transfer_network' && this.enodeb && this.enodeb.transferNetworkList) {
        const currentData = this.enodeb.transferNetworkList
        const addTabData = deepClone(currentData[0], false)

        delete addTabData.id
        addTabData.sort = currentData.length
        currentData.push(addTabData)
      } else {
        const currentData = this.enodeb.transferConfigList.filter(
          (item) => item.type === code
        )
        const addTabData = deepClone(currentData[0], false)
        addTabData.sort = currentData.length
        addTabData.type = code
        this.enodeb.transferConfigList.push(addTabData)
      }
    },
    removeTransferTabs($event, code) {
      if (code === 'transfer_network' && this.enodeb && this.enodeb.transferNetworkList) {
        const currentData = this.enodeb.transferNetworkList
        currentData.splice($event, 1)
      } else {
        const currentData = this.enodeb.transferConfigList
        const removeIndex = currentData.find(
          (item) => item.type === code && $event == item.sort
        )
        currentData.splice(removeIndex, 1)
      }
    },
    resetForm($event, code) {
      this.$nextTick(() => {
        const form = this.$refs['transfer-form']
        const currentData = this.getTransferData(
          this.getCurrentTransferCode,
          +this.currentTransferChildIndex
        )

       form && form.setFormValue(currentData)
      })
    },
  },
  components: { EditTab, BasicForm },
}
</script>

<style lang='scss' scoped>
::v-deep {
  form .el-form-item:last-child {
    display: none;
  }
}
.transfer-wrapper {
  border: 1px solid #dfe4ed;
  border-top: 0;
  .edit-tab,
  .el-form {
    padding: 0 15px;
  }
  ::v-deep .edit-tab {
    padding-bottom: 10px;
    .tab-add {
      right: 15px;
    }
  }
}
</style>
