import request from '@/utils/request'

// 查询权限列表
export function listPermission(query) {
  return request({
    url: '/doc/permission/list',
    method: 'get',
    params: query
  })
}

// 查询权限详细
export function getPermission(id) {
  return request({
    url: '/doc/permission/' + id,
    method: 'get'
  })
}

// 新增权限
export function addPermission(data) {
  return request({
    url: '/doc/permission',
    method: 'post',
    data: data
  })
}

// 修改权限
export function updatePermission(data) {
  return request({
    url: '/doc/permission',
    method: 'put',
    data: data
  })
}

// 删除权限
export function delPermission(id) {
  return request({
    url: '/doc/permission/' + id,
    method: 'delete'
  })
}