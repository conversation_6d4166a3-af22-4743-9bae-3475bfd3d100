import request from '@/utils/request'

// 查询规则分类列表
export function listPerCfgRuleType(query) {
  return request({
    url: '/hvpAn/perCfgRuleType/list',
    method: 'get',
    params: query
  })
}

// 查询规则分类详细
export function getPerCfgRuleType(id) {
  return request({
    url: '/hvpAn/perCfgRuleType/' + id,
    method: 'get'
  })
}

// 新增规则分类
export function addPerCfgRuleType(data) {
  return request({
    url: '/hvpAn/perCfgRuleType',
    method: 'post',
    data: data
  })
}

// 修改规则分类
export function updatePerCfgRuleType(data) {
  return request({
    url: '/hvpAn/perCfgRuleType',
    method: 'put',
    data: data
  })
}

// 删除规则分类
export function delPerCfgRuleType(id) {
  return request({
    url: '/hvpAn/perCfgRuleType/' + id,
    method: 'delete'
  })
}