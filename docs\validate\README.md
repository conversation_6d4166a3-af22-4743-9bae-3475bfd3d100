# 表单校验系统文档

## 文档概览

本目录包含了完整的表单校验系统技术文档，面向开发人员提供详细的实现说明、架构设计和开发指南。

## 文档结构

### 📋 [敏感词校验功能技术文档](./sensitive-words-validation.md)
- **目标读者**: 开发人员、技术负责人
- **内容概要**: 敏感词校验功能的详细技术实现
- **包含内容**:
  - 技术架构和实现细节
  - API接口说明
  - 配置管理方式
  - 已知问题和限制
  - 扩展建议和优化方案

### 🏗️ [表单校验架构文档](./validation-architecture.md)
- **目标读者**: 架构师、高级开发人员
- **内容概要**: 整体校验系统的架构设计
- **包含内容**:
  - 校验体系结构图
  - 执行流程说明
  - 字段配置系统
  - 性能优化策略
  - 扩展点设计
  - 监控和调试方案

### 👨‍💻 [开发指南](./developer-guide.md)
- **目标读者**: 前端开发人员
- **内容概要**: 实用的开发指南和最佳实践
- **包含内容**:
  - 快速开始教程
  - 常见开发场景
  - 调试技巧
  - 测试指南
  - 问题排查
  - 扩展开发

## 快速导航

### 🚀 我是新手开发者
1. 先阅读 [开发指南](./developer-guide.md) 的"快速开始"部分
2. 了解 [架构文档](./validation-architecture.md) 的"整体架构概览"
3. 参考 [敏感词文档](./sensitive-words-validation.md) 的"使用方法"

### 🔧 我要添加新功能
1. 查看 [架构文档](./validation-architecture.md) 的"扩展点设计"
2. 参考 [开发指南](./developer-guide.md) 的"常见开发场景"
3. 了解 [敏感词文档](./sensitive-words-validation.md) 的"扩展建议"

### 🐛 我遇到了问题
1. 查看 [开发指南](./developer-guide.md) 的"常见问题解决"
2. 使用 [开发指南](./developer-guide.md) 的"调试技巧"
3. 参考 [敏感词文档](./sensitive-words-validation.md) 的"已知问题与限制"

### 📊 我要优化性能
1. 阅读 [架构文档](./validation-architecture.md) 的"性能优化策略"
2. 查看 [敏感词文档](./sensitive-words-validation.md) 的"性能考虑"
3. 参考 [开发指南](./developer-guide.md) 的"最佳实践"

## 核心功能概述

### 🛡️ 敏感词校验
- **功能**: 检测用户输入中的敏感词汇
- **配置**: 通过系统参数 `SENSITIVE_WORDS` 配置
- **支持字段**: input、textarea 类型字段
- **特性**: 大小写不敏感、支持多敏感词检测

### ✅ 字段校验
- **必填校验**: 检查必填字段是否为空
- **格式校验**: 数字、邮箱、正则表达式等格式验证
- **条件校验**: 基于其他字段值的条件必填
- **自定义校验**: 支持 JS 表达式的自定义校验规则

### 🔗 业务校验
- **范围校验**: enodebId、cellTac 等业务字段的范围验证
- **重复校验**: 检查数据库中的重复值
- **关联校验**: 字段间的关联性验证

## 技术栈

- **前端框架**: Vue.js
- **HTTP 客户端**: Axios
- **工具库**: Lodash (部分功能)
- **测试框架**: Jest (推荐)

## 系统要求

- **Node.js**: >= 14.0.0
- **Vue.js**: >= 2.6.0
- **浏览器**: 支持 ES6+ 的现代浏览器

## 配置要求

### 系统参数配置
```javascript
// 必需的系统参数
SENSITIVE_WORDS = "敏感词1,敏感词2,敏感词3"
```

### 字段配置要求
```javascript
// 字段配置必需属性
{
  name: 'fieldName',      // 字段名 (必需)
  title: '字段显示名',     // 显示标题 (必需)
  formType: 'input',      // 表单类型 (推荐)
  formShow: true,         // 是否显示 (必需)
  formEdit: true,         // 是否可编辑 (必需)
  required: false         // 是否必填 (可选)
}
```

## 版本历史

### v1.1.0 (当前版本)
- ✨ 新增敏感词校验功能
- 🚀 优化字段类型过滤机制
- 📝 完善技术文档
- 🐛 修复已知问题

### v1.0.0 (基础版本)
- ✅ 基础字段校验功能
- 🔗 业务规则校验
- 📊 性能优化
- 🧪 测试覆盖

## 贡献指南

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue.js 官方风格指南
- 函数命名使用驼峰命名法
- 添加必要的注释和文档

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加新的校验规则"

# 问题修复
git commit -m "fix: 修复敏感词校验的性能问题"

# 文档更新
git commit -m "docs: 更新开发指南"

# 性能优化
git commit -m "perf: 优化字段遍历算法"
```

### 测试要求
- 新功能必须包含单元测试
- 测试覆盖率不低于 80%
- 包含边界情况测试
- 性能测试 (如适用)

## 支持与反馈

### 技术支持
- **内部文档**: 查看本目录下的详细文档
- **代码审查**: 提交 Pull Request 进行代码审查
- **技术讨论**: 在团队技术群中讨论

### 问题反馈
1. **Bug 报告**: 在项目管理系统中创建 Bug 单
2. **功能建议**: 在需求管理系统中提交功能需求
3. **文档问题**: 直接修改文档并提交 PR

### 联系方式
- **技术负责人**: [技术负责人姓名]
- **项目经理**: [项目经理姓名]
- **团队邮箱**: [团队邮箱地址]

## 许可证

本项目采用内部许可证，仅供公司内部使用。

---

## 更新日志

### 2025-06-12
- 📝 创建完整的技术文档体系
- 🛡️ 实现敏感词校验功能
- 🏗️ 优化整体架构设计
- 👨‍💻 提供详细的开发指南

---

**注意**: 本文档会随着功能的更新而持续维护，请定期查看最新版本。
