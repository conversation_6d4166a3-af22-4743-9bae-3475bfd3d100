<template>
  <div
    class="topology-node-menu"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @mousedown.stop
    @contextmenu.prevent
  >
    <ul>
      <li v-for="item in menu" :key="item.action" @click="handleAction(item.action)">
        <i :class="item.icon"></i> {{ item.label }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'NodeMenu',
  props: {
    position: { type: Object, required: true },
    target: { type: Object, default: null }
  },
  data() {
    return {
      menu: [
        { action: 'add-parent', label: '添加上级节点', icon: 'el-icon-top' },
        { action: 'add-sibling', label: '添加同级节点', icon: 'el-icon-more' },
        { action: 'add-child', label: '添加下级节点', icon: 'el-icon-bottom' },
        { action: 'edit', label: '编辑属性', icon: 'el-icon-edit' },
        { action: 'copy', label: '复制', icon: 'el-icon-document-copy' },
        { action: 'paste', label: '粘贴', icon: 'el-icon-document' },
        { action: 'delete', label: '删除', icon: 'el-icon-delete' }
      ]
    }
  },
  mounted() {
    document.addEventListener('mousedown', this.handleOutside)
  },
  beforeDestroy() {
    document.removeEventListener('mousedown', this.handleOutside)
  },
  methods: {
    handleAction(action) {
      this.$emit('action', action)
      this.$emit('close')
    },
    handleOutside(e) {
      // 点击菜单外部关闭菜单
      if (!this.$el.contains(e.target)) {
        this.$emit('close')
      }
    }
  }
}
</script>

<style scoped>
.topology-node-menu {
  position: absolute;
  z-index: 99;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  min-width: 140px;
  padding: 4px 0;
}
.topology-node-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.topology-node-menu li {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  transition: background 0.2s;
}
.topology-node-menu li:hover {
  background: #f5f7fa;
}
.topology-node-menu i {
  margin-right: 8px;
  font-size: 16px;
}
</style>
