import request from '@/utils/request'

// 查询业务字段配置列表
export function listPerCfgFeilds(query) {
  return request({
    url: '/hvpAn/perCfgFeilds/list',
    method: 'get',
    params: query
  })
}


export function getTableFields(query) {
  return request({
    url: '/hvpAn/perCfgFeilds/getTableFields',
    method: 'get',
    params: query
  })
}

// 查询业务字段配置详细
export function getPerCfgFeilds(schema) {
  return request({
    url: '/hvpAn/perCfgFeilds/' + schema,
    method: 'get'
  })
}

// 新增业务字段配置
export function addPerCfgFeilds(data) {
  return request({
    url: '/hvpAn/perCfgFeilds',
    method: 'post',
    data: data
  })
}

// 修改业务字段配置
export function updatePerCfgFeilds(data) {
  return request({
    url: '/hvpAn/perCfgFeilds',
    method: 'put',
    data: data
  })
}

// 删除业务字段配置
export function delPerCfgFeilds(schema) {
  return request({
    url: '/hvpAn/perCfgFeilds/' + schema,
    method: 'delete'
  })
}