<template>
  <div class="topology-editor-test">
    <h2>拓扑图编辑器测试页面</h2>
    <div class="editor-container">
      <TopologyEditor />
    </div>
  </div>
</template>

<script>
import TopologyEditor from '@/components/TopologyEditor/index.vue'

export default {
  name: 'TopologyEditorTest',
  components: { TopologyEditor }
}
</script>

<style scoped>
.topology-editor-test {
  width: 100vw;
  height: 100vh;
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
}
.editor-container {
  flex: 1;
  display: flex;
  min-height: 0;
  min-width: 0;
  background: #fff;
  margin: 0 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  overflow: hidden;
}
h2 {
  margin: 24px 0 0 24px;
  font-size: 20px;
  color: #333;
  font-weight: 700;
}
</style>
