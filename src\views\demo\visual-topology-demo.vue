<template>
  <div class="topology-demo-container">
    <div class="demo-header">
      <h2>可视化拓扑图组件示例</h2>
      <div class="demo-description">
        <p>这是一个基于G6的可视化拓扑图组件，支持以下功能：</p>
        <ul>
          <li>添加/删除节点</li>
          <li>双击节点创建连接</li>
          <li>拖拽节点调整位置</li>
          <li>编辑节点属性</li>
          <li>导入/导出拓扑图数据</li>
          <li>缩放和自动布局</li>
        </ul>
      </div>
    </div>
    
    <div class="topology-wrapper">
      <VisualTopology />
    </div>
  </div>
</template>

<script>
import VisualTopology from '@/components/VisualTopology'

export default {
  name: 'VisualTopologyDemo',
  components: {
    VisualTopology
  }
}
</script>

<style scoped>
.topology-demo-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-header h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.demo-description {
  color: #606266;
  line-height: 1.5;
}

.demo-description ul {
  padding-left: 20px;
}

.topology-wrapper {
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
  min-height: 600px;
}
</style>