import request from '@/utils/request'

// 查询cosmic功能列列表
export function listTCosmicFunction(query) {
  return request({
    url: '/cosmic/function/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic功能列详细
export function getTCosmicFunction(id) {
  return request({
    url: '/cosmic/function/' + id,
    method: 'get'
  })
}

// 新增cosmic功能列
export function addTCosmicFunction(data) {
  return request({
    url: '/cosmic/function',
    method: 'post',
    data: data
  })
}

// 修改cosmic功能列
export function updateTCosmicFunction(data) {
  return request({
    url: '/cosmic/function',
    method: 'put',
    data: data
  })
}

// 删除cosmic功能列
export function delTCosmicFunction(id) {
  return request({
    url: '/cosmic/function/' + id,
    method: 'delete'
  })
}