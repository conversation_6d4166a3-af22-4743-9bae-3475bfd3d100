import {useCommonQueryData} from '@/utils/useDictionary'
import {startFlow} from '@/api/site/open/dilatation'

import BasicForm from "@/components/BasicComponents/Form/index";
import Vue from "vue";

const config = {
  operat: ['showDetail', 'start'],
  formValue: {},
  operatConfig: {
    showDetail: {
      icon: 'el-icon-view',
      title: '详情',
      event: (row, vm) => vm.tableViews(row),
    },
  },
  process: [],
  async init() {
    const {chooseResource, resourceType} = this.$route.query
    this.resourceType = resourceType
    this.query.chooseResource = chooseResource
    this.query.nodeId = this.sysconfig.NewSiteEnodeBListIds
    this.query.status = 'ALL'
    this.process = await useCommonQueryData('WITHDRAW_PROCESS_TYPE')
    Vue.component("BasicForm", BasicForm);
  },
  tableViews(row) {
    const query = {
      type: 'view',
      sourceType: 'OPEN',
      businessType: 'withdraw',
      id: row?.id,
      status: this.query.status,
      orderNo: row?.order_no,
      nodeId: this.$route.query.nodeId,
      newSite: this.$route.query.newSite,
      nodeName: row?.node_name,
      taskId: row?.task_id,
      formInsId: row?.form_ins_id,
      siteIdCmcc: row?.site_id_cmcc,
      xqSiteIdCmcc: row?.xq_site_id_cmcc,
      tagsViewTitle: row?.site_id_cmcc,
      resourceType: row?.resource_type,
    }

    this.$router.push({
      path: `/open/offNet/handle/view`,
      query,
    })
  },
  beforeUpload() {
    const h = this.$createElement
    const fields = [
      {
        name: 'process',
        title: '申请流程',
        show: true,
        formType: 'radio',
        children: this.process,
      },
    ]
    let that = this;
    return new Promise((resolve, reject) => {
      this.$msgbox({
        title: '发起申请流程选项',
        message: h('BasicForm', {
          props: {
            size: 'small',
            labelWidth: '100px',
            fields,
            formValue: that.extendParam,
          },
          on: {
            returnFormValue: (value) => {
              this.$set(this.extendParam, "resourceType", this.resourceType ?? 'Open')
              this.$set(this.extendParam, "process", value.process)
            },
          },
        }),
        showCancelButton: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            if (!that.extendParam.process) {
              this.$message.error('请选择发起流程!')
              return;
            }
          }
          done()
        },
      }).then(action => {
        resolve(true);
      }).catch(e => {
        resolve(false);
      })
    })
  }
}
export default config
