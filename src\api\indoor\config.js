import request from '@/utils/request'

export function getDictData(dictCode) {
  return request({
    url: '/system/dict/data/list?dictTypeCode='+dictCode,
    method: 'get'
  })
}

//查询审核配置列表
export function userConfigList(queryData) {
  return request({
    url: '/indoor/userConfig/list',
    params:queryData
  })
}

//查询审核配置信息
export function getUserConfig(id) {
  return request({
    url: '/indoor/userConfig/detail/'+id,
    method:'get'
  })
}

// 添加审核配置
export function addUserConfig(data) {
  return request({
    url: '/indoor/userConfig/add',
    method:'post',
    data:data
  })
}

//复制
export function copyUserConfig(data) {
  return request({
    url: '/indoor/userConfig/copy',
    method:'post',
    data:data
  })
}

//删除审核配置
export function delUserConfig(id) {
  return request({
    url: '/indoor/userConfig/'+id,
    method:'delete',
  })
}

//修改审核配置
export function updateUserConfig(data) {
  return request({
    url: '/indoor/userConfig',
    method:'put',
    data:data
  })
}

//查询审核配置类型列表
export function listLevel(queryData) {
  return request({
    url: '/indoor/level/list',
    params:queryData
  })
}

//查询审核配置类型信息
export function getLevel(id) {
  return request({
    url: '/indoor/level/'+id,
    method:'get'
  })
}

// 添加审核配置类型
export function addLevel(data) {
  return request({
    url: '/indoor/level/add',
    method:'post',
    data:data
  })
}

//删除审核配置类型
export function delLevel(id) {
  return request({
    url: '/indoor/level/'+id,
    method:'delete',
  })
}

//修改审核配置类型
export function updateLevel(data) {
  return request({
    url: '/indoor/level',
    method:'put',
    data:data
  })
}

//查询审核规则列表
export function qryRuleList(queryData) {
  return request({
    url: '/indoor/config/list',
    params:queryData
  })
}

//查询审核规则信息
export function getRuleDetail(id) {
  return request({
    url: '/indoor/config/'+id,
    method:'get'
  })
}

// 添加审核规则
export function addRule(data) {
  return request({
    url: '/indoor/config/add',
    method:'post',
    data:data
  })
}

//删除审核规则
export function delRule(id) {
  return request({
    url: '/indoor/config/'+id,
    method:'delete',
  })
}

//修改审核规则
export function updateRule(data) {
  return request({
    url: '/indoor/config',
    method:'put',
    data:data
  })
}

//批量新增/修改审核规则
export function updateBatchRule(data) {
  return request({
    url: '/indoor/config/editBacth',
    method:'put',
    data:data
  })
}
