import request from '@/utils/request'

// 查询规则标签列表
export function listPerRuleRunTaskRule(query) {
  return request({
    url: '/an/perRuleRunTaskRule/list',
    method: 'get',
    params: query
  })
}

// 查询规则标签详细
export function getPerRuleRunTaskRule(sdate) {
  return request({
    url: '/an/perRuleRunTaskRule/' + sdate,
    method: 'get'
  })
}

// 新增规则标签
export function addPerRuleRunTaskRule(data) {
  return request({
    url: '/an/perRuleRunTaskRule',
    method: 'post',
    data: data
  })
}

// 修改规则标签
export function updatePerRuleRunTaskRule(data) {
  return request({
    url: '/an/perRuleRunTaskRule',
    method: 'put',
    data: data
  })
}

// 删除规则标签
export function delPerRuleRunTaskRule(sdate) {
  return request({
    url: '/an/perRuleRunTaskRule/' + sdate,
    method: 'delete'
  })
}