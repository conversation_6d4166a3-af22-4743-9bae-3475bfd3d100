import request from '@/utils/request'

// 查询分析-结果字段
export function fieldList(query) {
  return request({
    url: '/hvpAn/result/fieldList',
    method: 'get',
    params: query
  })
}

// 查询分析-结果列表
export function listResult() {
  return request({
    url: '/hvpAn/result/list',
    method: 'get',
    params: query
  })
}

// 查询分析-结果列表
export function qryPerResult(query) {
  return request({
    url: '/hvpAn/result/qryPerResult',
    method: 'post',
    data: query
  })
}

// 导出分析-结果列表
export function exportPreResult(data) {
  return request({
    url: '/hvpAn/result/exportCsv',
    method: 'post',
    data
  })
}

// 查询分析-结果详细
export function getResult(id) {
  return request({
    url: '/hvpAn/result/' + id,
    method: 'get'
  })
}

// 新增分析-结果
export function addResult(data) {
  return request({
    url: '/hvpAn/result',
    method: 'post',
    data: data
  })
}

// 修改分析-结果
export function updateResult(data) {
  return request({
    url: '/hvpAn/result',
    method: 'put',
    data: data
  })
}

// 删除分析-结果
export function delResult(id) {
  return request({
    url: '/hvpAn/result/' + id,
    method: 'delete'
  })
}