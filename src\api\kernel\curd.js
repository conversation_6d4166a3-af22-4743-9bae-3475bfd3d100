import request from '@/utils/request'

export function detail(code,id) {
  return request({
    url: '/kernel/curd/detail/' + code + '/' + id,
    method: 'get'
  })
}

export function add(code,data) {
  return request({
    url: '/kernel/curd/save/' + code,
    method: 'post',
    data: data
  })
}

export function update(code,data) {
  return request({
    url: '/kernel/curd/update/' + code,
    method: 'put',
    data: data
  })
}

export function del(code,id) {
  return request({
    url: '/kernel/curd/del/' + code,
    method: 'delete',
    data: id
  })
}

export function delAll(code, data) {
  return request({
    url: '/kernel/curd/delAll/' + code,
    method: 'post',
    data: data
  })
}