import request from '@/utils/request'

// 查询报表列表
export function listReport(query) {
  return request({
    url: '/kernel/report/list',
    method: 'get',
    params: query
  })
}


// 查询报表详细
export function getReport(id) {
  return request({
    url: '/kernel/report/' + id,
    method: 'get'
  })
}

// 查询报表详细
export function getReportByCode(code) {
  return request({
    url: '/kernel/report/detailByCode/' + code,
    method: 'get'
  })
}

// 新增报表
export function addReport(data) {
  return request({
    url: '/kernel/report',
    method: 'post',
    data: data
  })
}

// 修改报表
export function updateReport(data) {
  return request({
    url: '/kernel/report',
    method: 'put',
    data: data
  })
}

// 删除报表
export function delReport(id) {
  return request({
    url: '/kernel/report/' + id,
    method: 'delete'
  })
}

export function bigScreenSave(data){
  return request({
    url: '/kernel/report/bigScreen/save',
    method: 'post',
    data: data
  })
}

export function bigScreenDetail(data){
  return request({
    url: '/kernel/report/bigScreen/detail/' + data,
    method: 'get',
  })
}

export function queryAllDataSet(){
  return request({
    url: '/kernel/report/bigScreen/dataSet',
    method: 'get',
  })
}

// 查询组件列表
export function listReportComponent(query) {
  return request({
    url: '/kernel/report/component/list',
    method: 'get',
    params: query
  })
}

export function listAllComponent() {
  return request({
    url: '/kernel/report/component/listAll',
    method: 'get'
  })
}

export function listCompoentChartType() {
  return request({
    url: '/kernel/report/component/list/chartType',
    method: 'get'
  })
}

// 查询组件详细
export function getReportComponent(id) {
  return request({
    url: '/kernel/report/component/' + id,
    method: 'get'
  })
}

// 新增组件
export function addReportComponent(data) {
  return request({
    url: '/kernel/report/component',
    method: 'post',
    data: data
  })
}

// 修改组件
export function updateReportComponent(data) {
  return request({
    url: '/kernel/report/component',
    method: 'put',
    data: data
  })
}

// 删除组件
export function delReportComponent(id) {
  return request({
    url: '/kernel/report/component/' + id,
    method: 'delete'
  })
}

export function changeStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/kernel/report/component/changeStatus',
    method: 'put',
    data: data
  })
}