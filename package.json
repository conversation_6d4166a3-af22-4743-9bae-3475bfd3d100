{"name": "cmcc", "version": "3.0.0", "description": "覆盖端到端分析与支撑模块", "author": "xhxk", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "preview": "node build/index.js --preview", "report": "vue-cli-service build --report", "lint": "eslint --ext .js,.vue src", "postinstall": "patch-package", "dll": "webpack --config ./webpack.dll.conf.js"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "notes": ["ol-layerswitcher 3.8.1"], "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g2": "^4.1.19", "@antv/g6": "^4.7.3", "@riophae/vue-treeselect": "0.4.0", "@vue/composition-api": "^1.4.4", "axios": "0.21.0", "cesium": "1.80.0", "cesium-navigation-es6": "3.0.5", "clipboard": "2.0.6", "cron-parser": "^4.8.1", "crypto-js": "^4.2.0", "d3": "^5.14.2", "dagre-d3": "git+https://gitee.com/zh888/self-dagre-d3.git", "date-fns": "^4.1.0", "dayjs": "^1.10.7", "diff": "^7.0.0", "diff2html": "^3.4.48", "echarts": "5.1.1", "echarts-gl": "^2.0.9", "element-ui": "2.15.6", "event-source-polyfill": "^1.0.31", "fabric": "^5.2.1", "file-saver": "2.0.4", "fuse.js": "6.4.3", "generate-schema": "^2.6.0", "highlight.js": "9.18.5", "image-conversion": "^2.1.1", "intersection-observer": "^0.12.0", "jointjs": "2.1.3", "jquery": "^3.6.0", "jr-qrcode": "^1.1.4", "js-base64": "^3.6.1", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "lodash": "^4.17.21", "mammoth": "^1.8.0", "monaco-editor": "0.27.0", "nprogress": "0.2.0", "ol": "^6.3.1", "ol-wind": "^1.0.0-alpha.5", "path-browserify": "^1.0.1", "quill": "^2.0.2", "quill-image-resize-module": "^3.0.0", "screenfull": "^6.0.1", "shapefile": "^0.6.6", "simplex-noise": "^2.4.0", "sm-crypto": "^0.3.13", "sockjs-client": "^1.6.1", "sortablejs": "1.10.2", "sql-formatter": "4.0.2", "terraformer-wkt-parser": "^1.2.1", "typescript": "^5.6.3", "umy-ui": "^1.1.7", "util": "^0.12.4", "v-wave": "^1.5.0", "vcrontab": "^0.3.5", "vue": "2.6.14", "vue-codemirror": "4.0.6", "vue-contextmenujs": "1.3.13", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-fabric": "^0.1.42", "vue-router": "3.4.9", "vue-ruler-tool": "^1.2.4", "vue-superslide": "^0.1.1", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xmldom": "^0.6.0", "xterm": "^5.1.0", "xterm-addon-attach": "^0.6.0", "xterm-addon-fit": "^0.7.0", "yarn": "^1.22.22", "@turf/helpers": "^7.2.0", "@turf/turf": "^6.5.0"}, "devDependencies": {"@vue/cli-plugin-babel": "5.0.1", "@vue/cli-plugin-eslint": "5.0.1", "@vue/cli-service": "^5.0.2", "babel-eslint": "10.1.0", "chalk": "4.1.0", "compression-webpack-plugin": "^3.0.0", "connect": "3.6.6", "copy-webpack-plugin": "^10.2.4", "core-js": "^3.20.0", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "monaco-editor-webpack-plugin": "5.0.0", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^6.0.1", "patch-package": "^6.4.7", "prettier": "^2.7.1", "runjs": "4.4.2", "sass-loader": "^10.1.0", "speed-measure-webpack-plugin": "^1.5.0", "strip-pragma-loader": "^1.0.0", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^5.3.1", "vue-template-compiler": "2.6.14", "worker-loader": "2.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "chrome 68", "last 2 versions"]}