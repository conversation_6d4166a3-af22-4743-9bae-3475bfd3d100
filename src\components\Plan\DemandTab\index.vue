<template>
	<el-tabs v-model="currentTab" class="basic-tabs" type="border-card">
		<el-tab-pane class="basic" label="基站需求" name="site">
			<div>
				<BasicForm
					ref="form"
					:disabled="!inResourcePool || (inResourcePool && type === 'view')"
					:fields="useFields('XQJD_site')"
					:formValue="planSite && planSite.xqSite"
					:groupColumn="3"
					:useFieldDefaultValue="true"
					class="amend-from"
					size="small"
          groupMark="siteNeed"
					@fieldCascading="handleCascading"
					@returnFormValue="setPlanSite" />

				<div v-if="inResourcePool && type !== 'view'" class="form-button">
					<el-button
						:loading="analysisLoading"
						type="danger"
						@click="associationAnalysis">
						关联分析
					</el-button>
					<el-button :loading="submitLoading" type="primary" @click="submit">
						提交需求
					</el-button>
					<el-button type="danger" @click="fullScreenMap">放大地图</el-button>
				</div>
        <el-dialog
          :close-on-click-modal='false'
          :close-on-press-escape='false'
          :visible.sync='dealTask'
          :destroy-on-close="isCurrentProvince('BJ')"
          title='提交需求'
          width='70%'
        >
          <TaskDeal
            :dealTask.sync='dealTask'
            dealType='xqsubmit'
            :siteIdCmcc='planSite.xqSite ?planSite.xqSite.siteIdCmcc:""'
            @handleTask='handleTask'
            ref='taskDeal'
          />
        </el-dialog>
			</div>
			<component
				:is="dynamicMapComponent"
				id="basic-layer"
				ref="map"
				:preventInteraction="!inResourcePool"
				@callBackAreaType="callBackAreaType"
				queryLayerDetail
				@callBackDetail="mapCallBack" />
		</el-tab-pane>
		<el-tab-pane
			v-if="(getRpList && getRpList.length) || inResourcePool"
			label="需求价值"
			name="value">
			<el-button type="primary" @click="smartAnalyse">
				需求价值智能分析
			</el-button>
			<FormArea
				:formColumn="4"
				:formEditable="inResourcePool"
				:formFields="useFields('site_require_problem')"
				:namePropData="getNamePropData('site_require_problem', 'type')"
				:showAddFormButton="false"
				:showSwiper="false"
				:showUploader="false"
				:tabEditable="inResourcePool"
				:tabList="getRpList"
				nameProp="type"
				tabTitle="需求价值"
        groupMark="needWorth"
      />
		</el-tab-pane>
	</el-tabs>
</template>

<script>
import {
	computed,
	defineComponent,
	inject,
	onMounted,
	reactive,
	ref,
	toRefs,
	watch,
} from '@vue/composition-api'
import { controlFormFieldsChildren } from '@/utils/plan/handle'
import { useNamePropData } from '@/utils/plan/useBusiness'
import { commonQuery } from '@/api/kernel/query'
import { useGetters } from '@/utils/useMappers'
import { debounce } from '@/utils'
import { calForecastReq } from '@/api/cov_an/preSmartDataImport'
import store from '@/store'
import { isCurrentProvince } from '@/utils/common'

export default defineComponent({
  methods: { isCurrentProvince },
	props: {
		type: String,
		fields: Object,
		planSite: Object,
		inResourcePool: Boolean,
	},
	setup(props, { emit, root }) {
		const { fields, planSite, inResourcePool } = toRefs(props)
		const { $set, $message } = root

		const form = ref(null)
		const map = ref(null)
    let dealTask = ref(false);
		const smartAnalyse = () => {
			const { latitude, longitude, siteIdCmcc } = planSite.value.xqSite
			if (!longitude || !latitude) {
				$message.info('请先填写需求经纬度！')
				return
			}
			const param = {
				lon: longitude,
				lat: latitude,
				orderNo: siteIdCmcc,
			}
			calForecastReq(param).then((res) => {
				console.log(res)
			})
		}
		const state = reactive({
			currentTab: 'site',
			dynamicMapComponent: 'OpenLayer',
			submitLoading: false,
			analysisLoading: false,
		})

		const getRpList = computed(
			() => planSite.value.siteList && planSite.value.siteList[0]?.rpList,
		)

		const siteCmccId = inject('siteCmccId');
    store.state.plan.siteIdCmcc = siteCmccId.value
		const sysconfig = useGetters('sysconfig')()
		onMounted(() => {
			isCurrentProvince('ZJ') && (state.dynamicMapComponent = 'MapZJ');
      // isCurrentProvince('YN') && (state.dynamicMapComponent = 'MapZJ')//todo 开放测试
		})

		const stop = watch(
			() => planSite.value?.xqSite,
			(value, prev) => {
				if (
					(value.hasOwnProperty('longitude') && value.longitude) ||
					(value.hasOwnProperty('latitude') && value.latitude)
				) {
					useMapQueryAroundSite()
					stop()
				}
			},
		)

		const mapCallBack = (data) => {
      // console.log('当前阶段值：',store.state.plan);
      //只有需求库可以点击更改表单
      if(store.state.plan.currentNodeId === 'jt3b453adde06a4772b465c4d6fb1ccc4e'){
        form.value.setFormValue(data);
      }
      form.value.setFormValue(data)
      // console.log('callBackDetail方法调父方法,data:',data);
    }
		const callBackAreaType = async (param) => {
			const belongToAreaType = await commonQuery('qryAreaType', param)
			if (belongToAreaType?.data?.length) {
				const data = belongToAreaType.data[0]
				const detail = {}
				detail.sceneJt = data.code

				detail.townJt = data.town_name
				detail.areaJt = data.area_jt
				detail.cityJt = data.city_jt
				mapCallBack(detail)
			}
		}
		const fullScreenMap = () => {
      map.value.setMapToFullScreen()
      console.log('全屏方法被执行,',map.value.setMapToFullScreen());
    }
		const associationAnalysis = async () => {
			const { latitude, longitude } = planSite.value.xqSite
			if (!longitude || !latitude) {
				$message.info('请先填写需求经纬度！')
				return
			}
			state.analysisLoading = true

			const result = await commonQuery('qryAroundSiteRequireProblem', {
				lon: longitude,
				lat: latitude,
				dist: sysconfig.DEMAND_VALUE_DIST || 500,
				siteIdCmcc: siteCmccId.value,
			})

			state.analysisLoading = false
			if (result?.data?.length) {
				const rpListIds = getRpList.value.map((i) => i.pr_corp_id)
				const rows = result.data.filter(
					(i) => !rpListIds.includes(i.pr_corp_id),
				)

				if (rows.length) {
					planSite.value.siteList[0].rpList = getRpList.value
						.filter((i) => i.type)
						.concat(rows)

					planSite.value.siteList[0].rpList.forEach(
						(i, index) => (i.sort = index),
					)
					$message.success('关联分析结果已添加至需求价值!')
				} else {
					$message.warning('关联分析数据已存在!')
				}
			} else {
				$message.info('暂无数据!')
			}
		}
		const submit = () => {
      if(isCurrentProvince('BJ')) {
        dealTask.value = true;
      } else {
        emit('submit')
      }
    }

    const handleTask = (params) => {
      emit('submit', params);
    }
		const useFields = (code) => fields.value && fields.value[code]
		const getNamePropData = (code, name) => useNamePropData(useFields(code), name)

		const handleCascading = (field, value, autoTrigger,groupMark) => {
      // console.log('DemandTabIndex-handleCascading,字段名:',field.name,',value:',value,field);
			if (inResourcePool.value && field.name === 'indoorFlag') {
				controlFormFieldsChildren(value,useFields('XQJD_site').find((field) => field.name === 'configure'))
				// 程序设置值时，需要触发级联但不清空子字段（因为这是程序逻辑，不是用户操作）
				!autoTrigger && form.value.setFormValue({ configure: value === 'MacroSite' ? 'S1' : 'O1'}, true)
			}
		}
		const setPlanSite = (data) => {
      // console.log('DemandTabIndex-data:',data,',inResourcePool:',inResourcePool,',xqSite:',planSite.value?.xqSite);
			if (inResourcePool.value && planSite.value?.xqSite && Object.keys(data).length) {
				Object.keys(data).forEach((key) => {
					$set(planSite.value.xqSite, key, data[key])
				})

				// 反向关联地图中心经纬度 查询周边规划和入网站点
				const { longitude, latitude } = planSite.value.xqSite
				if (
					(data.hasOwnProperty('longitude') && longitude) ||
					(data.hasOwnProperty('latitude') && latitude)
				) {
					useMapQueryAroundSite()
				}
				emit('setPlanSite', data)
			}
		}

		const useMapQueryAroundSite = debounce(function () {
			const { longitude, latitude } = planSite.value.xqSite
			map.value.setMapCenter(+longitude, +latitude)
			requestXqSiteAroundGH()
			requestXqSiteAroundRW()
			requestXqSiteAreaType()
		}, 300)

		// 查询需求周边规划
		const requestXqSiteAroundGH = async () => {
			const {
				longitude,
				latitude,
				networkStandard,
				indoorFlag,
				frequencyBand,
			} = planSite.value.xqSite

			const data = await commonQuery('qryGHSite', {
				lon: longitude,
				lat: latitude,
			})

			if (data?.data?.length) {
				data.data.forEach((item) => {
					const points = []
					const text = item.site_name_cmcc + '\n(' + item.dist + '米)'
					let icon = require('@/assets/layer_images/open-sm.png')
					if (
						networkStandard == item.network_standard &&
						indoorFlag == item.indoor_flag &&
						frequencyBand == item.frequency_band
					) {
						icon = require('@/assets/layer_images/open-sm-select.png')
					}

					map.value.renderMark(item.longitude, item.latitude, icon, item.dist, {
						name: text,
					})
					points.push([Number(longitude), Number(latitude)])
					points.push([Number(item.longitude), Number(item.latitude)])
					let color = [17, 94, 211, 0.9]
					if ((item.hasOwnProperty('color') && item.color) || item.dist < 200) {
						color = [255, 0, 0, 0.9]
					}
					map.value.renderLine(points, color)
				})
			}
		}

		const requestXqSiteAreaType = async () => {
			const { longitude, latitude } = planSite.value.xqSite
      mapCallBack({town: planSite.value.xqSite.town})
      await callBackAreaType({longitude, latitude})
		}
		// 查询需求周边入网
		const requestXqSiteAroundRW = async () => {
			const {
				longitude,
				latitude,
				networkStandard,
				indoorFlag,
				frequencyBand,
			} = planSite.value.xqSite

			const data = await commonQuery('qryRwjdSite', {
				lon: longitude,
				lat: latitude,
				dist: 1000,
			})

			if (data?.data?.length) {
				data.data.forEach((item) => {
					const points = []
					const text = item.site_name_cmcc + '\n(' + item.dist + '米)'
					let icon = require('@/assets/layer_images/ghjd-sm.png')
					if (
						networkStandard == item.network_standard &&
						indoorFlag == item.indoor_flag &&
						frequencyBand == item.frequency_band
					) {
						icon = require('@/assets/layer_images/ghjd-sm-select.png')
					}

					map.value.renderMark(item.longitude, item.latitude, icon, item.dist, {
						name: text,
					})
					points.push([Number(longitude), Number(latitude)])
					points.push([Number(item.longitude), Number(item.latitude)])
					let color = [17, 94, 211, 0.9]
					if ((item.hasOwnProperty('color') && item.color) || item.dist < 200) {
						color = [255, 0, 0, 0.9]
					}
					map.value.renderLine(points, color)
				})
			}
		}
		return {
			...toRefs(state),
			form,
			map,
			getRpList,
      dealTask,
			useFields,
			getNamePropData,
			submit,
      handleTask,
			mapCallBack,
			callBackAreaType,
			fullScreenMap,
			associationAnalysis,
			handleCascading,
			setPlanSite,
			smartAnalyse,
		}
	},
	components: {
		OpenLayer: () => import('@/components/OpenLayers'),
		MapZJ: () => import('@/components/MapZJ.vue'),
		BasicForm: () => import('@/components/BasicComponents/Form'),
		FormArea: () => import('@/components/Open/Components/FormArea'),
    TaskDeal: () => import('@/components/Flow/TaskDeal'),
  },
})
</script>

<style lang="scss" scoped>
.basic-tabs {
	margin: 15px 15px 0;
	border-top: 0;
	border: 1px solid #e6ebf5 !important;
	::v-deep {
		.el-tabs__header {
			border-top: 0 !important;
		}
	}
	.basic {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: row;
		padding: 10px;

		> div {
			align-self: flex-start;

			&:first-child {
				flex: 3;
			}

			&:last-child {
				flex: 2;
			}
		}
		~ div {
			padding: 10px;
		}

		// 独占一行的表单项
		::v-deep {
			.form-item__address,
			.form-item__powerTransformation,
			.form-item__siteNameCmcc {
				width: calc(100% - 10px) !important;
			}

			.form-button {
				text-align: center;

				button {
					margin-top: 12px;
				}
			}

			.map {
				height: 480px;
				#map {
					height: 100%;
				}
			}
		}
	}
}
</style>

