import * as Cesium from "cesium"
import { parse } from 'terraformer-wkt-parser';
import {center, area, point, union, buffer, polygonToLine, booleanPointInPolygon, lineString} from '@turf/turf';
import {
  ArcType,
  Cartesian3,
  CircleGeometry, PerInstanceColorAppearance,
  PolygonGeometry,
  PolygonHierarchy,
  PolylineColorAppearance,
  PolylineGeometry
} from 'cesium'

export const unionPolygon = (wkt1, wkt2) => {
  if (!wkt1) return wkt2
  let geo1 = parse( wkt1 )
  if (geo1.type === 'Polygon') {
    geo1 = {
      type: "MultiPolygon",
      coordinates: [geo1.coordinates]
    };
  } else if (geo1.type === 'GeometryCollection') {
    geo1 = geo1.geometries.filter(g => g.type.includes('Polygon'))
      .reduce((a, b) => union(a, b))
  }
  const geo2 = parse( wkt2 )
  const merged = buffer(union(geo1, geo2), 0)
  return merged.geometry.type === 'MultiPolygon' ? `MULTIPOLYGON (${merged.geometry.coordinates.map(coordinate =>
    `(${coordinate.map(c => `(${c.map(i => i.join(' ')).join()})`).reduce((a,b) => `${a}, ${b}`)})`
  ).join()})` : `POLYGON(${merged.geometry.coordinates.map(coordinate =>
    `(${coordinate.map(c => c.join(' ')).reduce((a,b) => `${a}, ${b}`)})`
  ).join()})`
}

export const unionPolyline = (wkt1, wkt2) => {
  if (!wkt1) return wkt2
  let geo1 = parse( wkt1 )
  let coordinates = []
  if (geo1.type === 'LineString') {
    coordinates.push(geo1.coordinates)
  } else if (geo1.type === 'MultiLineString') {
    coordinates = [...coordinates, ...geo1.coordinates]
  }
  let geo2 = parse( wkt2 )
  if (geo2.type === 'LineString') {
    coordinates.push(geo2.coordinates)
  } else if (geo2.type === 'MultiLineString') {
    coordinates = [...coordinates, ...geo2.coordinates]
  }
  return `MULTILINESTRING (${coordinates.map((c) => `(${c.map(o => o[0] + ' ' + o[1]).join()})`)
    .reduce((a, b) => `${a}, ${b}`)})`
}

export const unionPoint = (wkt1, wkt2) => {
  if (!wkt1) return wkt2
  let geo1 = parse( wkt1 )
  let coordinates = []
  if (geo1.type === 'Point') {
    coordinates.push(geo1.coordinates)
  } else if (geo1.type === 'MultiPoint') {
    coordinates = [...coordinates, ...geo1.coordinates]
  }
  let geo2 = parse( wkt2 )
  if (geo2.type === 'Point') {
    coordinates.push(geo2.coordinates)
  } else if (geo2.type === 'MultiPoint') {
    coordinates = [...coordinates, ...geo2.coordinates]
  }
  debugger
  return `MULTIPOINT (${coordinates.map((c) => `(${c.map(o => o[0] + ' ' + o[1]).join()})`)
    .reduce((a, b) => `${a}, ${b}`)})`
}

export const polylineSplit = wkt => {
  if (!wkt) return []
  let origin = parse(wkt)
  if (origin.type === 'MultiLineString') {
    return origin.coordinates.map(coordinates => ({type: 'LineString', coordinates}))
  } else {
    return [origin]
  }
}

export const polylineBuffer = (wkt, bufferWidth = 200) => {
  if (!wkt) return []
  const split = polylineSplit(wkt)
  const format = (geom) => {
    const bufferGeometry = buffer(geom, bufferWidth / 2, {
      units: 'meters',
      steps: 8,
    })

    // 提取缓冲后的几何体
    const geometry = bufferGeometry.geometry;
    return geometry.coordinates
  }

  return split.map(geom => {
    const geometries = format(geom)
    return {
      positions: geometries.shift(),
      holes: geometries
    }
  })
}

export const geomFromText = wkt => {
  if (!wkt) return ''
  let geom = parse(wkt)
  let coordinate = geom.coordinates
  if (geom.type.startsWith('Multi')) coordinate = coordinate[0]
  let points = coordinate.shift() || []
  let lons = points.map(p => p[0]).sort()
  let lats = points.map(p => p[1]).sort()
  return `("${wkt}", ${lons.shift()}, ${lats.shift()}, ${lons.pop()}, ${lats.pop()}, 4326)`
}

export const getCenterPosition = viewer => {
  let centerResult = viewer.camera.pickEllipsoid(
    new Cesium.Cartesian2(
      viewer.canvas.clientWidth / 2,
      viewer.canvas.clientHeight / 2
    )
  );
  let curPosition = Cesium.Ellipsoid.WGS84.cartesianToCartographic(centerResult)
  let curLon = (curPosition.longitude * 180) / Math.PI
  let curLat = (curPosition.latitude * 180) / Math.PI
  return {lon: curLon, lat: curLat}
}

// 当前屏幕经纬度
export const getPositions = viewer => {
  let params = {};
  let extend = viewer.camera.computeViewRectangle();
  if (typeof extend === "undefined") {
    //2D下会可能拾取不到坐标，extend返回undefined,所以做以下转换
    let canvas = viewer.scene.canvas;
    let upperLeft = new Cesium.Cartesian2(0, 0);//canvas左上角坐标转2d坐标
    let lowerRight = new Cesium.Cartesian2(
      canvas.clientWidth,
      canvas.clientHeight
    );//canvas右下角坐标转2d坐标

    let ellipsoid = viewer.scene.globe.ellipsoid;
    let upperLeft3 = viewer.camera.pickEllipsoid(
      upperLeft,
      ellipsoid
    );//2D转3D世界坐标

    let lowerRight3 = viewer.camera.pickEllipsoid(
      lowerRight,
      ellipsoid
    );//2D转3D世界坐标

    let upperLeftCartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(
      upperLeft3
    );//3D世界坐标转弧度
    let lowerRightCartographic= viewer.scene.globe.ellipsoid.cartesianToCartographic(
      lowerRight3
    );//3D世界坐标转弧度

    let minx = Cesium.Math.toDegrees(upperLeftCartographic.longitude);//弧度转经纬度
    let maxx = Cesium.Math.toDegrees(lowerRightCartographic.longitude);//弧度转经纬度

    let miny = Cesium.Math.toDegrees(lowerRightCartographic.latitude);//弧度转经纬度
    let maxy = Cesium.Math.toDegrees(upperLeftCartographic.latitude);//弧度转经纬度

    params.westProjected = minx;
    params.eastProjected = maxx;
    params.southProjected = miny;
    params.northProjected = maxy;
  } else {
    //3D获取方式
    params.eastProjected = Cesium.Math.toDegrees(extend.east);
    params.northProjected = Cesium.Math.toDegrees(extend.north);

    params.westProjected = Cesium.Math.toDegrees(extend.west);
    params.southProjected = Cesium.Math.toDegrees(extend.south);
  }
  return params;//返回屏幕所在经纬度范围
}

// 栅格当前屏幕
export const getGridScenePositions = (viewer, grain = 10) => {
  let step = grain * grain
  let positions = getPositions(viewer)
  // 向外取整
  let east =  Math.ceil(positions.eastProjected * step)
  let west =  Math.floor(positions.westProjected * step)
  let north =  Math.ceil(positions.northProjected * step)
  let south =  Math.floor(positions.southProjected * step)

  let x = east - west
  let y = north - south

  let scenePositions = []

  for (let i = 0; i < x; i++) {
    for (let j = 0; j < y; j++) {
      scenePositions.push(
        `${(west + i) / step},${(south + j) / step},${(west + i + 1) / step},${(south + j + 1) / step}`
      )
    }
  }
  // 中间优先加载
  let newPositions = []
  for(let i = 0; i< x * y ;i ++) {
    newPositions.push(scenePositions.splice(scenePositions.length/2, 1)[0])
  }
  return newPositions.reverse()
}

export const groupBy = (list = [], fn) => {
  const groups = {};
  list.forEach(function (o) {
    const group = `${fn(o)}`;
    groups[group] = groups[group] || [];
    groups[group].push(o);
  });
  return groups
}

export const getColor = (style = [], kpi) => {
  return style.filter(o => {
    if (o.hasOwnProperty('expressions')) {
      return eval(o.expressions.map( express => {
        if (express.operator) return `${isNaN(kpi) ? `'${kpi}'` : kpi}${express.operator}${isNaN(express.value) ? `'${express.value}'` : express.value }`
        if (express.logic) return express.logic
      }).join(''))
    }
    if (!o.min) return +kpi <= o.max
    if (!o.max) return +kpi > o.min
    return o.min < +kpi && +kpi <= o.max
  }).map(o => Cesium.Color.fromCssColorString(o.color))[0] ?? Cesium.Color.PURPLE.withAlpha(0.6)
}


export const createPolygonGeometry = (coordinates, holeCoordinates = [], height, extrudedHeight, interval = 0) => {
  if (!coordinates || !coordinates.length) return null;
  let positions = Cesium.Cartesian3.fromDegreesArray(coordinates.reduce((a, b) => [...a, ...b], []))
  let holes = holeCoordinates.map(hole => new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(hole.reduce((a, b) => [...a, ...b], []))))

  return Cesium.PolygonGeometry.createGeometry(new Cesium.PolygonGeometry({
    polygonHierarchy: new Cesium.PolygonHierarchy(
      positions, holes
    ),
    height: height + interval,
    extrudedHeight: extrudedHeight - interval,
    vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT
  }))
}

export const createPrimitive = (instances, line = false) => {
  return new Cesium.Primitive({
    releaseGeometryInstances: false,
    geometryInstances: instances,
    appearance: line ? new Cesium.PolylineColorAppearance() :
      new Cesium.PerInstanceColorAppearance({ // 为每个instance着色
        flat: false,
        translucent: false,
        closed: true,
        renderState: {
          depthMask: false,
          depthTest: { enabled: true }, //深度测试
          blending: Cesium.BlendingState.ALPHA_BLEND
        }
      }),
    compressVertices: true,
    asynchronous: false,  // 确定基元是异步创建还是阻塞直到准备就绪
  })
}

export const guid = () => {
  function S4() {
    return (((1+Math.random())*0x10000)|0).toString(16).substring(1);
  }
  return (S4()+S4()+"-"+S4()+"-"+S4()+"-"+S4()+"-"+S4()+S4()+S4());
}


export const drawShape = (positionData, color = Cesium.Color.PURPLE) => {
  return {
    polyline: {
      positions: positionData,
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      // clampToGround: true,
      width: 5,
      material: color
    }
  }
}

export const drawLabel = (midpoint, distance) => {
  return {
    position: midpoint,
    label: {
      // This callback updates the length to print each frame.
      text: distance,
      font: '20px sans-serif',
      outlineWidth: 2,
      showBackground: true,
      verticalOrigin: Cesium.VerticalOrigin.TOP,
      pixelOffset: new Cesium.Cartesian2(15, 0), //此属性为设置偏移量
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      eyeOffset: new Cesium.Cartesian3(0, 0, -300)
    }
  }
}

export const drawRect = (positionData, height= 0) => {
  return {
    rectangle: {
      coordinates: positionData,
      height,
      material: Cesium.Color.PURPLE.withAlpha(0.5),
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  }
}

export const drawPoint = (positionData) => {
  return {
    position: positionData,
    point: {
      color: Cesium.Color.PURPLE,
      pixelSize: 6, // 基础大小
      scaleByDistance: new Cesium.NearFarScalar(
        1000, 2.0, // 当距离 <=1000 米时，放大到 2 倍（即 10*2=20px）
        10000, 1.5  // 当距离 >=10000 米时，缩小到 0.5 倍（即 10*0.5=5px）
      ),
    }

  }
}

export const drawCircle = (positionData, radius = 5, height= 0) => {
  return {
    position: positionData,
    ellipse: {
      semiMinorAxis: radius,
      semiMajorAxis: radius,
      height,
      material: Cesium.Color.PURPLE.withAlpha(0.5),
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  }
}

export const drawPolygon = (positionData, color = Cesium.Color.PURPLE.withAlpha(0.5), height = 0.01) => {
  return {
    polygon: {
      hierarchy: positionData,
      height,
      material: color,
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  }
}

export const getRectanglePosition = (firstPoint, secondPoint) => {
  let ellipsoid = window[this.gisname].viewer.scene.globe.ellipsoid
  let cartographic = ellipsoid.cartesianToCartographic(firstPoint)
  let firstLat = Cesium.Math.toDegrees(cartographic.latitude)
  let firstLng = Cesium.Math.toDegrees(cartographic.longitude)
  cartographic = ellipsoid.cartesianToCartographic(secondPoint)
  let secondLat = Cesium.Math.toDegrees(cartographic.latitude)
  let secondLng = Cesium.Math.toDegrees(cartographic.longitude)

  return Cesium.Rectangle.fromDegrees(
    firstLng < secondLng ? firstLng : secondLng,
    firstLat < secondLat ? firstLat : secondLat,
    firstLng < secondLng ? secondLng : firstLng,
    firstLat < secondLat ? secondLat : firstLat
  )
}

export const zoomRectangle = (rectangle, scaleFactor) => {
  // 计算当前矩形的中心和范围
  const west = rectangle.west;
  const east = rectangle.east;
  const south = rectangle.south;
  const north = rectangle.north;

  const lonCenter = (west + east) / 2;
  const latCenter = (south + north) / 2;
  const lonRange = (east - west) * scaleFactor;
  const latRange = (north - south) * scaleFactor;

  // 计算新的边界
  const newWest = lonCenter - lonRange / 2;
  const newEast = lonCenter + lonRange / 2;
  const newSouth = latCenter - latRange / 2;
  const newNorth = latCenter + latRange / 2;

  // 返回新矩形（确保边界合法）
  return new Cesium.Rectangle(
    Math.max(newWest, -Math.PI),  // 经度最小 -180°
    Math.max(newSouth, -Cesium.Math.PI_OVER_TWO), // 纬度最小 -90°
    Math.min(newEast, Math.PI),   // 经度最大 180°
    Math.min(newNorth, Cesium.Math.PI_OVER_TWO)  // 纬度最大 90°
  );
}

export const getDistance = (left, right) => {
  return Cesium.Cartesian3.distance(left, right)
}

export const getMidpoint = (left, right) => {
  return Cesium.Cartesian3.fromElements(
    (left.x + right.x) / 2,
    (left.y + right.y) / 2,
    (left.z + right.z) / 2
  )
}

export const handleDownload = (url, name) => {
  getBlob(url).then(blob => {
    saveAs(blob, name);
  })
  return false;
};
const getBlob = (url) => {
  return new Promise(resolve => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response);
      }
    };
    xhr.send();
  })
};

const saveAs = (blob, name) => {
  let url = window.URL.createObjectURL(blob)
  let link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', name)
  document.body.appendChild(link)
  link.click()
}

export const treeToOptions = (tree = []) => {
  return tree.map(o => {
    return o.children && o.children.length > 0 ? {
      label: o.name,
      code: o.code,
      options: treeToOptions(o.children)
    } : {
      label: o.name,
      code: o.code,
      value: o.dictId
    }
  })
}

export const isEqual = (obj1, obj2) => {
  for (let key in obj1) {
    if (obj1.hasOwnProperty(key)) {
      if (!obj2.hasOwnProperty(key)) return false;
      if (typeof obj1[key] === 'object') {
        if (!isEqual(obj1[key], obj2[key])) return false;
      } else if (obj1[key] !== obj2[key]) {
        return false;
      }
    }
  }
  for (let key in obj2) {
    if (obj2.hasOwnProperty(key) && !obj1.hasOwnProperty(key)) return false;
  }
  return true;
}

// 树形结构转化
export const flatArrayToTree = list => {
  if (Array.isArray(list)) {
    // parentId不存在为顶级菜单
    const parents = list.filter(i => !i.parentId)
    const childs = list.filter(i => i.parentId)

    const translator = (data, index) => {
      const children = []
      let childIndex = 0
      childs.forEach(i => {
        if (i.parentId === data.dictId) {
          const currentIndex = index + '-' + childIndex
          addProperty(i, currentIndex)
          i.children = translator(i, currentIndex)
          children.push(i)
          childIndex++
        }
      })
      return children.sort((a, b) => a.sort - b.sort)
    }

    const addProperty = (data, index) => {
      data.label = data.name
      data.key = index
      data.id = data.dictId
    }

    parents.forEach((data, index) => {
      addProperty(data, index)
      data.children = translator(data, index)
    })

    return parents.sort((a, b) => a.sort - b.sort)
  }
}

// 表单子选项数据格式化
export const formatFormChildrenData = data => {
  if (data && Array.isArray(data)) {
    data.forEach(item => {
      item.value = item.code
    })
  }
}


export const getScenePositions = (positions, x, y) => {
  let scenePositions = []
  // 九宫格
  let x_step = (positions.eastProjected - positions.westProjected) / parseFloat(x + '');
  let y_step = (positions.northProjected - positions.southProjected) / parseFloat(y + '');

  for (let i = 0; i < x; i++) {
    for (let j = 0; j < y; j++) {
      scenePositions.push({
        westProjected: positions.westProjected + x_step * i, southProjected: positions.southProjected + y_step * j,
        eastProjected: positions.westProjected + x_step * (i + 1), northProjected: positions.southProjected + y_step * (j + 1)
      })
    }
  }
  return scenePositions;//返回屏幕所在经纬度范围
}

export const formatCustomLegend = (style) => {
  return style.map(o =>
    `<div class="sld-style">
       <i style="background-color: ${o.color}" class="sld-style-item"></i>
       <span>${ format(o) }</span>
     </div>`).join('\n')
}

const format = (o) => {
  if (o.hasOwnProperty('expressions')) {
    return o.expressions.map( express => {
      if (express.operator) return `${express.operator.trim() === '==' ? '' : express.operator}${express.value}`
      if (express.logic) return express.logic.trim() === '&&' ? ' 且 ' : ' 或 '
    }).join('')
  }
  // 旧样式
  else {
    return !o.min ? `< ${o.max}` : (!o.max ? `> ${o.min}` : `${o.min} ~ ${o.max}`)
  }
}

export const formatGeom = geom => {
  if (!geom) return ''
  const wktMatch = geom.match(/"([^"]+)"/);

  if (wktMatch) {
    return wktMatch[1]; // 获取匹配组中的WKT
  } else {
    return ''
  }
}

export const formatSldLegend = (style, vista) => {
  return style.map(o => {
    if (o.kpiType.startsWith('KPI_')) {
      !!parseFloat(o.valueOne) && (o.valueOne = parseFloat(o.valueOne).toFixed(2))
      !!parseFloat(o.valueTwo) && (o.valueTwo = parseFloat(o.valueTwo).toFixed(2))
      return `<div class="sld-style">
        <i style="background-color: ${o.styleColor}" class="sld-style-item"></i>
        <span>${!o.valueOne ? `< ${o.valueTwo}` : (!o.valueTwo ? `> ${o.valueOne}` : `${o.valueOne} ~ ${o.valueTwo}`)}</span>
      </div>`
    } else {
      return `<div class="sld-style" style="margin-bottom: 13px">
        <img class="sld-style-item" src='${vista && o.styleImageVista ? o.styleImageVista : o.styleImage}' style="width: 25px;height: 25px;margin-bottom: -5px; margin-right: 5px;"/> <span>${o.ruleName}</span>
      </div>`
    }
  }).join('\n')
}

export const formatSldStyle = (style, vista) => {
  return {
    kpi: style[0] ? style[0].kpiName: '',
    style: style.map(o => {
      if (o.kpiType.startsWith('KPI_')) {
        return {
          min: o.valueOne,
          max: o.valueTwo,
          color: o.styleColor
        }
      } else {
        return {
          id: o.id,
          name: o.ruleName,
          key: o.valueOne,
          value: vista && o.styleImageVista ? `${o.styleImageVista}` : `${o.styleImage}`,
          kpiName: o.kpiName,
          styleImage: `${o.styleImage}`,
          styleImageVista: `${o.styleImageVista}`
        }
      }
    }),
  }
}

export const listToTree = (list = []) => {
  let clone = JSON.parse(JSON.stringify(list))
  return clone.filter(father => {
    let branch = clone.filter( child => father['dictId'] == child['parentId'])
    ;branch.length > 0 && (father['children'] = branch)
    // 基础图层，专题分析
    return father['parentId'] === 0
  })
}

export function getElementLeft(element){
  var actualLeft = element.offsetLeft;
  var current = element.offsetParent;
  while (current !== null){
    actualLeft += current.offsetLeft;
    current = current.offsetParent;
  }
  return actualLeft;
}

export function getElementTop(element){
  var actualTop = element.offsetTop;
  var current = element.offsetParent;
  while (current !== null){
    actualTop += current. offsetTop;
    current = current.offsetParent;
  }
  return actualTop;
}

// 计算多边形的中心经纬度, 面积
export function calculatePolygon(wkt) {
  let polygon = parse(wkt);
// 计算中心点
  const polygonCenter = center(polygon);
  const points = polygonCenter.geometry.coordinates
  let polygonArea = 0
  try {
    polygonArea = area(buffer(polygon, 0))
  } catch (e) {
    console.log(e)
  }
  return { lon: points[0], lat: points[1], area: polygonArea };
}

export function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

export function getRandomRgbaColor(alpha) {
  const r = Math.floor(Math.random() * 256);  // 随机生成 0-255 之间的值
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  const a = alpha !== undefined && !isNaN(alpha) ? alpha : Math.random()
  return `rgba(${r},${g},${b},${a})`;
}

export function formatGeometry(geom, properties) {
  const height = 0.1
  const {width = 5} = properties
  switch (geom.type) {
    case 'Point':
      return [Cartesian3.fromDegrees(geom.coordinates[0], geom.coordinates[1], height + 3)]
    case 'MultiPoint':
      return geom.coordinates.map(coordinate => Cartesian3.fromDegrees(coordinate[0], coordinate[1], height + 3))
    case 'LineString':
      return [new PolylineGeometry({
        positions : Cartesian3.fromDegreesArrayHeights(geom.coordinates.reduce((a, b) => [...a, ...b, height], [])),
        width,
        arcType: ArcType.NONE,
        vertexFormat : PolylineColorAppearance.VERTEX_FORMAT
      })]
    case 'MultiLineString':
      return geom.coordinates.map(coordinate =>
        new PolylineGeometry({
          positions: Cartesian3.fromDegreesArrayHeights(coordinate.reduce((a, b) => [...a, ...b, height], [])),
          width,
          arcType: ArcType.NONE,
          vertexFormat: PolylineColorAppearance.VERTEX_FORMAT
        }))
    case 'Polygon':
      return geom.coordinates.map(coordinate => {
        let positions = Cartesian3.fromDegreesArray(coordinate.reduce((a, b) => [...a, ...b], []))
        return PolygonGeometry.createGeometry(
          new PolygonGeometry({
            polygonHierarchy: new PolygonHierarchy(
              positions
            ),
            height: height,
            vertexFormat: PerInstanceColorAppearance.VERTEX_FORMAT
          }))
      })
    case 'MultiPolygon':
      return geom.coordinates.map(coordinate => {
        let positions = Cartesian3.fromDegreesArray(coordinate.shift().reduce((a, b) => [...a, ...b], []))
        let holes = coordinate ? coordinate.map(hole =>
          new PolygonHierarchy(Cartesian3.fromDegreesArray(hole.reduce((a, b) => [...a, ...b], [])))
        ) : []
        return PolygonGeometry.createGeometry(
          new PolygonGeometry({
            polygonHierarchy: new PolygonHierarchy(
              positions, holes
            ),
            height: height,
            vertexFormat: PerInstanceColorAppearance.VERTEX_FORMAT
          }))
      })
    case 'GeometryCollection':
      return geom.geometries.map(g => formatGeometry(g, Object.assign({}, properties, {type: g.type})))
        .filter(o => o).reduce((a, b) => [...a, ...b], [])
    default:
      console.error('Unsupported WKT type: ' + geom.type);
      return [];
  }
}
