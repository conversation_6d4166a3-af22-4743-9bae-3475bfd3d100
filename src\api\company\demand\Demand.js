import request from '@/utils/request'

// 查询02 需求数据列表
export function listDemand(query) {
  return request({
    url: '/doc/demand/list',
    method: 'get',
    params: query
  })
}

// 查询02 需求数据详细
export function getDemand(id) {
  return request({
    url: '/doc/demand/' + id,
    method: 'get'
  })
}

// 新增02 需求数据
export function addDemand(data,type) {
  return request({
    url: '/doc/demand/save/'+type,
    method: 'post',
    data: data
  })
}

// 修改02 需求数据
export function updateDemand(data,type,nextFlow) {
  return request({
    url: '/doc/demand/update/'+type+'/'+nextFlow,
    method: 'put',
    data: data
  })
}

// 删除02 需求数据
export function delDemand(id) {
  return request({
    url: '/doc/demand/' + id,
    method: 'delete'
  })
}

//批量提交任务
export function batchFlowTask(data) {
  return request({
    url: '/doc/demand/batchFlowTask',
    method: 'post',
    data: data
  })
}

export function addBatchDemand(data,type) {
  return request({
    url: '/doc/demand/saveBatch/'+type,
    method: 'post',
    data: data
  })
}

export function importDemandByExcel(query) {
  return request({
    url: '/doc/demand/importDemandByExcel',
    method: 'get',
    params: query
  })
}
