<!--新站和增删调都共用这个组件，需要添加流程标识-->
<template>
  <el-collapse v-model='boardCollapse'>
    <el-collapse-item name='board' title='板卡'>
      <FormArea
        :badge='!!type'
        :fileStructurePath='getFileStructurePath("board")'
        :formColumn='boardColumn'
        :formEditable='formEditable'
        :formFields='fields[fieldSubType]'
        :groupMark='fieldSubType'
        :namePropData='getNamePropData(fieldSubType, "boardType")'
        :showChangeFormToolTip='!!type'
        :tabEditable='tabEditable'
        :tabList='boardList'
        :virtualDelete='!!type'
        @addDefaultTab='$emit("addDefaultTab")'
        @addTab='addTab($event, "board")'
        @selectChanged="selectChanged"
        @removeTab='removeTab'
        @switchTab='switchTab($event, "board")'
        nameProp='boardType'
        tabTitle='板卡'
        :tab-title-name-prefix="tabTitleNamePrefix"
        :firstTabRemovable='firstTabRemovable'
      />
      <!--端口-->
      <Port
        :proc-def-key="procDefKey"
        :fields='fields'
        :fieldSubType='fieldSubType.replace("board", "port")'
        :fileBaseStructurePath='getFileStructurePath("board")'
        :formEditable='formEditable'
        :parentResourceCode='getResourceCodePath("board")'
        :portList='getPortList'
        :scheme='scheme'
        :tabEditable='tabEditable'
        :type='type'
        @addDefaultPort='addDefaultPort'
        :tab-title-name-prefix="`${tabTitleNamePrefix}-板卡${parseInt(boardIndex) + 1}-`"
        v-if='boardList && boardList.length && fields[fieldSubType.replace("board", "port")]'
        ref="port"
      />
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import FormArea from './FormArea'
import BasicForm from '@/components/BasicComponents/Form'
import { deepClone } from '@/utils'
import { portList } from '@/utils/plan/static'

import {
  useFormPlanId,
  useExpendAddTab,
  useFormType,
  useEditTabClosed,
  useRecursionCallBack, useEditTabBadgeVisible
} from '@/utils/plan/useBusiness'
import EditTab from '@/components/BasicComponents/EditTab/index.vue'

export default {
	name: 'board',
	props: {
    procDefKey :String,
    tabTitleNamePrefix: {
      type:String,
      default: '',
    },
		type: String,
		tabEditable: Boolean,
		formEditable: Boolean,
		fields: Object,
		scheme: Object,
		boardList: Array,
		boardColumn: {
			type: Number,
			default: 2,
		},
		fieldSubType: {
			type: String,
			default: 'board',
		},
		parentResourceCode: String,
		fileBaseStructurePath: String,
		firstTabRemovable: Boolean,
	},
	data() {
		return {
			boardIndex: '0',
			portIndex: '0',
			boardCollapse: ['board'],
			portTypeAcitve: 'Phy',
		}
	},
	computed: {
		getPortList() {
			return this.boardList && this.boardList[this.boardIndex] && this.boardList[this.boardIndex].portList
		},
		getCurrentPort() {
			return this.getPortList && this.getPortList[this.portIndex]
		},
		getBoardTypeData() {
			return (
				this.fields &&
				this.fields[this.fieldSubType]?.find((i) => i.name === 'boardType')
					?.children
			)
		},
	},
	methods: {
    selectChanged(data) {
      this.$emit('selectChanged', data, 'board')
    },
		addTab(tab, code) {
			useFormPlanId(code, tab, this.boardList)
			tab.resourceCode = this.getResourceCodePath(code, tab)

			// 生成port.resourceCode
			tab.portList?.forEach((port) => {
				port.resourceCode = tab.resourceCode + 'P' + (+port.sort + 1)
			})

			this.type && (this.procDefKey !== 'cmcc_5g_plan')&&useExpendAddTab(tab)
		},
		addDefaultPort() {
			this.$set(
				this.boardList[this.boardIndex],
				'portList',
				deepClone(portList())
			)
		},
		switchTab(index, mark) {
			this[mark + 'Index'] = index
      this.$refs.port.$refs.portForm.tabIndex = '0'
		},
		removeTab(index, tab) {
			if (this.type) {
				useRecursionCallBack(tab, (obj) => {
					useFormType(obj, 'delete');
					useEditTabClosed(obj, true);
					this.$set(obj, 'modifyType', 'deletions');
				})

				if (tab.modifyType || this.type === 'reduceCapacity') {
					this.$set(tab, 'modifyType', 'deletions') //后端识别标识
				}
        this.boardList?.splice(index, 1);
			}
		},
		getNamePropData(code, name) {
			const currentFields = this.fields[code]
			if (currentFields) {
				const field = currentFields.find((i) => i.name === name)
				return field?.children
			}
		},

		getFileStructurePath(code) {
			// 工单id/阶段/方案_index/标识_index
			let currentCode = ''
			code.split('_').forEach((key, index, arr) => {
				currentCode += key + '_' + (+this[key + 'Index'] + 1)
				if (index < arr.length - 1) {
					currentCode += '/'
				}
			})

			return `${this.fileBaseStructurePath + '/' + currentCode}`
		},
		getResourceCodePath(code, tab) {
			let currentCode = ''
			code.split('_').forEach((key, index, arr) => {
				currentCode += key.charAt(0).toUpperCase()

				let endCode = +this[key + 'Index'] + 1
				if (index === arr.length - 1 && tab) {
					endCode = +tab.sort + 1
				}
				currentCode += endCode
			})
			return this.parentResourceCode + currentCode
		},
	},
	components: {
    EditTab,
		FormArea,
		BasicForm,
		Port: () => import('./Port'),
	},
}
</script>

<style lang='scss' scoped>
@import '@/assets/styles/plan/common.scss';
.port-form {
	padding-top: 10px;
	::v-deep .el-form-item__content {
		width: calc(100% - 150px) !important;
	}
}
</style>
