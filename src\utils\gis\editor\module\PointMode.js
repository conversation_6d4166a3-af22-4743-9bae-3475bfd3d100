import {
  BasicMode,
  PrimitiveType,
} from './BasicMode'
import { ActiveState } from '@/utils/gis/editor'
import * as Cesium from 'cesium'
import {drawPolygon, polylineBuffer, unionPoint} from '@/utils/gis/tools/common'

export class PointMode extends BasicMode {
  constructor(editor) {
    super(editor);
  }

  clear() {
    super.clear()
  }

  onLeftClick(event) {
    super.onLeftClick(event)

    if (this.editor.actived === ActiveState.ADD) {
      let cartographic = this._getCartesian3(event.position)
      if (cartographic) {
        this.entityPoints.push(cartographic)
        this.editor.actived = ActiveState.EDIT
        this.isEditing = true
        super.onLeftUp(event)
      }
    }
  }

  onLeftDown(event) {
    super.onLeftDown(event)

    if (this.editor.actived === ActiveState.EDIT) {
      let primary = this.editor.viewer.scene.pick(event.position)
      // 拾取到对象 判断拾取到的对象类型
      if (!primary || !primary.primitive || !primary.primitive.type) {
        this.editor.actived = ActiveState.ADD
        return
      }
      if (primary.primitive?.type === PrimitiveType.Entity) {
        this.isEditing = true
      }
    }
  }

  onLeftUp(event) {
    super.onLeftUp(event)
  }

  onLeftDoubleClick(event) {
    super.onLeftDoubleClick(event)
  }

  onRightClick(event) {
    if (this.editor.actived === ActiveState.EDIT) {
      let pickFeatures = this.editor.viewer.scene.drillPick(event.position)
      // 拾取到对象 判断拾取到的对象类型
      let primary = pickFeatures.find(p => p.id && p.id.type)
      if (
        primary
      ) {
        if (primary.id.type === PrimitiveType.Entity) {
          // 右键菜单
          this.editor.menu.curr.items = [{
            label: '删除',
            divided: true,
            title: true,
            customClass: 'right-menu-title',
            onClick: () => this._delete(primary.id)
          }]
          this.editor.menu.onContextmenu(event)
        }
      }
    }
    super.onRightClick(event)
  }

  onMouseMove(event) {
    super.onMouseMove(event)

    let position = this._getCartesian3(event.endPosition)
    if (!position) return
    if (this.editor.actived === ActiveState.ADD) {
      this.entityPoints[this.entityPoints.length - 1] = position
    } else if (this.editor.actived === ActiveState.EDIT) {
      // 修改样式
      // 获取鼠标位置
      let pickedObject = this.editor.viewer.scene.pick(event.endPosition)
      if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive)) {
        if (
          pickedObject.primitive.type === PrimitiveType.Vertex ||
          pickedObject.primitive.type === PrimitiveType.CenterPoint
        )
          // 当鼠标悬浮在 Cesium Entity 上时，设置样式
          this.editor.viewer.container.style.cursor = 'move'
        else if (pickedObject.primitive.type === PrimitiveType.Midpoint)
          this.editor.viewer.container.style.cursor = 'pointer'
        //
        else this.editor.viewer.container.style.cursor = 'crosshair'
      } else {
        // 当鼠标不在 Cesium Entity 上时，恢复默认样式
        this.editor.viewer.container.style.cursor = ''
      }

      if (!this.isEditing || !this.editVertext) return
      if (this.editVertext.type === PrimitiveType.CenterPoint) {
        let startPosition =
          this.vertexCenterPositions[this.editVertext.vertexIndex]
        if (!startPosition) return
        this._moveEntityByOffset(startPosition, position)
        this.vertexCenterPositions[this.editVertext.vertexIndex] =
          this._getCenterPosition(
            this.editEntity.datasources[this.editVertext.vertexIndex].positions
          )
      } else {
        const index = [...this.editVertext.vertexIndex]
        if (this.editVertext.isHole) {
          this.editEntity.datasources[index.shift()].holes[index.shift()][
            index.shift()
          ] = position
        } else {
          this.editEntity.datasources[index.shift()].positions[index.shift()] =
            position
        }
      }
      this.isEditing = true
    }
  }

  _union(wkt) {
    return unionPoint(this.editor.origin, wkt)
  }

  _delete(entity) {
    if (entity.currPoint)
      this.entityPoints = this.entityPoints.filter(p => !p.equals(entity.currPoint))
    super._delete(entity)
  }

  _formatCoordinate(points = []) {
    const cartes = this.ellipsoid.cartesianArrayToCartographicArray(points)
    return cartes.map(
      (point) =>
        `(${Cesium.Math.toDegrees(point.longitude)} ${Cesium.Math.toDegrees(
          point.latitude
        )})`
    )
  }

  _formatWkt() {
    if (!this.entityPoints.length) {
      return ''
    }
    // 过滤相同的点
    const shapes = this._formatCoordinate(this.entityPoints)
    return `MULTIPOINT(${shapes.join()})`
  }
}
