import request from '@/utils/request'

export function qryConfigs(){
    return request({
        url: '/an/perCfgRuleType/list',
        method: 'GET'
    })
}

export function qryTypes(id){
    return request({
        url: '/an/perCfgRuleType/qryTypes/' + id,
        method: 'GET'
    })
}

export function configSave(data){
    return request({
        url: '/an/perCfgRuleType/save',
        method: 'POST',
        data: data
    })
}


export function configRemove(id){
    return request({
        url: '/an/perCfgRuleType/removeOne/' + id,
        method: 'GET'
    })
}
