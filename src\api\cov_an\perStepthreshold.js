import request from '@/utils/request'

// 查询分析-步骤-子步骤-门限值列表
export function listThreshold(query) {
  return request({
    url: '/an/subStep/threshold/list',
    method: 'get',
    params: query
  })
}

// 查询分析-步骤-子步骤-门限值详细
export function getThreshold(id) {
  return request({
    url: '/an/subStep/threshold/' + id,
    method: 'get'
  })
}

// 新增分析-步骤-子步骤-门限值
export function addThreshold(data) {
  return request({
    url: '/an/subStep/threshold',
    method: 'post',
    data: data
  })
}

// 修改分析-步骤-子步骤-门限值
export function updateThreshold(data) {
  return request({
    url: '/an/subStep/threshold',
    method: 'put',
    data: data
  })
}

// 删除分析-步骤-子步骤-门限值
export function delThreshold(id) {
  return request({
    url: '/an/subStep/threshold/' + id,
    method: 'delete'
  })
}

export function getThresholdBySubAndStep(id) {
  return request({
    url: '/an/subStep/threshold/getByStepIdAndSubStepId/' + id,
    method: 'post'
  })
}



