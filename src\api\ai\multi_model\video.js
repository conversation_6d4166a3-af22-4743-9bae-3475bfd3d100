import request from '@/utils/request'

// 查询AI视频采集列表
export function listVideo(query) {
  return request({
    url: '/ai/multi_model/video/list',
    method: 'get',
    params: query
  })
}

// 查询AI视频采集详细
export function getVideo(id) {
  return request({
    url: '/ai/multi_model/video/' + id,
    method: 'get'
  })
}

// 新增AI视频采集
export function addVideo(data) {
  return request({
    url: '/ai/multi_model/video',
    method: 'post',
    data: data
  })
}

// 修改AI视频采集
export function updateVideo(data) {
  return request({
    url: '/ai/multi_model/video',
    method: 'put',
    data: data
  })
}

// 删除AI视频采集
export function delVideo(id) {
  return request({
    url: '/ai/multi_model/video/' + id,
    method: 'delete'
  })
}