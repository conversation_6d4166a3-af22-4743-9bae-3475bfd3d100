<template>
  <div class="topology-editor">
    <!-- 工具栏区域 -->
    <Toolbar
      :can-undo="canUndo"
      :can-redo="canRedo"
      @undo="handleUndo"
      @redo="handleRedo"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @auto-layout="handleAutoLayout"
      @import-json="handleImportJson"
      @export-json="handleExportJson"
      @add-node="handleAddNode"
      @delete-selected="handleDeleteSelected"
    />

    <div class="topology-main">
      <!-- 画布区域 -->
      <Canvas
        ref="canvas"
        :nodes="nodes"
        :edges="edges"
        :selected="selected"
        @select="handleSelect"
        @update:nodes="updateNodes"
        @update:edges="updateEdges"
        @contextmenu="handleContextMenu"
      />
      <!-- 属性面板 -->
      <PropertyPanel
        :selected="selected"
        @update="handlePropertyUpdate"
      />
    </div>
    <!-- 右键菜单 -->
    <NodeMenu
      v-if="contextMenu.visible"
      :position="contextMenu.position"
      :target="contextMenu.target"
      @action="handleMenuAction"
      @close="contextMenu.visible = false"
    />
  </div>
</template>

<script>
import Toolbar from './Toolbar.vue'
import Canvas from './Canvas.vue'
import PropertyPanel from './PropertyPanel.vue'
import NodeMenu from './NodeMenu.vue'

// 生成唯一ID
function uuid() {
  return 'node-' + Math.random().toString(36).substr(2, 9)
}

export default {
  name: 'TopologyEditor',
  components: {
    Toolbar,
    Canvas,
    PropertyPanel,
    NodeMenu
  },
  data() {
    return {
      // mock 数据
      nodes: [
        { id: 'node1', type: 'default', title: '节点1', description: '描述1', x: 100, y: 200 },
        { id: 'node2', type: 'default', title: '节点2', description: '描述2', x: 300, y: 200 }
      ],
      edges: [
        { id: 'edge1', source: 'node1', target: 'node2', type: 'default', label: '连接' }
      ],
      selected: null, // 当前选中节点或连线
      canUndo: false,
      canRedo: false,
      contextMenu: {
        visible: false,
        position: { x: 0, y: 0 },
        target: null
      },
      history: [], // 操作历史
      historyIndex: -1,
      clipboard: null // 用于复制粘贴
    }
  },
  methods: {
    // 工具栏操作
    handleUndo() {
      if (this.historyIndex > 0) {
        this.historyIndex--
        this.restoreHistory()
      }
    },
    handleRedo() {
      if (this.historyIndex < this.history.length - 1) {
        this.historyIndex++
        this.restoreHistory()
      }
    },
    handleZoomIn() {
      this.$refs.canvas.graph && this.$refs.canvas.graph.zoom(1.2)
    },
    handleZoomOut() {
      this.$refs.canvas.graph && this.$refs.canvas.graph.zoom(0.8)
    },
    handleAutoLayout() {
      // 简单自动布局：横向排列
      let x = 100
      this.nodes.forEach((n, i) => {
        n.x = x + i * 200
        n.y = 200
      })
      this.saveHistory()
    },
    handleImportJson() {
      // 模拟导入
      const json = prompt('粘贴导入的JSON数据:')
      if (json) {
        try {
          const data = JSON.parse(json)
          this.nodes = data.nodes || []
          this.edges = data.edges || []
          this.saveHistory()
        } catch (e) {
          this.$message.error('JSON格式错误')
        }
      }
    },
    handleExportJson() {
      // 导出为JSON
      const data = JSON.stringify({ nodes: this.nodes, edges: this.edges }, null, 2)
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'topology.json'
      a.click()
      URL.revokeObjectURL(url)
    },
    handleAddNode() {
      // 添加新节点
      const newId = uuid()
      this.nodes.push({
        id: newId,
        type: 'default',
        title: '新节点',
        description: '',
        x: 200 + Math.random() * 200,
        y: 200 + Math.random() * 100
      })
      this.saveHistory()
    },
    handleDeleteSelected() {
      if (!this.selected) return
      if (this.selected.source && this.selected.target) {
        // 删除连线
        this.edges = this.edges.filter(e => e.id !== this.selected.id)
      } else {
        // 删除节点及相关连线
        this.edges = this.edges.filter(e => e.source !== this.selected.id && e.target !== this.selected.id)
        this.nodes = this.nodes.filter(n => n.id !== this.selected.id)
      }
      this.selected = null
      this.saveHistory()
    },
    // 画布事件
    handleSelect(target) {
      this.selected = target
    },
    updateNodes(nodes) {
      this.nodes = nodes
    },
    updateEdges(edges) {
      this.edges = edges
    },
    handleContextMenu({ position, target }) {
      this.contextMenu = {
        visible: true,
        position,
        target
      }
    },
    // 属性面板事件
    handlePropertyUpdate(newProps) {
      if (!this.selected) return
      if (this.selected.source && this.selected.target) {
        // 连线属性
        const edge = this.edges.find(e => e.id === this.selected.id)
        if (edge) Object.assign(edge, newProps)
      } else {
        // 节点属性
        const node = this.nodes.find(n => n.id === this.selected.id)
        if (node) Object.assign(node, newProps)
      }
      this.saveHistory()
    },
    // 右键菜单操作
    handleMenuAction(action) {
      const target = this.contextMenu.target
      if (!target) return
      if (action === 'add-parent') {
        // 添加上级节点（简单实现：新节点指向当前节点）
        const newId = uuid()
        this.nodes.push({ id: newId, type: 'default', title: '上级节点', x: target.x, y: target.y - 100 })
        this.edges.push({ id: uuid(), source: newId, target: target.id, type: 'default', label: '' })
      } else if (action === 'add-sibling') {
        // 添加同级节点（同一父节点指向新节点，或新节点无连线）
        const newId = uuid()
        this.nodes.push({ id: newId, type: 'default', title: '同级节点', x: target.x + 100, y: target.y })
      } else if (action === 'add-child') {
        // 添加下级节点（当前节点指向新节点）
        const newId = uuid()
        this.nodes.push({ id: newId, type: 'default', title: '下级节点', x: target.x, y: target.y + 100 })
        this.edges.push({ id: uuid(), source: target.id, target: newId, type: 'default', label: '' })
      } else if (action === 'edit') {
        // 触发属性面板编辑
        this.selected = target
      } else if (action === 'copy') {
        this.clipboard = JSON.parse(JSON.stringify(target))
      } else if (action === 'paste') {
        if (this.clipboard) {
          const newId = uuid()
          const copy = { ...this.clipboard, id: newId, x: (target.x || 200) + 40, y: (target.y || 200) + 40 }
          this.nodes.push(copy)
        }
      } else if (action === 'delete') {
        // 删除节点或连线
        if (target.source && target.target) {
          this.edges = this.edges.filter(e => e.id !== target.id)
        } else {
          this.edges = this.edges.filter(e => e.source !== target.id && e.target !== target.id)
          this.nodes = this.nodes.filter(n => n.id !== target.id)
        }
        this.selected = null
      }
      this.contextMenu.visible = false
      this.saveHistory()
    },
    // 操作历史管理
    saveHistory() {
      // 截断未来历史
      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1)
      }
      this.history.push({
        nodes: JSON.parse(JSON.stringify(this.nodes)),
        edges: JSON.parse(JSON.stringify(this.edges))
      })
      this.historyIndex = this.history.length - 1
      this.updateUndoRedo()
    },
    restoreHistory() {
      const h = this.history[this.historyIndex]
      if (h) {
        this.nodes = JSON.parse(JSON.stringify(h.nodes))
        this.edges = JSON.parse(JSON.stringify(h.edges))
      }
      this.updateUndoRedo()
    },
    updateUndoRedo() {
      this.canUndo = this.historyIndex > 0
      this.canRedo = this.historyIndex < this.history.length - 1
    }
  },
  mounted() {
    // 初始化历史
    this.saveHistory()
  }
}
</script>

<style scoped>
.topology-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f7f8fa;
}
.topology-main {
  display: flex;
  flex: 1;
  min-height: 0;
}
</style>
