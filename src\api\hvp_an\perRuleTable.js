import request from '@/utils/request'

// 查询规则选择数据源列表
export function listPerRuleTable(query) {
  return request({
    url: '/hvpAn/perRuleTable/list',
    method: 'get',
    params: query
  })
}

// 查询规则选择数据源详细
export function getPerRuleTable(id) {
  return request({
    url: '/hvpAn/perRuleTable/' + id,
    method: 'get'
  })
}

// 新增规则选择数据源
export function addPerRuleTable(data) {
  return request({
    url: '/hvpAn/perRuleTable',
    method: 'post',
    data: data
  })
}

// 修改规则选择数据源
export function updatePerRuleTable(data) {
  return request({
    url: '/hvpAn/perRuleTable',
    method: 'put',
    data: data
  })
}

// 删除规则选择数据源
export function delPerRuleTable(id) {
  return request({
    url: '/hvpAn/perRuleTable/' + id,
    method: 'delete'
  })
}