import request from '@/utils/request'

// 查询cosmic数据组列表
export function listTCosmicDataField(query) {
  return request({
    url: '/cosmic/dataField/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic数据组详细
export function getTCosmicDataField(id) {
  return request({
    url: '/cosmic/dataField/' + id,
    method: 'get'
  })
}

// 新增cosmic数据组
export function addTCosmicDataField(data) {
  return request({
    url: '/cosmic/dataField',
    method: 'post',
    data: data
  })
}

// 修改cosmic数据组
export function updateTCosmicDataField(data) {
  return request({
    url: '/cosmic/dataField',
    method: 'put',
    data: data
  })
}

// 删除cosmic数据组
export function delTCosmicDataField(id) {
  return request({
    url: '/cosmic/dataField/' + id,
    method: 'delete'
  })
}