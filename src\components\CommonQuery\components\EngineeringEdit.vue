<template>
  <div style="margin-left: 10px">
    <el-dialog
      :title="title"
      :visible.sync="open"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      width="700px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="80px"
      >
        <el-form-item label="工程名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item label="创建人" prop="creatUser">
          <el-input v-model="form.createUser" disabled type="text" />
        </el-form-item>
        <el-form-item v-if="false" label="省份" prop="province">
          <el-select
            v-model="form.province"
            ref="province"
            :disabled="!!sysconfig.CURRENT_PROVINCE_CODE"
            filterable
            clearable
            @change="handleProvinceChange"
          >
            <el-option
              v-for="province in provinceData"
              :label="province.name"
              :value="province.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地市" prop="city">
          <el-select
            v-model="form.city"
            filterable
            clearable
            multiple
            @change="handleCityChange"
          >
            <el-option
              v-for="city in cityData"
              :label="city.name"
              :value="city.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区县" v-if="false" prop="county">
          <el-select
            v-model="form.county"
            filterable
            clearable
            @change="handleCountyChange"
          >
            <el-option
              v-for="county in countyData"
              :label="county.name"
              :value="county.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="datasource">
          <el-cascader
            style="width: 100%"
            popper-class="data-manager-cascader"
            v-model="form.datasource"
            :options="options"
            :props="props"
            :key="key"
            v-loading="dataLoading"
            clearable
          >
            <template slot-scope="{ node, data }">
              <el-checkbox
                v-if="node.isLeaf"
                :disabled="data.disabled"
                v-model="node.checked"
                @change="handleDataManagerChange(node)"
              ></el-checkbox>
              <i
                style="color: red; margin-left: -7px; padding-right: 3px"
                v-if="data.require"
                >*</i
              >
              <span
                :class="{ isLeaf: node.isLeaf }"
                @click="
                  !data.disabled &&
                    ((node.checked = !node.checked),
                    handleDataManagerChange(node))
                "
                >{{ data.label }}</span
              >
            </template>
          </el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { add, update } from '@/api/yn_plan/engineering'
import { objToHump } from '@/utils/common'
import { mapGetters } from 'vuex'
import { commonQuery } from '@/api/kernel/query'

export default {
  name: 'EngineeringEdit',
  data() {
    const validateCity =(rule, value, callback) => {
      if (!value || !value.length) {
        callback(new Error('请选择地市'))
      }
      let checked = value.map((data) => data[1])
      if (!this.requires.every((r) => checked.includes(r))) {
        callback(new Error('带*号的数据版本必选'))
      }
      callback()
    }

    const validateData =(rule, value, callback) => {
      if (!value || !value.length) {
        callback(new Error('请选择数据源'))
      }
      let checked = value.map((data) => data[1])
      if (!this.requires.every((r) => checked.includes(r))) {
        callback(new Error('带*号的数据版本必选'))
      }
      callback()
    }

    return {
      // 是否显示弹出层
      form: {
        name: '',
        code: null,
        createUser: '',
        province: '',
        provinceName: '',
        city: [],
        cityName: [],
        county: '',
        countyName: '',
        datasource: [],
      },
      title: '新建工程信息',
      currProvince: 'YN',
      open: false,
      loading: false,
      dataLoading: false,
      key: 0,
      areaData: [],
      provinceData: [],
      cityData: [],
      countyData: [],
      options: [],
      props: {
        multiple: true,
        lazy: false,
      },
      requires: [],
      rules: {
        name: [
          { required: true, message: '工程名称不能为空', trigger: 'blur' },
        ],
        province: [
          { required: true, message: '省份不能为空', trigger: 'change' },
        ],
        city: [{ required: true, message: '地市不能为空', trigger: 'change' }],
        datasource: [
          { required: true, validator: validateData, trigger: 'change' },
        ],
      },
    }
  },
  async created() {
    this.dataLoading = true
    this.requires = this.sysconfig.DIPPER_PROJECT_DATAS?.split(',') || []
    await this.$store.dispatch('engineering/clearEngineeringData')
  },
  computed: {
    ...mapGetters(['sysconfig']),
  },
  methods: {
    async init(form) {
      this.open = true
      const { account } = this.$store.getters.user
      await this.loadAreaInfo(/*this.sysconfig.CURRENT_PROVINCE_CODE*/ this.currProvince)
      if (!form) {
        this.form.createUser = account

        this.title = '新建工程信息'
        this.form.name = ''
        this.form.id = ''
        this.form.datasource = []
        if (this.cityData.length === 1) {
          this.form.city = this.cityData.map((o) => o.code)
        }
      } else {
        this.title = '修改工程信息'
        Object.assign(this.form, objToHump(form))
        this.form.datasource =
          typeof this.form.datasource === 'string'
            ? JSON.parse(this.form.datasource)
            : this.form.datasource
        this.form.city =
          typeof this.form.city === 'string'
            ? JSON.parse(this.form.city)
            : this.form.city
      }
      await this.handleDataManagerInit()
    },
    // 加载地区信息
    async loadAreaInfo(code) {
      this.loading = true
      /*
      const data = await commonQuery('province_query')
      this.loading = false
      if (data) {
        this.areaData = data.data
        this.provinceData = this.getChildren()
        if (code) {
          this.form.province = code
          this.handleProvinceChange(code)
        }
      }*/
      this.form.province = code
      const { data } = await commonQuery('qryUserBindCities', {province: code})
      this.loading = false
      this.cityData = data
      if (data.length) {
        this.form.city = [data[0].code]
        this.form.cityName = [data[0].name]
      }
    },
    getChildren(parentId = 0) {
      return this.areaData.filter((o) => o.parent_id === parentId)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          ;(this.form.id ? update(this.form) : add(this.form))
            .then((res) => {
              this.$message.success(this.form.id ? '修改成功' : '创建成功')
              this.$forceUpdate()
              this.open = false
              this.$emit('refresh')
            })
            .catch((err) => {
              // this.$message.error(err.message || "新增失败")
              console.error(err)
            })
            .finally(() => (this.loading = false))
        }
      })
    },
    handleProvinceChange(value) {
      this.form.city = []
      const province = this.provinceData.filter((o) => o.code === value)[0]
      this.form.provinceName = province.name
      this.cityData = this.getChildren(province.id)
    },
    handleCityChange(value) {
      if (value.some(o => o === this.currProvince)) {
        if (value.at(-1) === this.currProvince) {
          this.form.city = [this.currProvince]
        } else {
          this.form.city = value.filter(o => o !== this.currProvince)
        }
      }

      this.form.cityName = this.form.city.map(
        (v) => this.cityData.filter((o) => o.code === v)[0]['name']
      )
    },
    // 废弃
    handleCountyChange(value) {
      const county = this.countyData.filter((o) => o.code === value)[0]
      this.form.countyName = county.name
    },
    async handleDataManagerInit() {
      this.options = await this.$store.dispatch(
        'engineering/getEngineeringData',
        this.requires
      )
      this.key++
      this.dataLoading = false
    },
    handleDataManagerChange(node) {
      if (node.checked && node.level === 3) {
        this.form.datasource = [
          node.path,
          ...this.form.datasource.filter(
            (item) => item.slice(0, 2).join() !== node.path.slice(0, 2).join()
          ),
        ]
      }
    },
  },
}
</script>

<style lang="scss">
.data-manager-cascader {
  .el-cascader-node > .el-checkbox {
    display: none;
  }
  .isLeaf {
    margin-left: 10px;
  }
}
</style>
