import request from '@/utils/request'

//查询任务列表
export function getCadTaskList(queryData) {
  return request({
    url: '/indoor/cad/task_list',
    params:queryData
  })
}

export function getTaskByProjectName(queryData) {
  return request({
    url: '/indoor/cad/getTaskByProjectName',
    params:queryData   ,
    method:'get',
  })
}

//查询任务
export function getCadTask(id) {
  return request({
    url: '/indoor/cad/task/'+id,
    method:'get',
  })
}

//查询任务
export function getCadTaskByOrderNo(orderNo) {
  return request({
    url: '/indoor/cad/taskByOrderNo',
    method:'get',
    params: {
      orderNo: orderNo
    }
  })
}

//查询审核信息
export function getCadCheckInfoById(id) {
  return request({
    url: '/indoor/cad/qryCheckResult',
    method:'get',
    params: {
      id: id
    }
  })
}

//删除任务
export function deleteCadTask(id) {
  return request({
    url: '/indoor/cad/task/'+id,
    method:'delete',
  })
}

// 查询站点图纸列表
export function listCad(data) {
  return request({
    url: '/indoor/cad/query',
    method:'post',
    data
  })
}

// 手动调用分析
export function buildCad(id) {
  return request({
    url: '/indoor/cad/build/'+id
  })
}

export function buildCadBySite(siteIdCmcc) {
  return request({
    url: '/indoor/cad/build?siteIdCmcc='+siteIdCmcc
  })
}


// 获取CAD BOX
export function cadBox(id) {
  return request({
    url: '/indoor/cad/box/'+id
  })
}

// 添加图纸
export function saveCad(type,data) {
  data.status=0;
  return request({
    url: '/indoor/cad/'+type,
    //method: (data.id==null||data.id==undefined)?'post':'put',
    method:'post',
    data:data
  })
}

// 添加图纸
export function batchCad(data) {
  return request({
    url: '/cov/service/dwg/batch',
    method:'post',
    data:data
  })
}

// 添加图纸
export function deleteCad(id) {
  return request({
    url: '/indoor/cad/'+id,
    method: 'Delete'
  })
}

export function getCadAntList(id) {
  return request({
    url: '/indoor/cad/ant/'+id
  })
}

// 添加天线
export function addCad(data) {
  return request({
    url: '/indoor/cad/ant/add',
    method:'post',
    data:data
  })
}

// 调整天线类型
export function changeAntTypeCad(data) {
  return request({
    url: '/indoor/cad/ant/changeAntType',
    method:'post',
    data:data
  })
}


// 删除天线
export function disabledCad(data) {
  return request({
    url: '/indoor/cad/ant/disabled',
    method:'post',
    data:data
  })
}

// 调整天线
export function changeCad(data) {
  return request({
    url: '/indoor/cad/ant/change',
    method:'post',
    data:data
  })
}

// 天线仿真
export function cadSimulation(id) {
  return request({
    url: '/indoor/cad/ant/simulation/'+id,
  })
}




// 获取参数配置
export function saveConfig(data) {
  return request({
    url: '/indoor/config',
    method:'post',
    data:data
  })
}

// 获取配置规则列表
export function getConfigList() {
  return request({
    url: '/indoor/userConfig/userConfigList',
  })
}

// 获取配置规则数据
export function getRuleList(id) {
  return request({
    url: '/indoor/config/listByConfigId/'+id,

  })
}

// dwg
export const leakageDwg = data => {
  return request({
    url: '/indoor/cad/leakage/dwg',
    method:'post',
    params: data
  })
}


// 获取报告数据
export function geReportData(orderNo) {
  return request({
    url: '/indoor/cad/gryReportData',
    method:'get',
    params: {
      orderNo: orderNo
    }
  })
}

export const autoCutService = data => {
  return request({
    url: '/indoor/cad/autoCutService',
    method:'post',
    data
  })
}

//获取正在执行任务数
export function getTaskStatus() {
  return request({
    url: '/indoor/cad/taskStatus?day=1',
    method:'get',
  })
}
