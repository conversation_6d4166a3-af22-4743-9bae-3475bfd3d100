<template>
  <div :class='["form-area", { "glossy": !isShowForm }]'>
    <EditTab
      id="edit-tab"
      :badge='badge'
      :class='{"scroll-tab": tabEditable || firstTabRemovable}'
      :editable='tabEditable || firstTabRemovable'
      :index.sync='tabIndex'
      :nameProp='nameProp'
      :namePropData='namePropData'
      :namePropIndex='namePropIndex'
      :tab-title-name-prefix="tabTitleNamePrefix"
      :tabLabelTitle='tabTitle'
      :tabs='tabList'
      :firstTabRemovable='firstTabRemovable'
      :groupMark="groupMark"
      :enableResourceConfig="enableResourceConfig"
      :libraryType="libraryType"
      :resourceType="resourceType"
      :canAddResource="canAddResource"
      :canDeleteResource="canDeleteResource"
      @addTab='addTab'
      @changeTabIndex='switchTab'
      @removeTab='removeTab'
      @recoverTab="recoverTab"
    >
      <template slot-scope='scope'>
        <slot name='editTab' v-bind='scope' />
      </template>
    </EditTab>
    <div class='flex-adaptive'>
      <!--      基站，小区，射频，天线-->
      <BasicForm
        :form-id="groupMark + '_' + tabIndex "
        :beforePopupCondition='beforePopupCondition'
        :disabled='!formEditable'
        :fields='getFormFields'
        :formValue='tabList && tabList[tabIndex]'
        :groupColumn='getAdaptFormColumn'
        :groupMark='groupMark'
        :hiddenFiels='hiddenFiels'
        :key='groupMark + tabIndex'
        :showChangeToolTip='showChangeFormToolTip'
        :useFieldDefaultValue='false'
        @fieldCascading='handleFormCascading'
        @returnFormValue='returnFormValue'
        @selectChanged="selectChanged"
        class='hide-form-slot'
        ref='form'
        size='small'
      >
        <slot name='form' />
      </BasicForm>
      <Swiper :list='getSwiperImageList' v-if='getAdaptShowSwiper' />
      <template v-else>
        <slot />
      </template>
    </div>
    <section v-if='showUploader'>
      <el-upload
        :auto-upload='false'
        :before-upload='handleBeforeUpload'
        :disabled='!formEditable'
        :http-request='handleCustomUpload'
        :limit='getFileFields && getFileFields.length'
        :multiple='multiple'
        :on-change='handleUploadChange'
        :on-exceed='handleUploadExceed'
        :show-file-list='false'
        action='action'
        ref='uploader'
      >
        <template v-for='(file, index) in getFileFields'>
          <div
            :class='[
							file.name,
							"plan-file-upload",
							{
								"file-upload--disable": !formEditable
							}
						]'
            :data-name='file.name'
            :data-url='getCurrentTabData && getCurrentTabData[file.name]'
            :draggable='getCurrentTabData && getCurrentTabData[file.name]'
            :key='file.name + +new Date()'
            @click='currentTrigger = Object.assign(file, {element: $event})'
            @dragover.prevent
            @dragstart='dragStart'
            @drop.prevent='drop'
            element-loading-background='rgba(0, 0, 0, 0.6)'
            element-loading-spinner='el-icon-loading'
            element-loading-text='拼命上传中'
            slot='trigger'
            v-if='file.formEdit'
            v-loading='file.upload'
          >
            <div class='el-upload__hooker'>
              <div>
                <span class='file-required' v-if='file.required'>*</span>
                {{file.title}}
              </div>
              <i class='el-icon-plus' />

              <template v-if='getCurrentTabData && getCurrentTabData[file.name]'>
                <img
                  :data-src='getFileUrl(getCurrentTabData[file.name])'
                  class='drag-image'
                  v-img-lazy
                />

                <span class='el-upload-list__item-actions'>
									<span class='upload-icon'>
										<span @click.stop='previewImage(file)' style='margin-right: 10px;'>
											<i class='el-icon-zoom-in'></i>
										</span>
										<span @click.stop='removeImageFile(file)'>
											<i class='el-icon-delete'></i>
										</span>
									</span>
								</span>
              </template>
            </div>
          </div>
        </template>
      </el-upload>
      <el-dialog
        :visible.sync='previewDialog'
        append-to-body
        title='预览'
        width='800'
      >
        <img :src='currentTriggerUrl' style='width: 100%; height: 100%' />
      </el-dialog>
    </section>

    <el-button @click='$emit("addDefaultTab")' class='add-button' type='primary' v-if='getHasShowAddBtn'>添加{{tabTitle}}</el-button>

    <el-dialog
      :before-close='handleClose'
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      :visible.sync='ocrResolved'
      title='图像识别回填'
      v-if='ocrResolved'
      width='50%'
    >
      <div class='ocr-group'>
        <img :src='ocrResolvedData.url' />
        <el-checkbox-group v-model='ocrResolvedBackData'>
          <el-checkbox
            :key='item.name'
            :label='item.name'
            v-for='item in ocrResolvedData.fields'
          >
            <label class='field-title'>{{item.title}}:</label>
            <el-input disabled v-model='item.ocrResolvedValue' />
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <p v-if='ocrResolvedData.ocrResultNothing'>未识别到任何信息,请确认识别图像是否正确!</p>
      <span slot='footer'>
				<el-button @click='ocrResolved = false'>取 消</el-button>
				<el-button @click='handleOcrResolved' type='primary'>回 填</el-button>
			</span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { compress } from 'image-conversion'
import { isNull } from '@/utils/common'
import siteAttachmentApi from '@/api/site/siteAttachmentApi'
import { useOcrRealTimeResolved, useRecursionCallBack, useStringLast, useStringPrev } from '@/utils/plan/useBusiness'
import {deepClone, isJsonResolve, toLine} from "@/utils";
import Swiper from '@/components/BasicComponents/Swiper'
import BasicForm from '@/components/BasicComponents/Form'
import EditTab from '@/components/BasicComponents/EditTab'
import { recoverBaseBandState, recoverCellState, removeBaseBandState, removeCellState } from '@/utils/tabs'
import * as _ from 'lodash'
import { clearResourceCode_ } from '@/utils/plan/handle'
import store from '@/store'

const defaultFileSize = 100 // 图片上传默认大小100M
const compressThreshold = 2 // 超过2M对图片进行压缩
let isRadioTrigger = false // 文件上传单选模式

export default {
  name: 'formArea',
  props: {
    enableInherit: {
      type: Boolean,
      default: false
    },
    formId:String,
    tabEditable: Boolean,
    formEditable: Boolean,
    tabTitleNamePrefix: String,
    tabTitle: String,
    tabList: {
      type: Array,
      default: () => [{}],
    },
    firstTabRemovable: Boolean,
    fileStructurePath: String,
    formFields: Array,
    formColumn: Number,
    hiddenFiels: Array,
    nameProp: String,
    namePropData: Array,
    namePropIndex: {
      type: Boolean,
      default: true,
    },
    groupMark: String,
    showSwiper: {
      type: Boolean,
      default: true,
    },
    showUploader: {
      type: Boolean,
      default: true,
    },
    showAddFormButton: {
      type: Boolean,
      default: true,
    },
    badge: Boolean,
    multiple: Boolean,
    virtualDelete: Boolean, //虚拟删除tab
    showChangeFormToolTip: Boolean,
    beforePopupCondition: Function,

    // 新增资源配置相关props
    enableResourceConfig: {
      type: Boolean,
      default: false
    },
    libraryType: {
      type: String,
    },
    resourceType: {
      type: String,
      default: ''
    },
    canAddResource: {
      type: Boolean,
      default: true
    },
    canDeleteResource: {
      type: Boolean,
      default: true
    }
  },
  inject: ['siteCmccId', 'stage'],
  data() {
    return {
      tabIndex: '0',
      batchFileCount: 1,
      currentTrigger: null,
      currentTriggerUrl: '',
      dragStartTrigger: {}, // 起始拖拽图片数据
      recordBatchTrigger: {}, // 记录批量上传trigger
      isShowForm: true,
      previewDialog: false,
      fileUploadLoading: false,
      filesType: ['pic', 'file'],
      ocrLoading: null,
      ocrResolved: false,
      ocrResolvedData: {},
      ocrResolvedBackData: [],
    }
  },
  watch: {
    tabList: {
      handler(newval) {
        //设置只有一个基站时，即默认选中,解决删除基站后因基站没有选中而导致下拉框未只读
        if(this.groupMark==='enodeb' && newval.length===1){
          this.tabIndex='0';
        }
        let showForm = false
        if (!this.showAddFormButton) {
          this.isShowForm = true
          return
        }

        if (newval && newval.length) {
          if (JSON.stringify(newval) === '[{}]') {
            showForm = false
          } else {
            showForm = true
          }
        }
        this.isShowForm = showForm
      },
      immediate: true,
    },
  },
  created() {
    // if(this.groupMark === 'enodeb') console.log('formArea.formValue:', this.tabList);
  },
  computed: {
    getHasShowAddBtn() {
      const {nodeId,flowKey} = this.$route.query;
      if(flowKey && flowKey==='cmcc_5g_plan'){
        //需求库、规划库、设计库显示新增按钮，其它阶段不显示.
        const isShow = !this.isShowForm && (nodeId && ['jta54ce3bf238a44eab1c6a8ac88a33804','jt3b453adde06a4772b465c4d6fb1ccc4e','jt3ca48544a50e43ae9dfec174d28b6447'].includes(nodeId));
        // console.log('groupMark:',this.groupMark,', 是否显示:',isShow);
        return isShow;
      }
      return  !this.isShowForm;
    },
    getFormFields() {
      // console.log('this.formFields:',this.formFields,',tabIndex:',this.tabIndex,',groupmark:',this.groupMark);
      const formFields =  this.formFields?.filter((field) => !this.filesType.includes(field.formType))
      //设置需求库，基站1的基站模式、网络制式、频段、配置、站型不可选
      if(this.tabIndex==='0' && this.groupMark==='enodeb' && this.$route.query?.nodeId==='jt3b453adde06a4772b465c4d6fb1ccc4e'){
        const fieldCopyArr = deepClone(formFields,true);
        _.forEach(fieldCopyArr,function(item){
          if(item.name && ['enodebType','networkStandard','cellBand','configure','indoorFlag'].includes(item.name)){
            item.formEdit=false
          }
        });
        store.state.plan.enodebFirstFields = fieldCopyArr;//基站1字段配置单独存放，其它基站字段配置相同
        return fieldCopyArr;
      }else{
        return formFields;
      }
    },
    getFileFields() {
      return this.formFields?.filter((field) => field.formType === 'pic')
    },
    getAdaptShowSwiper() {
      return this.showSwiper ? this.getFileFields?.length : this.showSwiper
    },
    getAdaptFormColumn() {
      if (!this.showSwiper) return this.formColumn
      if (!this.getAdaptShowSwiper) return +this.formColumn + 1
      return this.formColumn
    },
    getCurrentTabData() {
      return this.tabList && this.tabList[this.tabIndex]
    },
    getSwiperImageList() {
      const swiperList = []
      const currentData = this.getCurrentTabData
      if (this.getFileFields && currentData) {
        this.getFileFields.forEach((field) => {
          const src = this.getFileUrl(currentData[field.name])
          src && swiperList.push({ src, title: field.title })
        })
      }
      return swiperList
    },
  },
  methods: {
    handleAdd() {
      this.addTab()
    },
    handleInherit() {
      this.$emit('onInherit')
    },
    recoverTab(index) {
      if (this.groupMark === 'cell') {
        this.tabList.forEach((cell) => {
          if (cell.closable === false) this.$set(cell, 'closable', true)
        })
      }
      //恢复小区的生命周期状态，射频单元与天线的状态
      recoverCellState(this.tabList[index],this.groupMark);
      //恢复基带设备的CU,BU,状态
      if(this.groupMark === 'baseBand') {
        recoverBaseBandState(this.tabList[index]);
        //检查是否存在名称相同的，将其移除
        const temData = this.tabList[index];
        let indexRemove = undefined;
        this.tabList.forEach((item,indexTmp)=>{
          if(temData[this.nameProp] === item[this.nameProp] && index !== indexTmp){indexRemove = indexTmp;}
        })
        indexRemove && this.tabList[indexRemove].modifyType === 'additions' && this.tabList.splice(indexRemove,1);
      }
      useRecursionCallBack(this.tabList[index], (obj) => {
        this.$set(obj, 'recover', false)
        this.$set(obj, 'closed', false)
        delete obj.formType
        delete obj.modifyType
      })
    },
    setImage(data, file) {
      const currentData = this.getCurrentTabData

      file.upload = false
      const fileUrl = data?.url ? data.url + '?time=' + +new Date() : ''
      this.$set(currentData, file.name, fileUrl)

      // 单选 && 图片是懒加载模式：要设置图片的src
      try {
        if (isRadioTrigger && fileUrl) {
          const uploadTrigger = this.currentTrigger.element.target.offsetParent
          this.$nextTick(() => {
            const triggerImage = uploadTrigger?.querySelector('.drag-image')
            if (triggerImage?.dataset.src) {
              triggerImage.src = this.getFileUrl(fileUrl)
            }
          })
        }

        const ocrFieldMap = useOcrRealTimeResolved(file)

        if (ocrFieldMap && this.ocrLoading) {
          this.ocrLoading?.close()
          this.ocrResolved = true
          this.ocrLoading = null
          const ocrResolvedResult = data?.picIdentification || {}

          const map = Object.keys(ocrFieldMap)

          const fields = this.formFields.filter((i) => {
            if (map.includes(i.name)) {
              const mapKey = ocrFieldMap[i.name].fieldMap
              this.$set(i, 'ocrMapKey', mapKey)
              this.$set(i, 'ocrResolvedValue', ocrResolvedResult[mapKey + 'Text'] ?? ocrResolvedResult[mapKey])
              i.ocrResolvedValue && this.ocrResolvedBackData.push(i.name)
              return true
            }
          })

          this.ocrResolvedData = {
            field: file,
            url: this.getFileUrl(data?.url),
            response: data,
            fields,
            ocrFieldMap,
            currentData,
            ocrResultNothing: fields.every((i) => isNull(i.ocrResolvedValue)),
          }
        } else {
          this.$emit('setImage', currentData, data, file.name, this.groupMark)
        }
      } catch (err) {
        console.log(err)
      }
    },
    removeImageFile(field) {
      this.$set(this.getCurrentTabData, field.name, '')
    },
    previewImage(field) {
      this.previewDialog = true
      this.currentTriggerUrl = this.getFileUrl(
        this.getCurrentTabData[field.name],
      )
    },
    handleBeforeUpload(file) {
      const fileName = useStringPrev(file.name, '.')
      const fileType = useStringLast(file.name, '.')

      /*
       *	单选模式下精准触发上传的trigger
       *	多选模式：
       * 1. 跳过图像识别的trigger
       * 2. 文件名include匹配
       * 3. 剩余空位按顺序匹配
       * 4. 多余的丢弃
       */
      let ocrResolvedField = null
      let currentTriggerField = null

      if (this.batchFileCount === 1) {
        isRadioTrigger = true
        currentTriggerField = this.currentTrigger
      } else {
        isRadioTrigger = false
        currentTriggerField = this.useFindCurrentTriggerField(fileName)
      }

      // 按文件名匹配失败, 查找第一个未上传的空位, 如果这个空位是图像识别trigger, 放弃上传
      // 否则顺序匹配会错乱 jump:true(跳过可上传的空位)
      if (!currentTriggerField) {
        const uploadableTrigger = this.getFileFields.find(
          (filed) =>
            !this.getCurrentTabData[filed.name] && !filed.upload && !filed.jump,
        )

        if (!uploadableTrigger) return false

        ocrResolvedField = useOcrRealTimeResolved(uploadableTrigger)
        if (ocrResolvedField) {
          uploadableTrigger.jump = true
          return false
        } else {
          currentTriggerField = uploadableTrigger
        }
      }

      // before钩子因为对图片进行压缩处理, 所以返回的是promise
      // 导致上传的时候currentTrigger永远都是最后一个
      this.currentTrigger = currentTriggerField
      this.recordBatchTrigger[file.name] = { ...this.currentTrigger }

      // 校验文件类型、文件大小
      if (!this.useValidFileType(currentTriggerField, fileType)) return false
      if (!this.useValidFileSize(file.size)) return false

      !ocrResolvedField &&
      (ocrResolvedField = useOcrRealTimeResolved(currentTriggerField))

      if (ocrResolvedField) {
        if (!isRadioTrigger) return false // 多选模式 跳过图像识别
        this.ocrResolvedBackData = []
        this.ocrLoading = this.$loading({
          body: true,
          lock: true,
          fullscreen: true,
          customClass: 'loading-circle',
          text: '图像识别中,请稍后',
          background: 'rgba(255, 255, 255, 0.3)',
        })
      } else {
        this.$set(currentTriggerField, 'upload', true)
      }
      // 默认压缩
      return new Promise((resolve, reject) => {
        const size = file.size / 1024 / 1024
        if (
          size >= compressThreshold &&
          this.imagesType.includes(fileType.toLowerCase())
        ) {
          compress(file, {
            quality: 0.6,
            type: file.type,
            scale: 0.6,
          }).then((res) => resolve(res))
        } else {
          resolve()
        }
      })
    },
    useFindCurrentTriggerField(name) {
      return this.getFileFields?.find((field) => name.includes(field.title))
    },
    useValidFileType(field, fileType) {
      const { param1 } = field
      let imagesType = this.imagesType
      if (isJsonResolve(param1)) {
        const config = JSON.parse(param1)
        if (Array.isArray(config) && config.length) {
          imagesType = config
        }
      }

      if (imagesType.includes(fileType.toLowerCase())) {
        return true
      } else {
        this.$message.error(
          `文件格式不正确, 请上传${imagesType.join('、')}格式文件!`,
        )
        return false
      }
    },
    useValidFileSize(fileSize) {
      const size = fileSize / 1024 / 1024

      if (size > defaultFileSize) {
        this.$message.error(`上传文件大小不能超过 ${defaultFileSize} MB!`)
        return false
      }
      return true
    },
    async handleCustomUpload({ file }) {
      const { name, param4 } = this.recordBatchTrigger[file.name]
      const params = {
        id: this.getCurrentTabData?.id,
        objectType: this.groupMark,
        objectId: this.getCurrentTabData?.resourceCode,
        fileName: toLine(name),
        classify: toLine(name),
        path: this.fileStructurePath,
        siteIdCmcc: this.siteCmccId.value,
        stageCode: this.stage.value,
        nodeId: this.$route.query?.nodeId,
        businessType: 'upload_pic',
        checkRepeat: true,
        module: this.$route.query?.fileUploadModule,
        ocr: param4 || '{}',
      }
      const formData = new FormData()
      formData.append('file', file)

      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })

      const currentTrigger = this.getFileFields?.find(
        (field) => field.name == name,
      )
      const result = await siteAttachmentApi.upload(formData).catch((err) =>
        this.handleUploadError(currentTrigger),
      )
      this.handleUploadSuccess(result, currentTrigger)

      // 上传成功后清空记录
      delete this.recordBatchTrigger[file.name]
    },
    handleUploadChange(file, fileList) {
      const length = fileList.length
      this.batchFileCount = Math.max(this.batchFileCount, length)

      setTimeout(() => {
        if (length !== this.batchFileCount) return
        this.$refs.uploader.submit()

        // 上传完成后将最大文件数量清零
        this.batchFileCount = 0
      }, 0)
    },
    handleUploadError(field) {
      this.$set(field, 'upload', false)
      this.ocrLoading?.close()
    },
    handleUploadSuccess(res, field) {
      if (res.code == this.HTTP_CODE.SUCCESS) {
        this.$message.success(res.msg ? res.msg : '上传成功')
        this.setImage(res.data, field)

        this.$nextTick(() => {
          this.$refs.uploader.clearFiles()
        })
      } else {
        this.$message.error(res.msg ? res.msg : '上传异常!')
        this.handleUploadError(field)
      }
    },
    handleUploadExceed(file) {
      this.$message.error(
        `超出上传数量限制,最大文件选择数量不得超过${this.getFileFields.length}个!`,
      )
    },
    handleClose(done) {
      this.$confirm('关闭后回填信息需要重新识别,是否确认?')
        .then((_) => {
          done()
        })
        .catch((_) => {})
    },
    handleOcrResolved() {
      const { field, response, currentData, ocrFieldMap } = this.ocrResolvedData
      Object.keys(ocrFieldMap).forEach((key) => {
        if (!this.ocrResolvedBackData.includes(key)) {
          delete response.picIdentification[ocrFieldMap[key].fieldMap]
        }
      })
      this.ocrResolved = false
      this.$emit('setImage', currentData, response, field.name, this.groupMark)
    },
    addTab(index) {
      let indexKey = 0;
      //基带设备新增的tab由关闭索引的位置来决定基础对象
      if(this.groupMark === 'baseBand' && index) {
        indexKey = index;
      }
      const tabData = deepClone(this.tabList[indexKey], true, false, true)
      if (this.tabList[this.tabList.length - 1])  {
        tabData.sort = ( this.tabList[this.tabList.length - 1].sort ?? 0) + 1
      } else {
        tabData.sort = 0
      }
      // 机房、天面复制数据的时候清空站点ID(ZD号) 交给后台生成
      if (['room', 'roof'].includes(this.groupMark)) {
        delete tabData[this.groupMark + 'IdCmcc']
      }
      //清空新增数据的综资标识
      clearResourceCode_(tabData);
      this.tabList.push(tabData);
      this.$emit('addTab', tabData, this.groupMark)
    },
    //移除tab
    removeTab(index,allowAdd) {
      //如果只有一个tab，则提示需要添加一个tab
      // const removeData = this.tabList[index]
      if (!this.virtualDelete && this.groupMark !== 'baseBand') {
        this.tabList.splice(index, 1);
        // 重排sort
        this.tabList.forEach((tab, index) => { tab.sort = index})
        index <= this.tabIndex && (this.tabIndex = String(this.tabIndex--))
      }
      //移除小区、射频单元、天线状态
      removeCellState(this.tabList[index],this.groupMark);
      if(this.groupMark === 'baseBand') {
        //allowAdd=true则允许添加
        allowAdd && (this.addTab(index));
        //移除基带设备的CU,BU的状态
        removeBaseBandState(this.tabList[index]);
      }
      this.$emit('removeTab', index, this.tabList[index], this.groupMark)
    },
    switchTab(index) {
      this.tabIndex = index
      if (this.multiple && this.getFileFields?.length) {
        // 清空图片上传空位标记
        this.getFileFields.forEach((field) => {
          delete field.jump
        })
      }
      this.$emit('switchTab', index, this.groupMark)
    },
    selectChanged(data) {
      this.$emit('selectChanged', data, this.groupMark)
    },
    returnFormValue(form, mark) {
      if (this.getCurrentTabData) {
        Object.keys(form).forEach((key) => {
          this.$set(this.tabList[this.tabIndex], key, form[key])
        })
        this.$emit('returnFormValue', form, mark)
      }
    },
    setFormValue(data) {
      // console.log('设置表单默认值:', data);
      if (data && Object.keys(data).length) {
        this.$refs.form.setFormValue(data)
      }
    },
    resetFormValue() {
      this.$refs.form.resetForm()
    },
    handleFormCascading(field, value, autoTrigger) {
      this.$emit('handleFormCascading', field, value, autoTrigger, this.groupMark)
    },
    dragStart(e) {
      const originElement = e.target
      const { name, url } = originElement.dataset

      // 没有图片或者是单选模式(单选不存在上传识别出错的问题) 禁止拖拽
      if (!url || !this.multiple) {
        e.preventDefault()
        return
      }

      // 需要进行图像识别的图片字段,禁止拖拽
      const dragField = this.useFindDragField(name)
      if (useOcrRealTimeResolved(dragField)) {
        this.$message.error(`[${dragField.title}]是图像识别图片，禁止拖拽！`)
        e.preventDefault()
        return
      }

      // 设置起始拖拽图片数据
      this.dragStartTrigger = {
        name,
        url,
        mask: originElement.querySelector('.el-upload-list__item-actions'),
      }

      // 隐藏hover触发的遮罩层
      this.useDragMaskDisplay('none')

      // 设置拖拽属性
      const image = originElement.querySelector('.drag-image')
      e.dataTransfer.effectAllowed = 'link'
      e.dataTransfer.setDragImage(image, 10, 10)
    },
    drop(e) {
      // 恢复hover遮罩
      this.useDragMaskDisplay('inline-block')

      // 找到存放有dataset数据的目标节点
      const realTargetEl = e.target.offsetParent

      if (realTargetEl) {
        const { name } = realTargetEl.dataset
        if (name === this.dragStartTrigger.name) return

        const targetField = this.useFindDragField(name)
        if (useOcrRealTimeResolved(targetField)) {
          this.$message.error(
            `[${targetField.title}]需要单独上传进行图像识别，无法完成拖拽交换！`,
          )
          return
        }

        // 交换拖拽图片
        this.useDragExchangeTrigger(realTargetEl)
      }
    },
    useFindDragField(name) {
      return this.getFileFields.find((field) => field.name === name)
    },
    useDragMaskDisplay(display) {
      this.dragStartTrigger.mask &&
      (this.dragStartTrigger.mask.style.display = display)
    },
    useDragExchangeTrigger(dropElement) {
      const { name: startName, url: startUrl, mask } = this.dragStartTrigger
      const { name: endName, url: endUrl } = dropElement.dataset

      // 交换后图片地址发生变化: room30 -> room90
      // 调接口重命名图片名称
      siteAttachmentApi.imageRename({
        siteIdCmcc: this.siteCmccId.value,
        id: this.getCurrentTabData?.id,
        sourceUrl: startUrl?.split('?')[0],
        sourceClassify: toLine(startName),
        decUrl: endUrl?.split('?')[0],
        descClassify: toLine(endName),
      }).then((res) => {
        // 设置起始拖拽图片地址
        const startImage = mask.previousElementSibling

        endUrl &&
        (startImage.src =
          this.getFileUrl(startUrl).split('?')[0] + '?time=' + +new Date())

        // 目的地没有图片
        if (!this.getCurrentTabData[endName]) {
          this.$set(
            this.getCurrentTabData,
            endName,
            startUrl.replace(toLine(startName), toLine(endName)),
          )

          // 清空起始位置图片
          this.$set(this.getCurrentTabData, startName, '')
        } else {
          // 设置目标图片地址
          const endImage = dropElement.querySelector('.drag-image')
          endImage.src =
            this.getFileUrl(endUrl).split('?')[0] + '?time=' + +new Date()
        }
      })
    },
  },
  components: {
    Swiper,
    EditTab,
    BasicForm,
  },
}
</script>

<style lang='scss' scoped>
@import '@/assets/styles/plan/common.scss';
.form-area {
  ::v-deep {
    .edit-tab {
      padding-bottom: 10px;
    }
    .el-upload {
      height: 0;
      text-align: left;
    }
  }

  .scroll-tab {
    ::v-deep {
      .el-tabs__nav-wrap.is-scrollable {
        padding: 0 60px 0 20px;
      }
      .el-tabs__nav-next {
        right: 35px;
      }
    }
  }
}
.el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 25px;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  z-index: 6;
  &:hover {
    opacity: 1;
    & span {
      display: inline-block;
    }
  }
}
.upload-icon {
  position: absolute;
  top: 40%;
  margin-left: -28px;
  margin-top: -18px;
}
.file-required {
  margin-right: 6px;
  color: #ff4949;
}

.opacity-none {
  opacity: 0;
}
// 毛玻璃特效
.glossy {
  position: relative;
  .add-button {
    position: absolute;
    top: 45%;
    left: 45%;
    z-index: 3;
    display: block;
  }
  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.7);
    z-index: 2;
  }
}

.ocr-group {
  display: flex;
  img {
    width: 40%;
    margin-right: 50px;
  }
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    .el-checkbox {
      margin-bottom: 20px;
    }
    .field-title {
      width: 100px;
      display: inline-block;
    }
  }
  & + p {
    color: red;
  }
}
</style>
