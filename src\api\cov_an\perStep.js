import request from '@/utils/request'

// 查询分析-方案-步骤列表
export function listStep(query) {
  return request({
    url: '/an/step/list',
    method: 'get',
    params: query
  })
}

// 查询分析-方案-步骤详细
export function getStep(id) {
  return request({
    url: '/an/step/' + id,
    method: 'get'
  })
}

// 新增分析-方案-步骤
export function addStep(data) {
  return request({
    url: '/an/step',
    method: 'post',
    data: data
  })
}

// 修改分析-方案-步骤
export function updateStep(data) {
  return request({
    url: '/an/step',
    method: 'put',
    data: data
  })
}

// 删除分析-方案-步骤
export function delStep(id) {
  return request({
    url: '/an/step/' + id,
    method: 'delete'
  })
}