import request from '@/utils/request'

// 查询优化方案列表
export function listScheme(query) {
  return request({
    url: '/hvpAn/optimizeScheme/list',
    method: 'get',
    params: query
  })
}

// 查询优化方案详细
export function getOptimizeScheme(id) {
  return request({
    url: '/hvpAn/optimizeScheme/' + id,
    method: 'get'
  })
}

// 新增优化方案
export function addOptimizeScheme(data) {
  return request({
    url: '/hvpAn/optimizeScheme',
    method: 'post',
    data: data
  })
}

// 修改优化方案
export function updateScheme(data) {
  return request({
    url: '/hvpAn/optimizeScheme',
    method: 'put',
    data: data
  })
}

// 删除优化方案
export function delScheme(id) {
  return request({
    url: '/hvpAn/optimizeScheme/' + id,
    method: 'delete'
  })
}