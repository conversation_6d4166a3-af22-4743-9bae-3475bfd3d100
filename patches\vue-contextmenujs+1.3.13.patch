diff --git a/node_modules/vue-contextmenujs/src/components/Submenu.vue b/node_modules/vue-contextmenujs/src/components/Submenu.vue
index bdcbce9..114295b 100644
--- a/node_modules/vue-contextmenujs/src/components/Submenu.vue
+++ b/node_modules/vue-contextmenujs/src/components/Submenu.vue
@@ -14,6 +14,7 @@
               :class="[
                 commonClass.menuItem, commonClass.unclickableMenuItem,
                 'menu_item', 'menu_item__disabled',
+                item.customClass ? item.customClass : null,
                 item.divided?'menu_item__divided':null
               ]"
               :key="index"
@@ -29,6 +30,7 @@
               :class="[
                 commonClass.menuItem, commonClass.unclickableMenuItem,
                 'menu_item', 'menu_item__available',
+                item.customClass ? item.customClass : null,
                 activeSubmenu.index===index? 'menu_item_expand':null,
                 item.divided?'menu_item__divided':null
               ]"
@@ -46,6 +48,7 @@
               :class="[
                 commonClass.menuItem, commonClass.clickableMenuItem,
                 'menu_item', 'menu_item__available',
+                item.customClass ? item.customClass : null,
                 item.divided?'menu_item__divided':null
               ]"
               :key="index"
