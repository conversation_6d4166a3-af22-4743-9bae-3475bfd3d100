import request from '@/utils/request'

// 查询通用查询列表
export function listQuery(query) {
  return request({
    url: '/kernel/commonquery/list',
    method: 'get',
    params: query
  })
}

// 查询通用查询详细
export function getQuery(id) {
  return request({
    url: '/kernel/commonquery/detail/' + id,
    method: 'get'
  })
}

export function getQueryByCode(code) {
  return request({
    url: '/kernel/commonquery/detailByCode/' + code,
    method: 'get'
  })
}

// 新增通用查询
export function addQuery(data) {
  return request({
    url: '/kernel/commonquery',
    method: 'post',
    data: data
  })
}

// 修改通用查询
export function updateQuery(data) {
  return request({
    url: '/kernel/commonquery',
    method: 'put',
    data: data
  })
}

// 复制通用查询
export function copyQuery(data) {
  return request({
    url: '/kernel/commonquery/copy',
    method: 'post',
    data: data
  })
}

// 修改报表样式
export function editStyle(data) {
  return request({
    url: '/kernel/commonquery/editStyle',
    method: 'post',
    data: data
  })
}

// 删除通用查询
export function delQuery(id) {
  return request({
    url: '/kernel/commonquery/del/' + id,
    method: 'delete'
  })
}

export function delQueryField(id) {
  return request({
    url: '/kernel/commonquery/del/field/' + id,
    method: 'delete'
  })
}

export function validSql(data) {
  return request({
    url: '/kernel/commonquery/valid',
    method: 'post',
    data: data
  })
}

export function changeStatus(changeType,data) {
  return request({
    url: '/kernel/commonquery/changeStatus/' + changeType,
    method: 'put',
    data: data
  })
}

//清除缓存
export function clearCacheData(code) {
  return request({
    url: '/kernel/commonquery/clearCacheData/' + code,
    method: 'get'
  })
}

//刷新mybits
export function refresh() {
  return request({
    url: '/kernel/commonquery/reload',
    method: 'post',
    data: "ALL"
  })
}


// 通用查询 by code
export function commonQuery(code, query) {
  return request({
    url: '/kernel/dynamic/query/' + code,
    method: 'get',
    params: query
  })
}

// 通用查询 by url
export function queryByUrl(url,params) {
  return request({
    url: url,
    method: 'get',
    params: params
  })
}


// 通用查询 by code post
export function commonQueryByPost(code,data, key) {
  return request({
    url: '/kernel/dynamic/query/byPost/' + code,
    method: 'post',
    data,
    key
  })
}

export function commonQueryFieldData(code, query) {
  return request({
    url: '/kernel/dynamic/field/data/' + code,
    method: 'get',
    params: query
  })
}

// 查询字段,只查询显示的
export function commonQueryField(code) {
  return request({
    url: '/kernel/dynamic/field/' + code,
    method: 'get'
  })
}

// 查询字段,所有字典
export function commonQueryFieldALL(code) {
  return request({
    url: '/kernel/dynamic/field/all/' + code,
    method: 'get'
  })
}

//导出
export function commonQueryExportData(data) {
  return request({
    url: '/kernel/data/export' ,
    method: 'post',
    data: data
  })
}

/**
 * 自定义导出字段
 *
 * @param {*} codeType
 * @param {*} code
 * @returns
 */
export function qryCustomExportField(codeType,code) {
  return request({
    url: '/kernel/data/custom/export/field/' + codeType + "/" + code,
    method: 'get'
  })
}

//执行SQL
export function commonQueryBySql(data) {
  return request({
    url: '/kernel/db/operation',
    method: 'post',
    data: data
  })
}

//导出
export function dbExportExcel(data) {
 return request({
 url: '/kernel/db/exportExcel',
  method: 'post',
  data: data,reqType: 'back', responseType: 'blob',
  headers: { },
 })
}


//dbAuth
export function commonQueryDbAuth(data) {
  return request({
    url: '/kernel/db/auth',
    method: 'post',
    data: data
  })
}

export function qrySchema(system) {
  return request({
    url: '/kernel/db/qry/schema/' + system,
    method: 'get'
  })
}


export function qryDataBaseTableViews(dataSource,schemaName) {
  return  request({
    url: '/kernel/db/qryDataBaseTableViews',
    method: 'get',
    params:{
      dataSource: dataSource,
      schemaName: schemaName
    },
  })
}

export function qryDataBaseTableViewDetails(param) {
  return  request({
    url: '/kernel/db/qryDataBaseTableViews/detail',
    method: 'get',
    params:param,
  })
}
export function getStructure(param) {
  return  request({
    url: '/kernel/db/getStructure',
    method: 'get',
    params:param,
  })
}


//导出
export function commonQueryBatchExport(data) {
  return  request({
    url: '/kernel/dynamic/batchExport',
    method: 'post',
    data: data,reqType: 'back', responseType: 'blob',
    headers: {  },
  })
}


// 查询三方接口Token
export function qryThirdInterToken(configCode) {
  return request({
    url: '/kernel/dynamic/third/inter/token/' + configCode,
    method: 'get'
  })
}

// 设置当前用户的通用查询字段排序
export function setCurrentFieldSort(code, userId, fieldSortList) {
  return request({
    url: '/kernel/commonquery/setCurrentFieldSort/' + code,
    method: 'post',
    data: {userId: userId, fieldSortList: fieldSortList}
  })
}

// 查询当前用户的通用查询字段排序
export function findCurrentFieldSort(code, userId) {
  return request({
    url: '/kernel/commonquery/findCurrentFieldSort/' + code,
    method: 'get',
    params: {userId: userId}
  })
}

export function currentFieldSortIsShow(code, userId, fieldId, show) {
  return request({
    url: '/kernel/commonquery/currentFieldSortIsShow/' + code,
    method: 'put',
    params: {userId: userId, fieldId: fieldId, show: show}
  })
}

export function restoreDefaultSortableSettings(code, userId) {
  return request({
    url: '/kernel/commonquery/restoreDefaultSortableSettings/' + code,
    method: 'DELETE',
    params: {userId: userId}
  })
}

// 导入CSV文件
export function importCsv(data) {
  return request({
    url: '/kernel/db/csvImport',
    method: 'post',
    data: data
  })
}

// 添加SQL同步的接口
export function syncSql(data) {
  return request({
    url: '/service/syncOperation/sync',
    method: 'post',
    data
  })
}
// 添加SQL同步的接口
export function checkPwdSyncSql(data) {
  return request({
    url: '/service/syncOperation/checkSqlSyncPwd',
    method: 'post',
    data:data
  })
}

// 获取采集对比更新信息的接口
export function queryCrsCuUpdateData(code, query) {
  return request({
    url: '/service/comprehensive/queryCrsCuUpdateData/' + code,
    method: 'get',
    params: query
  })
}

// 获取通用查询下拉选项
export function querySelectData(url) {
  return request({
    url,
    method: 'get',
  })
}
