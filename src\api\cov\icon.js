import request from '@/utils/request'

// 查询icon列表
export function listIcon(query) {
  return request({
    url: '/cov/icon/list',
    method: 'get',
    params: query
  })
}

// 查询icon详细
export function getIcon(id) {
  return request({
    url: '/cov/icon/' + id,
    method: 'get'
  })
}

// 新增icon
export function addIcon(data) {
  return request({
    url: '/cov/icon',
    method: 'post',
    data: data
  })
}

// 修改icon
export function updateIcon(data) {
  return request({
    url: '/cov/icon',
    method: 'put',
    data: data
  })
}

// 删除icon
export function delIcon(id) {
  return request({
    url: '/cov/icon/' + id,
    method: 'delete'
  })
}