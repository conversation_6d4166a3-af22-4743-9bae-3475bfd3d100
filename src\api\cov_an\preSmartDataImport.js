import request from "@/utils/request";

// 调用九天智能识别接口
export function smartNip(data, name, code) {
  return request({
    url: "/an/nip/smartReq/" + name + "/" + code,
    method: "post",
    data: data
  });
}



// 导入数据
export function importData(data) {
  return request({
    url: "/an/nip/importData",
    method: "post",
    data: data
  });
}

// 下载数据导入模板
export function downloadTemplate(params) {
  return request({
    url: "/an/nip/downloadTemplate",
    method: "get",
    params: params
  });
}

export function downloadSmartDataImportTemplate(params) {
  return request({
    url: "/an/nip/downloadSmartDataImportTemplate",
    method: "get",
    params: params
  });
}
export function downloadValueEvalTemplate(params) {
  return request({
    url: "/an/nip/downloadValueEvalTemplate",
    method: "get",
    params: params
  });
}

export function calForecastReq(params) {
  return request({
    url: "/an/nip/calForecastReq",
    method: "get",
    params: params
  });
}

export function callTrafficForecast(params) {
  return request({
    url: "/an/nip/trafficForecast",
    method: "get",
    params: params
  });
}


