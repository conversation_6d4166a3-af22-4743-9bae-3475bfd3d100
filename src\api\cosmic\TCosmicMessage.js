import request from '@/utils/request'

// 查询千帆大模型交互记录列表
export function listTCosmicMessage(query) {
  return request({
    url: '/cosmic/message/list',
    method: 'get',
    params: query
  })
}

// 查询千帆大模型交互记录详细
export function getTCosmicMessage(id) {
  return request({
    url: '/cosmic/message/' + id,
    method: 'get'
  })
}

// 新增千帆大模型交互记录
export function addTCosmicMessage(data) {
  return request({
    url: '/cosmic/message',
    method: 'post',
    data: data
  })
}

// 修改千帆大模型交互记录
export function updateTCosmicMessage(data) {
  return request({
    url: '/cosmic/message',
    method: 'put',
    data: data
  })
}

// 删除千帆大模型交互记录
export function delTCosmicMessage(id) {
  return request({
    url: '/cosmic/message/' + id,
    method: 'delete'
  })
}