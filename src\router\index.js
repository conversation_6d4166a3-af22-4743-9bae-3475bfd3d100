import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import { getInfo } from '@/api/system/user'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    cache: true                 // 如果设置为true，会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
	{
		path: '/topology',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'example',
				component: () => import('@/views/topology/example'),
				name: 'TopologyExample',
				meta: { title: '拓扑图示例', icon: '', cache: false },
			},
		],
	},
	{
		path: '/redirect',
		component: Layout,
		hidden: true,
		children: [
			{
				path: '/redirect/:path(.*)',
				component: () => import('@/views/redirect'),
			},
		],
	},
	{
		path: '/login',
		component: () => import('@/views/login'),
		hidden: true,
	},
	{
		path: '/ln',
		component: () => import('@/views/ssoLogin'),
		hidden: true,
		meta: { cache: false },
	},
  {
    path: '/channel',
    component: () => import('@/views/channel'),
    hidden: true,
    meta: { cache: false },
  },
	{
		path: '/casLogin/:chapters*',
		component: (resolve) => require(['@/views/casLogin'], resolve),
		hidden: true,
	},
	{
		path: '/mainPage',
		component: () => import('@/views/main'),
		hidden: true,
		meta: { cache: false },
	},
	{
		path: '/coverageAnalysis',
		component: () => import('@/views/coverageAnalysis'),
		hidden: true,
  },
  {
    path: '/plan/generalSituation',
    component: () => import('@/views/plan/report/GeneralSituation'),
    hidden: false,
  },
  {
    path: '/user/grant/pri/:account',
    component: () => import('@/views/system/user/userPri'),
    hidden: false,
	},
	{
		path: '/404',
		component: () => import('@/views/error/404'),
		hidden: true,
	},
	{
		path: '/401',
		component: () => import('@/views/error/401'),
		hidden: true,
	},
	{
		path: '/bigscreen/designer',
		component: () => import('@/views/report/bigscreen/designer'),
		hidden: true,
		meta: { title: '报表设计', icon: '', cache: false },
	},
	{
		path: '/bigscreen/viewer',
		component: () => import('@/views/report/bigscreen/view'),
		hidden: true,
		meta: { title: '报表预览', icon: '', cache: false },
	},
	// 数据字典
	{
		path: '/report',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'component/mgr',
				component: () => import('@/views/report/report/component'),
				name: 'reprtComponentMgr',
				meta: { title: '报表组件管理', icon: '', cache: false },
			},
		],
	},

	{
		path: '/user',
		component: Layout,
		hidden: true,
		redirect: 'noredirect',
		children: [
			{
				path: 'profile',
				component: () => import('@/views/system/user/profile/index'),
				name: 'Profile',
				meta: { title: '个人中心', icon: 'user' },
			},
		],
	},

	// 数据字典
	{
		path: '/dict',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'type/data/:dictTypeCode',
				component: () => import('@/views/system/dict/data'),
				name: 'Data',
				meta: { title: '字典数据', icon: '', cache: true },
			},
		],
	},
	// 系统参数
	{
		path: '/system',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'config/template',
				component: () => import('@/views/system/config/configTemplate'),
				name: 'configTemplate',
				meta: { title: '参数模板', icon: '', cache: true },
			},
		],
	},
	// 通用查询预览
	{
		path: '/common',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'query/preview/:queryCode',
				component: () => import('@/views/devManager/queryConfig/preview'),
				name: 'preview',
				meta: { title: '预览', icon: '', cache: false },
			},
			{
				path: 'bigscreen/preview/:code',
				component: () => import('@/views/report/bigscreen/view'),
				name: 'bigscreenPreview',
				meta: { title: '预览', icon: '', cache: false },
			},
			{
				path: 'report/preview/:code',
				component: () => import('@/components/Report/CommonChart'),
				name: 'reportPreview',
				meta: { title: '预览', icon: '', cache: false },
			},
			{
				path: 'combineReport/preview/:code',
				component: () => import('@/components/Report/CombinationChart'),
				name: 'combChartPreview',
				meta: { title: '预览', icon: '', cache: false },
			},
      {
        path: 'devManageQueryConfigPreview/:id',
        component:  () => import('@/views/devManager/queryConfig/preview'),
        name: 'devManageQueryConfigPreview',
        beforeEnter: (to, from, next) => {
          if (to.params.id) {
			to.meta.title = to.query.tabTitle;
		  }
          next()
        },
      },
		],
	},
  // 规划任务处理
	{
		path: '/plan',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'handle/:type',
				component: () => import('@/views/plan/handle'),
				name: 'Handle',
				meta: { title: '任务处理', icon: '', cache: true },
			}
		],
	},
// 应急站流程详情
{
  path: '/emergency',
  component: Layout,
  hidden: true,
  children: [
    {
      path: 'detail/:type?', // 添加可选参数，确保路由更具体
      component: () => import('@/views/emergency/detail/index.vue'),
      name: 'EmergencyHandle', // 修改name使其更具体
      meta: { title: '任务处理', icon: '', cache: true },
      beforeEnter: (to, from, next) => {
        if (to.query.type === 'add') {
          to.meta.title = '新增方案';
        } else {
          to.meta.title = '任务处理';
        }
        next();
      }
    }
  ],
},
// 应急站流程详情
  {
    path: '/rm',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:type?', // 添加可选参数，确保路由更具体
        component: () => import('@/views/resource-manager-off-flow/index.vue'),
        name: 'EmergencyHandle', // 修改name使其更具体
        meta: { title: '任务处理', icon: '', cache: true },
        beforeEnter: (to, from, next) => {
          if (to.query.type === 'add') {
            to.meta.title = '新增方案';
          } else {
            to.meta.title = '任务处理';
          }
          next();
        }
      }
    ],
  },

	// 现网任务处理
	{
		path: '/open',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'handle/:type',
				component: () => import('@/views/open/handle'),
				name: 'OpenHandle',
				meta: { title: '任务处理', icon: '', cache: true },
			},
			// unify resource view/edit page
			{
				path: 'roomRoofMerge/handle',
				component: () => import('@/views/open/roomRoofMerge/handle'),
				hidden: true,
				name: 'RoomRoofMergeHandle',
				meta: { title: '站点信息', icon: '', cache: true },
			},
			// 搬迁任务处理
			{
				path: 'siteMove/handle/:type',
				component: () => import('@/views/open/siteMove/handle'),
				name: 'SiteMove',
				meta: { title: '任务处理', icon: '', cache: true },
			},
			// 增删调任务处理（工单详情界面）
			{
				path: 'dilatation/handle/:type',
				component: () => import('@/views/open/dilatationHandle'),
				name: 'DilatationHandle',
				meta: { title: '任务处理', icon: '', cache: true },
			},
			// 退网任务处理
			{
				path: 'offNet/handle/:type',
				component: () => import('@/views/open/offNet'),
				name: 'OffNetHandle',
				meta: { title: '任务处理', icon: '', cache: true },
			},
		],
	},

	// 现网任务处理组件单独生成路由
	{
		path: '/openHandle/:type',
		component: () => import('@/views/open/handle'),
		hidden: true,
		name: 'OpenSiteHandle',
		meta: { title: '任务处理', icon: '', cache: true },
	},
	//新增黑点
	{
		path: '/blackPoint',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'newBlackPoint',
				component: () => import('@/views/blackPoint/handle/AddBlackPoint'),
				name: 'newBlackPoint',
				meta: { title: '新增黑点', icon: '', cache: true },
			},
			{
				path: 'AddBlackPoint/:id',
				component: () => import('@/views/blackPoint/handle/AddBlackPoint'),
				name: 'AddBlackPoint',
				meta: { title: '黑点编辑', icon: '', cache: true },
			},
		],
	},
	// 流程
	{
		path: '/flow',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'editor/:type',
				component: () => import('@/views/devManager/flow/editor'),
				name: 'editor',
				meta: { title: '流程编辑', icon: '' },
			},
		],
	},

  // 流转轨迹图
  {
    path: '/history/:orderId/:nodeId',
    component: () => import('@/components/Flow/History'),
    hidden: true,
    name: 'History',
    meta: { title: '历史记录', icon: '' },
  },
  // 基站历程详情
  {
    path: '/baseStationJourney',
    component: Layout,
    hidden: true,
    children: [
      {
        path: `detail/:enodebId`,
        component: () => import('@/views/utils/baseStationJourney/detail'),
        name: 'baseStationJourneyDetail',
        meta: { title: '基站历程详情', icon: '' },
      }
    ]
  },
  // 需求导入
  {
    path: '/demand',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'import',
        component: () => import('@/views/import/plan/siteImport.vue'),
        name: 'demand',
        meta: { title: '需求导入', icon: '', cache: true },
      },
      {
        path: 'importSite',
        component: () => import('@/views/import/plan/simpleImport.vue'),
        name: 'importSite',
        meta: { title: '导入站点', icon: '', cache: true },
      },
    ],
  },
	{
		path: '/list/:code/:columns',
		name: 'ListCard',
		component: () => import('@/views/coverageAnalysis/listCard'),
		hidden: true,
	},

	{
		path: '/gen',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'edit/:tableId(\\d+)',
				component: () => import('@/views/devManager/gen/editTable'),
				name: 'GenEdit',
				meta: { title: '修改生成配置' },
			},
		],
	},
	{
		path: '/tickling',
		component: () => import('@/views/coverageAnalysis/ticklingSheetDetail'),
		hidden: true,
		name: 'TicklingSheetDetail',
		meta: { title: '区域详情' },
	},
	{
		// 价值评分体系
		path: '/score',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'rule/:type',
				component: () => import('@/views/score/rule'),
				name: 'OpenRule',
				//meta: { title: '价值评分体系', icon: '', cache: true }
			},
		],
	},
	// 价值评分体系--流程版本
	{
		path: '/vs',
		component: Layout,
		hidden: true,
		children: [
			/*{
        path: 'list',
        component: () => import('@/views/vs/list'),
        name: 'vs_list',
        //meta: { title: '工单列表', icon: '', cache: true }
      },*/
			{
				path: 'orderDetail',
				component: () => import('@/views/vs/orderDetail'),
				name: 'vs_orderDetail',
				meta: { icon: '', cache: true },
			},
			/*{
        path: 'rule',
        component: () => import('@/views/vs/rule'),
        name: 'vs_rule',
        //meta: { title: '评估规则', icon: '', cache: true }
      },*/
			{
				path: 'ruleDetail',
				component: () => import('@/views/vs/ruleDetail'),
				name: 'vs_ruleDetail',
				meta: { icon: '', cache: true },
			},
			{
				path: 'jzxq',
				component: () => import('@/views/vs/jzxq'),
				name: 'vs_jzxq',
				meta: { title: '建站需求', icon: '', cache: true },
			},
		],
	},
  {
    path: '/hvp/vs',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'orderDetail',
        component: () => import('@/views/hvp/vs/orderDetail'),
        name: 'hvp_vs_orderDetail',
        meta: { icon: '', cache: true },
      },
      {
        path: 'jzxq',
        component: () => import('@/views/hvp/vs/jzxq'),
        name: 'hvp_vs_jzxq',
        meta: { title: '建站需求', icon: '', cache: true },
      },
    ],
  },
	{
		path: '/vs/orderDetailCell',
		hidden: false,
		component: () => import('@/views/vs/orderDetail/orderDetailCell'),
		name: 'vs_orderDetailCell',
	},
	{
		path: '/vs/orderDetailStation',
		hidden: true,
		component: () => import('@/views/vs/orderDetail/orderDetailStation'),
		name: 'vs_orderDetailStation',
	},
	// 价值评分体系--流程版本
	{
		path: '/outdoor',
		component: Layout,
		hidden: true,
		children: [
			/*{
        path: 'task',
        component: () => import('@/views/outdoor/task'),
        name: 'task_list',
        //meta: { title: '评估任务', icon: '', cache: true }
      },*/
			{
				path: 'taskDetail',
				component: () => import('@/views/outdoor/taskDetail'),
				name: 'outdoor_taskDetail',
				//meta: { title: '任务详情', icon: '', cache: true }
			},
			/*{
        path: 'rule',
        component: () => import('@/views/outdoor/rule'),
        name: 'outdoor_rule',
        //meta: { title: '评估规则', icon: '', cache: true }
      },*/
			{
				path: 'render',
				component: () => import('@/views/outdoor/render'),
				name: 'outdoorRender',
				meta: { title: '渲染', icon: '', cache: true },
			},
			{
				path: 'optimize',
				component: () => import('@/views/outdoor/optimize'),
				name: 'outdoorOptimizer',
				meta: { title: '优化', icon: '', cache: true },
			},
			{
				path: 'simulation',
				component: () => import('@/views/outdoor/simulation'),
				name: 'outdoorSimulation',
				meta: { title: '仿真', icon: '', cache: true },
			},
		],
	},
	{
		path: '/indoor/examine',
		hidden: true,
		component: () => import('@/views/indoor'),
		name: 'indoor',
	},
	{
		path: '/indoor',
		component: Layout,
		children: [
			{
				path: 'detail',
				hidden: true,
				component: () => import('@/views/indoor/detail'),
				name: 'indoorOrderDetail',
			},
			{
				path: '/indoor/nip_history',
				hidden: true,
				component: () => import('@/views/indoor/nip_history'),
				name: 'nip_history',
			},
			{
				path: 'report',
				hidden: true,
				component: () => import('@/views/indoor/report'),
				name: 'indoorOrderReport',
			},
			{
				path: 'config',
				hidden: true,
				component: () => import('@/views/indoor/config'),
				name: 'indoorConfig',
				meta: { title: '参数配置', icon: '', cache: true },
			},
			{
				path: 'distinguishInfo',
				hidden: true,
				component: () => import('@/views/indoor/distinguish/info'),
				name: 'distinguishInfo',
			},
			{
				path: 'distinguishResult',
				hidden: true,
				component: () => import('@/views/indoor/distinguish/result'),
				name: 'distinguishResult',
			},
		],
		name: 'indoorDetail',
	},
	{
		path: '/cov/rule_',
		hidden: true,
		component: () => import('@/views/vs/rule'),
		name: 'cov_rule_',
	},
	{
		// 覆盖分析新逻辑
		path: '/cov',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'icon',
				component: () => import('@/views/cov/icon'),
				name: 'cov_icon',
				meta: { title: 'GIS图标管理', icon: '', cache: true },
			},
			{
				path: 'style',
				component: () => import('@/views/cov/style'),
				name: 'cov_style',
				meta: { title: 'GIS样式管理', icon: '', cache: true },
			},
			{
				path: 'building/dwg',
				component: () => import('@/views/cov/building/dwg'),
				name: 'cov_building_dwg',
				meta: { title: '图纸管理', icon: '', cache: true },
			},
			{
				path: 'building/dwgMatch',
				component: () => import('@/views/cov/building/dwgMatch'),
				name: 'cov_building_dwgMatch',
				meta: { title: '图纸匹配', icon: '', cache: true },
			},
		],
	},

	// curd detail
	{
		path: '/curd',
		component: Layout,
		name: 'CURD',
		children: [
			{
				path: 'tagviewEdit',
				component: () => import('@/components/CommonCurd/tagviewEdit'),
				name: 'CURDHandle',
				hidden: true,
				meta: { title: '详情', icon: '' },
			},
		],
	},
	{
		// 覆盖分析AN
		path: '/cov_an',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'perRuleConfig',
				component: () => import('@/views/cov_an/perRuleConfig'),
				name: 'perRuleConfig',
				meta: { title: '规则配置', icon: '', cache: true },
			},
			{
				path: 'perRuleRunLogs',
				component: () => import('@/views/cov_an/perRuleRunLogs'),
				name: 'perRuleRunLogs',
				meta: { title: '规则调度', icon: '', cache: true },
			},
			{
				path: 'perProblemList',
				component: () => import('@/views/cov_an/perProblemList'),
				name: 'perProblemList',
				meta: { title: '规则问题', icon: '', cache: true },
			},
			{
				path: 'perAnalysisStep',
				component: () => import('@/views/cov_an/perAnalysisStep'),
				name: 'perAnalysisStep',
				meta: { title: '规则分类', icon: '', cache: true },
			},
			{
				path: 'perAnalysisScheme',
				component: () => import('@/views/cov_an/perAnalysisScheme'),
				name: 'perAnalysisScheme',
				meta: { title: '分析方案', icon: '', cache: true },
			},
			{
				path: 'perAnalysisResult',
				component: () => import('@/views/cov_an/perAnalysisResult'),
				name: 'perAnalysisResult',
				meta: { title: '分析结果', icon: '', cache: true },
			},
			{
				path: 'perAnalysisDispatch',
				component: () => import('@/views/cov_an/perAnalysisDispatch'),
				name: 'perAnalysisDispatch',
				meta: { title: '方案调度', icon: '', cache: true },
			},
			{
				path: 'perCfgTables',
				component: () => import('@/views/cov_an/perCfgTables'),
				name: 'perCfgTables',
				meta: { title: '数据源配置', icon: '', cache: true },
			},
			{
				path: 'perSchemeConfig',
				component: () => import('@/views/cov_an/perSchemeConfig'),
				name: 'perSchemeConfig',
				meta: { title: '方案配置', icon: '', cache: true },
			},
			{
				path: 'perOptimizeScheme',
				component: () => import('@/views/cov_an/perOptimizeScheme'),
				name: 'perOptimizeScheme',
				meta: { title: '优化方案', icon: '', cache: true },
			},
		],
	},
	{
		// AI多模态
		path: '/ai',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'multi_model/video',
				component: () => import('@/views/ai/multi_model/video'),
				name: 'multi_model_video',
				meta: { title: '视频任务列表', icon: '', cache: true },
			},
			{
				path: 'multi_model/videoAnalysis',
				component: () => import('@/views/ai/multi_model/videoAnalysis'),
				name: 'multi_model_videoAnalysis',
				meta: { title: '视频解析结果', icon: '', cache: true },
			},
		],
	},
	// 入网交维任务处理
	{
		path: '/deliver',
		component: Layout,
		hidden: true,
		children: [
			{
				path: 'handle/:type',
				component: () => import('@/views/deliver/handle'),
				name: 'DeliverHandle',
				meta: { title: '任务处理', icon: '', cache: true },
			},
		],
	},
	{
    path: '/import',
    component: Layout,
    name: 'SJDR',
    children: [
      {
        path: 'grid',
        component: () =>
          import('@/views/import/grid'),
        name: 'SJDRHandler',
        hidden: true,
        meta: { title: '数据导入', icon: '' },
      },
    ],
  },
  {
	  // IDS
	  path: '/ids',
	  component: Layout,
	  hidden: true,
	  children: [
		  {
			  path: 'graph',
			  component: () => import('@/views/ids/graph'),
			  name: 'ids_graph',
			  meta: { title: '干扰走势图', icon: '', cache: true },
		  },
		]
	},

  // 云南智能规划详情
  {
    path: '/intelligent/plan',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'handle/:type',
        component: () => import('@/views/provinces/yn/intelligentPlan/handle'),
        name: 'IntelligentHandle',
        meta: { title: '', icon: '', cache: true },
      },
    ],
  },
  {
    path: '/yn/demand',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'task',
        component: () => import('@/views/provinces/yn/demand/taskArrangement'),
        name: 'task',
        meta: { title: '任务', icon: '', cache: true },
      },
    ],
  },
  {
    path: '/cosmic',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'project',
        component: () => import('@/views/cosmic/TCosmicProject/index'),
        name: 'project',
        meta: { title: '项目列表', icon: '', cache: true },
      },
      {
        path: 'projectDetail',
        component: () => import('@/views/cosmic/TCosmicProject/detail'),
        name: 'projectDetail',
        meta: { title: '项目详情', icon: '', cache: true },
      },
    ],
  },



]

if (casConfig.VUE_APP_CAS_LOGOUT_URL) {
  // 支持从外系统跳转到指定路由
  const originalPush = Router.prototype.push
  Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject)
      return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch((err) => err)
  }
}

const router = {
  mode: process.env.VUE_APP_CONTEXT_PATH == './' ? 'hash' : 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
}

if (process.env.VUE_APP_CONTEXT_PATH != './') {
  router.base = process.env.BASE_URL
}

export default new Router(router)
