import request from '@/utils/request'

// 查询字段配置列表
export function listFields(query) {
  return request({
    url: '/hvpVs/score/fields/list',
    method: 'get',
    params: query
  })
}

// 查询字段配置详细
export function getFields(id) {
  return request({
    url: '/hvpVs/score/fields/' + id,
    method: 'get'
  })
}

// 新增字段配置
export function addFields(data) {
  return request({
    url: '/hvpVs/score/fields',
    method: 'post',
    data: data
  })
}

// 修改字段配置
export function updateFields(data) {
  return request({
    url: '/hvpVs/score/fields',
    method: 'put',
    data: data
  })
}

// 删除字段配置
export function delFields(id) {
  return request({
    url: '/hvpVs/score/fields/' + id,
    method: 'delete'
  })
}
