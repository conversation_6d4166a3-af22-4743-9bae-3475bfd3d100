import { useCommonQueryData } from '@/utils/useDictionary'
import { startFlow } from '@/api/site/open/dilatation'

import BasicForm from "@/components/BasicComponents/Form/index";
import Vue from "vue";

const config = {
	operat: ['showDetail', 'start'],
	formValue: {},
	operatConfig: {
		showDetail: {
			icon: 'el-icon-view',
			title: '详情',
			event: (row, vm) => vm.tableViews(row),
		},
	},
	process: [],
	async init() {
		const { chooseResource, resourceType } = this.$route.query
		this.resourceType = resourceType
		this.query.chooseResource = chooseResource
		this.query.nodeId = this.sysconfig.NewSiteEnodeBListIds
		this.query.status = 'ALL'
		this.process = await useCommonQueryData('ADA_TYPE')
		Vue.component("BasicForm", BasicForm);
	},
	tableViews(row) {
		const query = {
			type: 'view',
			sourceType: 'open',
			businessType: 'adjustCapacity',
			id: row?.id,
			status: this.query.status,
			orderNo: row?.order_no,
			nodeId: this.$route.query.nodeId,
			newSite: this.$route.query.newSite,
			nodeName: row?.node_name,
			taskId: row?.task_id,
			formInsId: row?.form_ins_id,
			siteIdCmcc: row?.site_id_cmcc,
			xqSiteIdCmcc: row?.xq_site_id_cmcc,
			tagsViewTitle: row?.site_id_cmcc,
			resourceType: row?.resource_type,
		}

		this.$router.push({
			path: `/open/dilatation/handle/view`,
			query,
		})
	},
}
export default config
