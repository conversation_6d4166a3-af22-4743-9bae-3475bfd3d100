import * as Cesium from "cesium";
import { drawPoint, drawPolygon, drawShape, zoomRectangle } from '@/utils/gis/tools/common'
import WKT from "terraformer-wkt-parser";
import { ActiveState } from '@/utils/gis/editor'

export const PrimitiveType = {
  Entity: -1,
  Vertex: 1,
  CenterPoint: 2,
  Midpoint: 3
}

export class BasicMode {

  constructor(editor) {
    this.ellipsoid = editor.viewer.scene.globe.ellipsoid;
    this.editor = editor
  }

  init() {
    this.editMap = new Map();
    this.entityPoints = []
    this.lineWidth = 200
    this.editVertext = null
    this.editEntity = null
  }

  load(origin) {
    this.clear()
    if (origin) this._loadWkt(origin)
  }

  width(width = 200) {
    this.lineWidth = width
  }

  clear() {
    this.editor.dataSource?.entities.removeAll()
    this._clearVertex()
    this._clearMidpoints()
    this.init()
  }

  onLeftClick(event) {}

  onLeftDown(event) {}

  onLeftUp(event) {
    if (this.editor.actived === ActiveState.EDIT && this.isEditing) {
      const wkt = this._formatWkt()
      this.editor.origin = wkt
      wkt && this.editor.bus.$emit('format', wkt)
      this.isEditing = false
    }
  }

  onLeftDoubleClick(event) {
    if (this.editor.actived === ActiveState.ADD) {
      this.editor.bus.$emit('format', this.editor.origin)
      this.editor.actived = ActiveState.EDIT
      this.isEditing = false
    }
  }

  onRightClick(event) {
    if (this.editor.actived === ActiveState.ADD) {
      const wkt = this._formatWkt()
      // 激活编辑
      const nWkt = this._union(wkt)
      // this.load(nWkt)
      this.editor.origin = nWkt
      nWkt && this.editor.bus.$emit('format', nWkt)
      this.editor.actived = ActiveState.EDIT
      this.isEditing = false
    }
    else if (this.editor.actived === ActiveState.EDIT && this.isEditing) {
      const wkt = this._formatWkt()
      this.editor.origin = wkt
      wkt && this.editor.bus.$emit('format', wkt)
      this.isEditing = false
    }
  }

  onMouseMove(event) {}

  _union(wkt) {}

  _delete(entity) {
    const index = entity.index
    this.editor.dataSource.entities.remove(this.editEntity.datasources[index])
    this.editEntity.datasources.splice(index, 1)

    this._pickEntity()
    this.isEditing = true
    const wkt = this._formatWkt()
    this.editor.origin = wkt
    wkt && this.editor.bus.$emit('format', wkt)
  }

  _reloadDatasource(i) {
    const { isMulti, identify, index, type, positions, holes } =
      this.editEntity.datasources[i]
    let nHoles = isMulti
      ? holes.map((hole) => new Cesium.PolygonHierarchy(hole))
      : []
    let points = new Cesium.CallbackProperty(() => {
      return new Cesium.PolygonHierarchy(positions, nHoles)
    }, false)
    const datasource = this.editor.dataSource.entities.add(drawPolygon(points))
    datasource.identify = identify
    datasource.index = index
    datasource.type = type
    datasource.positions = positions
    datasource.holes = holes
    datasource.isMulti = isMulti
    this.editEntity.datasources[i] = datasource
  }

  _loadWkt({id, geom, row, label}) {
    this.editor.origin = geom
    const geometry = WKT.parse(geom);
    const datasources = []
    if (geometry.type === 'LineString') {
      let datasource = this._formatPolyline(geometry.coordinates)
      datasource.identify = id
      datasource.index = 0
      datasource.type = PrimitiveType.Entity
      datasource.isMulti = false
      datasource.canEdit = true;
      (this.editor.disabled || !this.editor.edit) && (datasource.info = label)
      datasource.currRow = row
      datasources.push(datasource)
    }
    else if (geometry.type === 'Point') {
      let datasource = this._formatPoint(geometry.coordinates)
      datasource.identify = id
      datasource.index = 0
      datasource.type = PrimitiveType.Entity
      datasource.isMulti = false
      datasource.canEdit = true;
      (this.editor.disabled || !this.editor.edit) && (datasource.info = label)
      datasource.currRow = row
      datasources.push(datasource)
    }
    else
      geometry.coordinates.forEach((coordinate, i) => {
        let datasource;
        if (geometry.type === 'MultiPolygon') {
          datasource = this._formatPolygon(coordinate)
          datasource.identify = id
          datasource.index = i
          datasource.type = PrimitiveType.Entity
          datasource.isMulti = true
        } else if (geometry.type === 'Polygon') {
          datasource = this._formatPolygon([coordinate])
          datasource.identify = id
          datasource.index = i
          datasource.type = PrimitiveType.Entity
          datasource.isMulti = false
        }
        else if (geometry.type === 'MultiLineString') {
          datasource = this._formatPolyline(coordinate)
          datasource.identify = id
          datasource.index = i
          datasource.type = PrimitiveType.Entity
          datasource.isMulti = true
          datasource.canEdit = true
        }
        else
        if (geometry.type === 'MultiPoint') {
          datasource = this._formatPoint(coordinate)
          datasource.identify = id
          datasource.index = i
          datasource.type = PrimitiveType.Entity
          datasource.isMulti = true
        }
        if (datasource) {
          (this.editor.disabled || !this.editor.edit) && (datasource.info = label)
          datasource.currRow = row
          datasources.push(datasource)
        }
      })

    if (!datasources.length) return

    const obj = {
      id,
      geom,
      datasources
    }
    this.editMap.set(id, obj)
    this.editor.zoom && this._zoomTo(id)

    if (!this.editor.disabled && this.editor.edit) {
      this.editEntity = this.editMap.get(id)
      this.isEditing = false
      this._pickEntity()
      // this._highlightEntity(id)
      this.editor.actived = ActiveState.EDIT
    }
  }

  _formatWkt() {}

  _pickEntity() {
    this._clearVertex()
    this._clearMidpoints()
    this._createVertex();
    this._createMidpoints();
  }

  _getCartesian3(position) {
    let ray = this.editor.viewer.camera.getPickRay(position)
    let cartesian = this.editor.viewer.scene.globe.pick(ray, this.editor.viewer.scene)
    if (Cesium.defined(cartesian)) {
      return cartesian;
    }
    return null
  }

  //获取编辑对象中心点
  _getCenterPosition(positions = []) {
    return Cesium.BoundingSphere.fromPoints(positions).center;
  }

  _formatPolygon(coordinate = []) {
    let positions = this._filterNextEqualsEpsilon(
        Cesium.Cartesian3.fromDegreesArray(coordinate.shift().reduce((a, b) => [...a, ...b], [])))
    if (this._filterEqualsEpsilon(positions).length < 3) return null;
    let canEdit
    let holes = []
    if (coordinate.length) {
      holes = coordinate ? coordinate.map(hole =>
          new Cesium.PolygonHierarchy(this._filterNextEqualsEpsilon(
              Cesium.Cartesian3.fromDegreesArray(hole.reduce((a, b) => [...a, ...b], []))))
      ) : []
      canEdit = (positions.length + holes.length) < 1000
    } else {
      canEdit = positions.length < 1000
    }
    // !canEdit && Message.error('此区域点位过多，无法编辑')
    let points = canEdit ? new Cesium.CallbackProperty(() => {
      return new Cesium.PolygonHierarchy(positions, holes)
    }, false) : new Cesium.PolygonHierarchy(positions, holes)
    let datasource = this.editor.dataSource.entities.add(drawPolygon(points, Cesium.Color.PURPLE, 0.5))
    datasource.positions = positions
    datasource.holes = holes.map(hole => hole.positions)
    datasource.canEdit = canEdit
    return datasource
  }

  _formatPolyline(coordinate = []) {
    let positions = this._filterNextEqualsEpsilon(
        Cesium.Cartesian3.fromDegreesArrayHeights(coordinate.reduce((a, b) => [...a, ...b, 0.5], [])))
    let points = new Cesium.CallbackProperty(() => positions, false)
    let datasource = this.editor.dataSource.entities.add(drawShape(points))
    datasource.positions = positions
    return datasource
  }

  _formatPoint(coordinate = []) {
    let position = Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
    this.entityPoints.push(position)
    let point = new Cesium.CallbackProperty(() => position, false)
    let datasource = this.editor.dataSource.entities.add(drawPoint(point))
    let dis = 2500 / 6378137
    datasource.positions = [
      Cesium.Cartesian3.fromDegrees(coordinate[0] - dis, coordinate[1] - dis),
      Cesium.Cartesian3.fromDegrees(coordinate[0] + dis, coordinate[1] + dis),
    ]
    datasource.currPoint = position
    return datasource
  }

  _zoomTo(id) {
    if (this.editMap.has(id)) {
      const editEntity = this.editMap.get(id)
      const positions = editEntity.datasources.reduce((a, b) => [...a, ...b.positions], [])
      if (positions && positions.length) {
        // 创建矩形实例
        const rectangle = Cesium.Rectangle.fromCartesianArray(positions);
        // 将视角定位到矩形范围
        setTimeout(() => this.editor.viewer.camera.setView({
          destination: zoomRectangle(rectangle, 1.5)
        }), 1000);
      }
      return true
    }
    return false
  }

  _highlightEntity(id) {
    if (this.provCheckId){
      const datasources = this.editMap.get(this.provCheckId)?.datasources;
      datasources.forEach(datasource => {
        if (datasource.polygon)
          datasource.polygon.material = Cesium.Color.PURPLE.withAlpha(0.5)
        else if (datasource.polyline)
          datasource.polyline.material = Cesium.Color.PURPLE.withAlpha(0.5)
      })
      this.provCheckId = null
    }
    if (this.editMap.has(id)) {
      this.provCheckId = id
      const datasources = this.editMap.get(id)?.datasources;
      datasources.forEach(datasource => {
        if (datasource.polygon)
          datasource.polygon.material = Cesium.Color.BLUEVIOLET.withAlpha(0.6)
        else if (datasource.polyline)
          datasource.polyline.material = Cesium.Color.BLUEVIOLET.withAlpha(0.6)
      })
    }
  }

  //根据偏移量移动实体
  _moveEntityByOffset(startPosition, endPosition) {
    let subtract = Cesium.Cartesian3.subtract(
      startPosition,
      endPosition,
      new Cesium.Cartesian3()
    )
    this.editEntity.datasources[this.editVertext.vertexIndex].positions.forEach(
      (point, i) => {
        this.editEntity.datasources[this.editVertext.vertexIndex].positions[i] =
          Cesium.Cartesian3.subtract(point, subtract, new Cesium.Cartesian3())
      }
    )
    this.editEntity.datasources[this.editVertext.vertexIndex].holes?.forEach(
      (hole) => {
        hole.forEach((point, i) => {
          hole[i] = Cesium.Cartesian3.subtract(
            point,
            subtract,
            new Cesium.Cartesian3()
          )
        })
      }
    )
  }

  _filterEqualsEpsilon(list = []) {
    const seen = new Set();

    return list.filter(cartesian => {
      const key = `${cartesian.x},${cartesian.y},${cartesian.z}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  _filterNextEqualsEpsilon(list = [], close = true) {
    return list.filter((item, index, array) => {
      if (index === 0) return true;
      // 最后一个点判断与第一个点是否相等
      if (close && index === array.length - 1
          && Cesium.Cartesian3.equalsEpsilon(item, array[0], 0.000003)) {
        return false
      }
      return !Cesium.Cartesian3.equalsEpsilon(item, array[index - 1], 0.000003)
    })
  }

  _drawPolyline(close = false) {
    let points = new Cesium.CallbackProperty(() =>
      close ? [...this.entityPoints, this.entityPoints[0]] : this.entityPoints, false)
    return this.editor.dataSource.entities.add(drawShape(points, Cesium.Color.PURPLE))
  }

  _drawPolygon() {
    let points = new Cesium.CallbackProperty(() => {
      return new Cesium.PolygonHierarchy(this.entityPoints)
    }, false)

    return this.editor.dataSource.entities.add(drawPolygon(points))
  }

  _clearVertex() {
    if (!this.vertexEntities) {
      this.vertexEntities = new Cesium.PointPrimitiveCollection();
      this.editor.primitives.add(this.vertexEntities)
    }
    this.vertexEntities.removeAll()
  }

  _clearMidpoints() {
    if (!this.midpointEntities) {
      this.midpointEntities = new Cesium.PointPrimitiveCollection();
      this.editor.primitives.add(this.midpointEntities)
    }
    this.midpointEntities.removeAll()
  }

  _createVertex() {
    this.vertexCenterPositions = []
    this.editEntity.datasources.filter(datasource => datasource.canEdit).forEach((datasource, i) => {
      datasource.positions.forEach((p, index) => {
        const editPoint = this._creteVertex(datasource.positions[index]);
        editPoint.isHole = false
        editPoint.vertexIndex = [i, index] //节点索引
      });

      // holes
      datasource.holes && datasource.holes.length && datasource.holes.forEach((hole, hi) => {
        hole.forEach((p, index) => {
          const editPoint = this._creteVertex(hole[index]);
          editPoint.isHole = true
          editPoint.vertexIndex = [i, hi, index] //节点索引
        })
      })

      this.vertexCenterPositions.push(this._getCenterPosition(datasource.positions))
      // 创建中心点
      const centerPoint = this._createCenterPoint(this.vertexCenterPositions[i])
      centerPoint.isHole = false
      centerPoint.vertexIndex = i
    })

  }

  _createMidpoints() {
    this.editEntity.datasources.filter(datasource => datasource.canEdit).forEach((datasource, i) => {
      const length = datasource.positions.length - 1
      datasource.positions.forEach((p, index) => {
        const position = Cesium.Cartesian3.midpoint(datasource.positions[index],
          datasource.positions[index === length ? 0 : (index + 1)],
          new Cesium.Cartesian3())
        const midPoint = this._createMidpoint(position);
        midPoint.isHole = false
        midPoint.vertexIndex = [i, index] //节点索引
      });

      // holes
      datasource.holes && datasource.holes.length && datasource.holes.forEach((hole,hi) => {
        const length = hole.length - 1
        hole.forEach((p, index) => {
          const position = Cesium.Cartesian3.midpoint(hole[index],
            hole[index === length ? 0 : (index + 1)],
            new Cesium.Cartesian3())
          const midPoint = this._createMidpoint(position, index);
          midPoint.isHole = true
          midPoint.vertexIndex = [i, hi, index] //节点索引
        })
      })
    });
  }

  _createCenterPoint(position) {
    const point = this.vertexEntities.add({
      position: new Cesium.Cartesian3.add(position, new Cesium.Cartesian3(0, 0, 1.3), new Cesium.Cartesian3()),
      color: Cesium.Color.ORANGERED.withAlpha(0.6),
      pixelSize: 5,
      outlineColor: Cesium.Color.YELLOW.withAlpha(0.6),
      outlineWidth: 1,
      scaleByDistance: new Cesium.NearFarScalar(
        1000, 2.0, // 当距离 <=1000 米时，放大到 2 倍（即 10*2=20px）
        10000, 1.5  // 当距离 >=10000 米时，缩小到 0.5 倍（即 10*0.5=5px）
      ),
    })
    point.type = PrimitiveType.CenterPoint
    return point;
  }

  _creteVertex(position) {
    const point = this.vertexEntities.add({
      position: new Cesium.Cartesian3.add(position, new Cesium.Cartesian3(0, 0, 1.3), new Cesium.Cartesian3()),
      color: Cesium.Color.DARKBLUE.withAlpha(0.6),
      pixelSize: 5,
      outlineColor: Cesium.Color.YELLOW.withAlpha(0.6),
      outlineWidth: 1,
      scaleByDistance: new Cesium.NearFarScalar(
        1000, 2.0, // 当距离 <=1000 米时，放大到 2 倍（即 10*2=20px）
        10000, 1.5  // 当距离 >=10000 米时，缩小到 0.5 倍（即 10*0.5=5px）
      ),
    })
    point.type = PrimitiveType.Vertex
    return point;
  }

  _createMidpoint(position) {
    const point = this.midpointEntities.add({
      position: new Cesium.Cartesian3.add(position, new Cesium.Cartesian3(0, 0, 1.3), new Cesium.Cartesian3()),
      color: Cesium.Color.PALEGREEN.withAlpha(0.6),
      pixelSize: 6,
      scaleByDistance: new Cesium.NearFarScalar(
        1000, 2.0, // 当距离 <=1000 米时，放大到 2 倍（即 10*2=20px）
        10000, 1.5 // 当距离 >=10000 米时，缩小到 0.5 倍（即 10*0.5=5px）
      ),
    })
    point.type = PrimitiveType.Midpoint
    return point
  }
}
