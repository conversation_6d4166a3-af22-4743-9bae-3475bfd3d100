import request from '@/utils/request'

// 查询视频分析结果列表
export function listVideoAnalysis(query) {
  return request({
    url: '/ai/multi_model/videoAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询视频分析结果详细
export function getVideoAnalysis(id) {
  return request({
    url: '/ai/multi_model/videoAnalysis/' + id,
    method: 'get'
  })
}

// 新增视频分析结果
export function addVideoAnalysis(data) {
  return request({
    url: '/ai/multi_model/videoAnalysis',
    method: 'post',
    data: data
  })
}

// 修改视频分析结果
export function updateVideoAnalysis(data) {
  return request({
    url: '/ai/multi_model/videoAnalysis',
    method: 'put',
    data: data
  })
}

// 删除视频分析结果
export function delVideoAnalysis(id) {
  return request({
    url: '/ai/multi_model/videoAnalysis/' + id,
    method: 'delete'
  })
}