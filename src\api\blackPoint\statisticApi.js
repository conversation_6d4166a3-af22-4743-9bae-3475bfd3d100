import axios from "@/utils/request";

/**
 * 统计
 */
class statisticApi {
  get base() {
    return "/blackspot/statistic";
  }

  // 统计数据
  basicCount(data) {
    return axios.post(`${this.base}/basicCount`, data);
  }
  // 地图黑点统计数据
  mapStatics(data) {
    return axios.post(`${this.base}/mapStatics`, data);
  }
  // 地图投诉统计数据
  mapStaticsComp(data) {
    return axios.post(`${this.base}/mapStaticsComp`, data);
  }
  // 黑点统计数据
  blackStatics(data) {
    return axios.post(`${this.base}/blackStatics`, data);
  }
  // 黑点同比统计数据
  blackStaticsMonth(data) {
    return axios.post(`${this.base}/blackStaticsMonth`, data);
  }
  // 投诉统计数据
  complainStatics(data) {
    return axios.post(`${this.base}/complainStatics`, data);
  }
  // 投诉同比统计数据
  complainCurMonth(data) {
    return axios.post(`${this.base}/complainCurMonth`, data);
  }
  // 投诉同比统计数据
  longTimeBlack(data) {
    return axios.post(`${this.base}/longTimeBlack`, data);
  }

  //黑点遗留，黑点新增 柱状图
  provinceBlackStatic(data) {
    return axios.post(`${this.base}/provinceBlackStatic`, data);
  }
  //黑点解决率
  provinceSolveRate(data) {
    return axios.post(`${this.base}/provinceSolveRate`, data);
  }
  //黑点解决手段
  blackSolution(data) {
    return axios.post(`${this.base}/blackSolution`, data);
  }
  //黑点场景
  blackScene(data) {
    return axios.post(`${this.base}/blackScene`, data);
  }
}

export default new statisticApi();
