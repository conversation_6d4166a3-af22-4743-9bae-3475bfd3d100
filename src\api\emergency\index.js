import request from '@/utils/request'

export function submitSiteAndStartFlow(data) {
  return request({
    url: '/site/plan/emergencyFlow/startFlow',
    method: 'post',
    data: data
  })
}

export function getSiteInfo(orderNo,nodeId) {
  return request({
    url: '/site/plan/emergencyFlow/getSiteInfo/' + orderNo + '/' + nodeId,
    method: 'get'
  })
}

export function completeTask(data) {
  return request({
    url: '/site/plan/emergencyFlow/taskComplete',
    method: 'post',
    data
  })
}
