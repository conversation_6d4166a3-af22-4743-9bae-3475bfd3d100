import request from '@/utils/request'

// 查询问题分析及优化方案配置详情列表
export function listConfigDetail(query) {
  return request({
    url: '/an/schemeConfigDetail/list',
    method: 'get',
    params: query
  })
}

// 查询问题分析及优化方案配置详情详细
export function getConfigDetail(id) {
  return request({
    url: '/an/schemeConfigDetail/' + id,
    method: 'get'
  })
}

// 新增问题分析及优化方案配置详情
export function addConfigDetail(data) {
  return request({
    url: '/an/schemeConfigDetail',
    method: 'post',
    data: data
  })
}

// 修改问题分析及优化方案配置详情
export function updateConfigDetail(data) {
  return request({
    url: '/an/schemeConfigDetail',
    method: 'put',
    data: data
  })
}

// 删除问题分析及优化方案配置详情
export function delConfigDetail(id) {
  return request({
    url: '/an/schemeConfigDetail/' + id,
    method: 'delete'
  })
}
