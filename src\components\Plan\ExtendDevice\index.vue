<template>
    <el-collapse v-model='extendDeviceCollapse'>
      <el-collapse-item name='extendDevice' title='扩展设备' v-if='getFields'>
        <div :class='{"glossy": getExtendDeviceList.length < 1}'>
        <EditTab
          :badge='badge'
          :editable="true"
          :first-tab-removable="true"
          :index.sync='extendDeviceIndex'
          :tabs='tabList'
          @addTab='addTabs($event, "extend_device")'
          @changeTabIndex='switchTab($event)'
          @removeTab='removeTabs($event, "extend_device")'
          ref='extendDeviceTab'
          :tab-title-name-prefix="tabTitleNamePrefix"
          tabLabelTitle='扩展设备'
          :groupMark='groupMark'
        />

        <BasicForm
          :showChangeToolTip='showChangeFormToolTip'
          :fields='getFields("extend_device")'
          :formValue='getCurrentExtendDevice'
          :groupColumn='4'
          @fieldCascading='handleFormCascading'
          @returnFormValue='setData($event, "extend_device")'
          @selectChanged="selectChanged"
          ref='form'
          size='small'
          :groupMark="groupMark"
        />
        <el-button
          @click="addTabs($event, 'extend_device')"
          class='add-button'
          type='primary'
          v-if="hasShowAddBtn"
        >添加扩展设备</el-button>
      </div>
    </el-collapse-item>
  </el-collapse>

</template>

<script>
import { deepClone } from '@/utils'
import BasicForm from '@/components/BasicComponents/Form'
import EditTab from '@/components/BasicComponents/EditTab'
import { useExpendAddTab } from '@/utils/plan/useBusiness'
import { clearResourceCode_ } from '@/utils/plan/handle'
export default {
  name: 'extendDevice',
  props: {
    editable: Boolean,
    formDisabled: Boolean,
    fields: Array,
    tabList: {
      type: Array,
      default: () => [{}],
    },
    mark : String,
    badge: Boolean,
    showChangeFormToolTip: Boolean,
    groupMark:{type:String, default:'extendDevice'},//父组件必须传入groupMark，因为一个页面会有多个扩展设备的情况
    tabTitleNamePrefix: {
      type:String,
      default: '',
    },
  },
  data() {
    return {
      getExtendDeviceListIsEmpty: true,
      extendDeviceIndex: '0',
      latticeIndex: '0',
      boardIndex: '0',
      portIndex: '0',
      extendDeviceCollapse: ['lattice'],
    }
  },
  computed: {
    // 需求库、规划库、设计库显示新增按钮，其它节点不显示.
    hasShowAddBtn(){
      const nodeId = this.$route.query?.nodeId;
      const isShow = (this.getExtendDeviceList.length < 1) && (nodeId && ['jta54ce3bf238a44eab1c6a8ac88a33804','jt3b453adde06a4772b465c4d6fb1ccc4e','jt3ca48544a50e43ae9dfec174d28b6447'].includes(nodeId));
      return  isShow;
    },
    // 使用计算属性可以缓存数据 避免切换Collapse时重复计算数据 浪费资源
    getExtendDeviceList() {
      return this.tabList || []
    },
    getCurrentExtendDevice() {
      return this.getExtendDeviceList && this.getExtendDeviceList[this.extendDeviceIndex]
    },


  },
  mounted() {
    this.getExtendDeviceListIsEmpty = this.tabList.length < 1
  },
  methods: {
    selectChanged(data) {
      this.$emit('selectChanged', data, this.mark)
    },
    setFormValue(data) {
      if (data && Object.keys(data).length) {
        this.$refs.form.setFormValue(data)
      }
    },
    handleFormCascading(field, value, autoTrigger) {
      this.$emit('handleFormCascading', field, value, autoTrigger,this.mark,this.groupMark)
    },
    getFields() {
      return this.fields
    },
    setData(data, code) {
      const currentData =this.getCurrentExtendDevice
      currentData && Object.assign(currentData, data)
    },
    firstCodeToUpperCase(code) {
      return code.charAt(0).toUpperCase() + code.slice(1)
    },
    addTabs($event, code) {
      if (this.tabList.length < 1) {
        const obj = {}
        this.getFields(code).forEach(f => obj[f.name] = '')
        obj.sort = 0
        useExpendAddTab(obj)
        obj.formType= ''
        this.tabList.push(obj)
        this.getExtendDeviceListIsEmpty = false
      } else {
        const tabData = deepClone(this.tabList[0],  false, true)
        tabData.sort = (this.tabList[this.tabList.length - 1].sort ?? 0) + 1
        this.getExtendDeviceListIsEmpty = false
        useExpendAddTab(tabData)
        tabData.formType= ''
        //新建时清空综资标识
        clearResourceCode_(tabData);
        this.tabList.push(tabData);
      }
    },
    removeTabs($event, code) {
      this.tabList.splice($event, 1)
      if (this.tabList.length < 1) {
        this.getExtendDeviceListIsEmpty = true
      }
    },
    //扩展设备切换时触发
    switchTab(index, mark) {
      console.log('index:',index,this.groupMark);
      this.$emit('switchTab',this.groupMark)
    },
  },
  components: { EditTab, BasicForm },
}
</script>

<style lang='scss' scoped>
::v-deep {
  form .el-form-item:last-child {
    display: none;
  }
  .edit-tab {
    padding-bottom: 10px;
  }
}
// 毛玻璃特效
.glossy {
  position: relative;
  .add-button {
    position: absolute;
    top: 45%;
    left: 45%;
    z-index: 3;
    display: block;
  }
  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.7);
    z-index: 2;
  }
}
</style>
