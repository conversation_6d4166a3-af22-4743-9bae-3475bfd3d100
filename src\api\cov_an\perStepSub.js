import request from '@/utils/request'

// 查询分析-步骤-子步骤列表
export function listSub(query) {
  return request({
    url: '/an/subStep/list',
    method: 'get',
    params: query
  })
}

// 查询分析-步骤-子步骤详细
export function getSub(id) {
  return request({
    url: '/an/subStep/' + id,
    method: 'get'
  })
}

// 新增分析-步骤-子步骤
export function addSub(data) {
  return request({
    url: '/an/subStep',
    method: 'post',
    data: data
  })
}

// 修改分析-步骤-子步骤
export function updateSub(data) {
  return request({
    url: '/an/subStep',
    method: 'put',
    data: data
  })
}

// 删除分析-步骤-子步骤
export function delSub(id) {
  return request({
    url: '/an/subStep/' + id,
    method: 'delete'
  })
}
export function getParentStep(id) {
  return request({
    url: '/an/subStep/getParentStep/',
    method: 'get'
  })
}
export function getSubSteps(parentId) {
  return request({
    url: '/an/subStep/getSubSteps/' + parentId,
    method: 'get'
  })
}
