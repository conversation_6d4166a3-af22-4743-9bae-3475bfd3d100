<template>
  <div class="preview" v-loading="pageLoading">
    <template v-if="!hiddenHeader">
      <basic-form
        :fields="searchFields"
        :formValue="query"
        :labelWidth="labelWidth"
        @returnFormValue="getQuery"
        class="search-area"
        ref="basicForm"
        size="small"
        v-show="searchFields.length"
      ></basic-form>
    </template>
    <section class="buttons-area" ref="buttons">
      <el-button
        @click="search"
        icon="el-icon-search"
        size="mini"
        type="primary"
        v-if="searchFields.length || showSearchBtn"
      >{{ searchContent }}
      </el-button>
      <el-button
        @click="handleAddUpdate()"
        icon="el-icon-plus"
        plain
        size="mini"
        type="primary"
        v-if="showAdd"
        v-hasPermi="['curd:' + code + ':add']"
      >新增
      </el-button>

      <el-button
        v-if="showCov"
        v-hasPermi='[usePermission("showCov")]'
        plain
        size="mini"
        type="primary"
        @click="handleCovUpdate()"
      >GIS创建覆盖区域
      </el-button>

      <el-button
        @click="handleDelete()"
        icon="el-icon-delete"
        plain
        size="mini"
        type="danger"
        :disabled="!choiceIds.length"
        :loading="delLoading"
        v-hasPermi="['curd:' + code + ':delete']"
        v-if="showBatchDel"
      >删除
      </el-button>
      <el-button
        @click="handleDeleteAll()"
        icon="el-icon-delete"
        plain
        size="mini"
        type="danger"
        :loading="clearLoading"
        v-hasPermi="['curd:' + code + ':deleteAll']"
        v-if="showBatchDelAll"
      >清空
      </el-button>
      <commonExport
        validPermission
        :codeType="codeType"
        :expCsv="expCsv"
        :expExecl="expExecl"
        :expShape="expShape"
        :expMif="expMif"
        :choiceIds="choiceIds"
        :queryCode="getExportCode"
        :buttonName="exportButtonName"
        :currentCodeExportRecord="currentCodeExportRecord"
        :queryParams="Object.assign({},queryParams,query)"
        v-hasPermi="['export:' + getExportCode + ':exp']"
        v-if="showExport"
        style="padding-left: 4px;"
      ></commonExport>

      <importButton
        validPermission
        :importCode="importCode"
        :useImportPage="useImportPage"
        :showExportImport="showExportImport"
        :queryParams="Object.assign({},queryParams,query)"
        :buttonName="importButtonName"
        :showCurImportRecord="showCurImportRecord"
        :showTemplate="showImportTemplate"
		    :importUrl="importUrl"
        v-hasPermi="['import:' + importCode + ':imp']"
        v-if="importCode"
        @refresh="search"
        style="padding-left: 4px;"
        @input="handleCovUpload"
      ></importButton>

      <!-- 插入自定义组件 -->
      <component :is="component.name" v-for="(component, index) in getComponentArray(customComponent)"
                     :key="index" class="custom-component"
                     v-bind="component.props"
                     :code="code"
                     :menu-query="routerQuery"
                     :choiceIds="choiceIds"
                     :queryParams="Object.assign({},Object.assign({},queryParams,query))"
                     @refresh="search"
                     style="padding-top: 10px">
      </component>

      <slot name="customSectionSlot" :choiceIds="choiceIds"></slot>

      <TableTitleSetting v-if="isCurrentUserFieldShow" :code="code" :tableTitle="tableTitle" @restoreDefaultSettings="requestTableTitleFields"></TableTitleSetting>
    </section>
    <basic-table
      :show-download="showDownload"
      :checkbox="checkbox || showBatchDel"
      :showCov="showCov"
      :expand-component="expandComponent"
      :expandQueryCode="queryConfig.expandQueryCode "
      :expandQueryParam="Object.assign({},queryParams,query)"
      :height="tableHeight"
      :insertIndex="operationColumn"
      :is-expand="isExpand"
      :radio="radio"
      :tableData="tableData"
      :tableTitle="tableTitle"
      :span-method="spanMethod"
      @covclick="handleCovUpdate"
      @dblclick="handleDoubleClick"
      @handleSelectionChange="handleSelectionChange"
      @cellComponentEvent="handleCellComponentEvent"
      border
      ref="basicTable"
      v-loading="tableLoading"
      :key="tableTitle.length"
    >
      <template slot-scope="row">
        <div style="display: flex; flex-wrap: wrap;justify-content: center;align-content: start">
          <el-button
            v-if="showDetail"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="jumpView(row.item)"
          >详情
          </el-button>

          <el-button
            v-if="showEdit"
            v-hasPermi="['curd:' + code + ':update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleAddUpdate(row.item)"
          >修改
          </el-button>

          <el-button
            v-if="showDelete"
            slot="reference"
            v-hasPermi="['curd:' + code + ':delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            :loading="curDelRow == row.item[primaryKey]"
            @click="handleDelete(row.item)"
          >删除
          </el-button>
          <!-- 插入自定义组件 -->
          <component :is="customRowButtons" :row-data="row.item" class="custom-row-buttons" @refresh="search"></component>

          <slot name="customRowSlot" :row='row.item'></slot>
        </div>
      </template>
    </basic-table>

      <pagination
        v-if="showPage"
        :limit.sync="page.pageSize"
        :page.sync="page.pageNum"
        :total="total"
        @pagination="requestTableData"
      />

      <el-dialog
        v-if="openView"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :modal="false"
        :visible.sync="openView"
        title="详情"
        width="80%"
      >
        <BasicForm
          ref="form"
          :disabled="true"
          :fields="fields"
          :formValue="rowData"
          :groupColumn="3"
          class="preview-form"
          labelWidth="120px"
          size="small"
        />
      </el-dialog>


    <region-edit
      :visible="openCov"
      :is-preview="!showCov"
      ref="regionEditRef"
      @format="handleCovFormat"
      @submit="handleCovSubmit"
      @close="openCov = false">
    </region-edit>

    <dialogEdit
      v-if="openEdit"
      @closeEditDialog="closeEditDialog"
      :queryCode="code"
      :openView="openEdit"
      :queryKey="queryKey"
      :primaryKey="primaryKey"
      :copyRow="copyRowEnable"
      :tagsViewTitle="tagsViewTitle"
    />
	</div>
</template>

<script>
import {
  commonQuery,
  commonQueryByPost,
  commonQueryFieldALL,
  findCurrentFieldSort,
  setCurrentFieldSort
} from '@/api/kernel/query'
import { useFieldsChildren } from '@/utils/useDictionary'
import verificationPermission from '@/mixins/buttonPermission'

import { deepClone, debounce } from '@/utils'
import BasicForm from '@/components/BasicComponents/Form'
import BasicTable from '@/components/BasicComponents/Table'
import RegionEdit from "@/components/CoverageAnalysis/dialog/regionEdit.vue";
import { detail, add, update, del, delAll } from '@/api/kernel/curd'
import user from "@/store/modules/user";
import {uuid} from "vue-contextmenujs/src/util";
import { calculatePolygon } from '@/utils/gis/tools/common'
const dynamicComponents = {}
const contexts = [
  // require.context('./src', true, /components\/.*\.vue$/),
  // require.context('./shared', true, /components\/.*\.vue$/),
  // require.context('./modules', true, /components\/.*\.vue$/),
  require.context('./components', true, /\.vue$/)
]

contexts.forEach((context) => {
  context.keys().forEach((key) => {
    const component = context(key).default
    if (component && component.name) {
      dynamicComponents[component.name] = component
    }
  })
})
//debugger

export default {
	name: 'commonQueryTable',
	mixins: [verificationPermission],
	props: {
    mark: {
      type: String,
      default: 'commonQueryTable'
    },
		showDownload: {
			type: Boolean,
			default: false
		},
		hiddenHeader: {
			type: Boolean,
			default: false,
		},
		checkbox: {
			type: Boolean,
			default: false,
		},
		radio: {
			type: Boolean,
			default: false,
		},
		labelWidth: {
			type: String,
			default: '80px',
		},
		queryParams: {
			type: Object,
			require: false,
		},
		showPage: {
			type: Boolean,
			default: false,
		},
		pagerCount:{
			type:Number,
			default:7,
		},
		showSearchBtn: {
			type: Boolean,
			default: false,
		},
		showDetail: {
			type: Boolean,
			default: true,
		},
		showExport: {
			type: Boolean,
			default: true,
		},
		showEdit: {
			type: Boolean,
			default: false,
		},
		showAdd: {
			type: Boolean,
			default: false,
		},
		showDelete: {
			type: Boolean,
			default: false,
		},
		showBatchDelAll: {
			type: Boolean,
			default: false,
		},
		showBatchDel: {
			type: Boolean,
			default: false,
		},
		exportButtonName:{
			type: String
		},
		expShape: {
			type: Boolean,
			default: false
		},
    expMif: {
			type: Boolean,
			default: false
		},
		expCsv: {
			type: Boolean,
			default: true
		},
		expExecl: {
			type: Boolean,
			default: true
		},
		fixedHeight: {
			type: Number
		},
		code: {
			type: String,
			require
		},
		exportCode: {
			type: String
		},
		exportCodeType: {
			type: String
		},
		importCode: {
			type: String
		},
		importButtonName: {
			type: String
		},
		showExportImport: {
			type: Boolean,
			default: false
		},
		showImportTemplate: {
			type: Boolean,
			default: false
		},
		showCurImportRecord: {
			type: Boolean,
			default: false
		},
    currentCodeExportRecord:{
      type: Boolean,
			default: false
    },
    copyRowEnable: {
      type: Boolean,
      default: false
    },
    isCurrentUserFieldShow: {
      type: Boolean,
      default: false
    },
    popupEdit: {
      type: Boolean,
      default: true
    },
    importUrl: String,
    searchContent: {
      type: String,
      default: "搜索"
    },
    // 自定义组件 ./components/*
    customComponent: String | Array,
    customRowButtons: String | Object,
    expandComponent: {
      type: String,
      default: "div"
    },
    isExpand: {
      type: Boolean,
      default: false
    },
    showCov: {
      type: Boolean,
      default: false
    },
    showOperation: {
      type: Boolean,
      default: false
    },
    operationAreaWidth: Number,
    useImportPage:{
      type: Boolean,
      default: false
    },
    // 单元格自定义组件配置
    cellComponents: {
      type: Object,
      default: () => ({})
    },
    spanMethod: {
      type: Function
    }
	},

	data() {
		return {
      routerQuery: {},

      openSortble: false,
      codeType: "common",
      showSearch: true,
      pageLoading: false,
      tableLoading: false,
      delLoading: false,
      clearLoading: false,
      curDelRow: null,
      tableTitle: [],
      tableData: [],
      key: 0,
      tableHeight: 0,
      rowData: {},
      formData: {},
      total: 0,
      queryConfig: {},
			fields: [],
			openView: false,
      openCov: false,
			openEdit: false,
			openExportRecord: false,
			searchFields: [],
			isExport: false,
			primaryKey: 'id',
			primaryTitle: '',
			queryKey: undefined,
			operationColumn: undefined,
			tagsViewTitle: undefined,
			page: {
				pageNum: 1,
				pageSize: 10,
			},
			query: {},
			choiceIds: [],
		}
	},
	created() {
		if(this.exportCodeType){
			this.codeType = this.exportCodeType
		}else{
			//不传导出Code类型，如果导出编码exportCode传了，默认按系统参数编码处理
			if (this.exportCode) {
				this.codeType = 'config'
			} else {
				this.codeType = 'common'
			}
		}

    this.routerQuery = this.$router.currentRoute.query
    console.log(this.routerQuery )
		this.requestTableTitleFields()
    // this.initWebSocket();
	},
	mounted() {
		window.addEventListener('resize', this.setTableScreenHeight)
	},
	destroyed() {
		window.removeEventListener('resize', this.setTableScreenHeight)
	},
	watch: {
		showSearch(newval, oldval) {
			this.setTableScreenHeight()
		},
		fixedHeight(newval, oldval) {
			this.setTableScreenHeight()
		},
	},
	computed: {
		getExportCode() {
			if (this.exportCode) return this.exportCode
			return this.code
		},
	},
	activated() {
		this.search()
	},
	methods: {
    uuid,
    getComponentArray(components) {
      // 将逗号分隔的字符串转换为组件名称的数组
      return (!!components && !(components instanceof Array)) ? components.split(",").map(item => {
        return {name: item, props: {}}
      }) : components;
    },
    search(data) {
      this.page.pageNum = 1;
      this.$nextTick(() => {
        data && this.$emit("refreshTop", data);
      });
      this.requestTableData();
    },
		getQuery(value) {
			this.query = Object.assign(this.query, value)
			this.search()
		},
		resetQuery() {
			this.$refs.basicForm.resetForm()
			this.search()
		},
		jumpView(row) {
      if (this.popupEdit) {
        this.openView = true;
        this.$nextTick(() => {
          this.rowData = row;
        });
      }else{
        this.handleAddUpdate(row,true)
      }
		},
		setTableScreenHeight() {
			if (this.fixedHeight) {
				this.tableHeight = this.fixedHeight
				return
			}
			const clientHeight =
				window.innerHeight ||
				document.documentElement.clientHeight ||
				document.body.clientHeight

			const tableEl = this.$refs.basicTable?.$el
			if (tableEl) {
				const fiexdHeaderHeight =
					document.getElementsByClassName('fixed-header')[0]
				const searchAreaHeight = this.$refs.basicForm?.$el.offsetHeight
				this.tableHeight =
					clientHeight -
					fiexdHeaderHeight.offsetHeight -
					(searchAreaHeight || 0) -
					50 -
					20 -
					40
			}
		},
		async requestTableTitleFields() {
			const data = await commonQueryFieldALL(this.code)
      this.queryConfig = data.data
			this.fields = deepClone(data.data.fields)
			this.fields.forEach((item, index) => {
				item.show = true
				item.tagsViewTitle && (this.primaryTitle = item.name)
			})
			this.tableTitle = data.data.fields.filter((field) => field.show)

      if (this.isCurrentUserFieldShow) {
        let filedSortArray = []
        let tableColumns = []
        await findCurrentFieldSort(this.code, user.state.user.userId).then(res => filedSortArray = res.success ? res.data: [])
        filedSortArray.forEach(fieldSort => {
          for (let i = 0; i < this.tableTitle.length; i++) {
            if (fieldSort.commonQueryFieldId === this.tableTitle[i].id) {
              tableColumns.push({...this.tableTitle[i], fieldShow: fieldSort.show})
              break
            }
          }
        })
        if (filedSortArray.length > 0 && tableColumns.length > 0) {
          this.tableTitle = [...tableColumns]
        } else {
          this.tableTitle.forEach(item => this.$set(item, 'fieldShow', true))
          const fieldSortList = this.tableTitle.map((item, index) => {
            return {
              fieldId: item.id,
              name: item. name,
              title: item.title,
              sort: index,
              show: item.fieldShow
            };
          })
          setCurrentFieldSort(this.code, user.state.user.userId,fieldSortList)
        }
      }

			if (this.showDetail || this.showOperation || this.showEdit || this.showDelete || this.customRowButtons) {
				this.tableTitle.push({
					title: '操作',
					name: 'operation',
					fixed: 'right',
					width: this.operationAreaWidth || 180,
				})
				this.operationColumn = this.tableTitle.length - 1
			}

			this.searchFields = this.fields.filter((field) => {
				if (field.search) {
					field.formEdit = true
					field.formShow = true
					field.required = false
				}
				return field.search
			})
			!this.searchFields.length && await this.requestTableData()

			this.primaryKey = this.tableTitle.filter((field) => field.primaryKey)
			if (this.primaryKey && this.primaryKey.length > 0) {
				this.primaryKey = this.primaryKey[0].name
			} else {
				this.primaryKey = 'id'
			}

			this.pageLoading = false
			this.$nextTick(() => {
				this.setTableScreenHeight()
			})
			// 设置select字段选项
			useFieldsChildren(this.searchFields)
		},
		async requestTableData() {
			!this.pageLoading && (this.tableLoading = true)
			let params = Object.assign({}, this.queryParams, this.query)
			if (this.showPage) {
				params = Object.assign({}, this.page, params)
			}
      // 加入dialog参数
      if (this.code) {
        ++this.key
        const { data, total, key: curr } = await commonQueryByPost(this.code, params, this.key);
        if (this.key === curr ) {
          this.tableData = data;
          this.total = total;
          // this.hidePage = total < this.page.pageSize;
        }
      }
      this.$emit('tableDataLoaded', this.tableData);
      this.tableLoading = false;
		},
		handleSelectionChange(selection) {
			this.choiceIds = selection.map(item => item[this.primaryKey])
			this.$emit('handleSelectionChange', selection)
		},
    handleUpdate(row) {
      // console.log('handleUpdate row value',row)
      update(this.code, row);
      this.msgSuccess('更新成功')
    },
		handleDelete(row) {
			if(!row && this.choiceIds.length == 0 ){
				this.msgInfo('请勾选需要删除的记录')
        		return
			}
			let ids = [];
      if(row && row[this.primaryKey]){
        ids.push(row[this.primaryKey]);
        this.curDelRow =  row[this.primaryKey]
      }else{
        this.delLoading = true
        ids = this.choiceIds;
      }
			this.$confirm('是否确认删除数据项?', "警告", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			}).then(()=>{
				return del(this.code, ids);
			}).then(() => {
				this.requestTableData()
				this.msgSuccess('删除成功')
			}).finally(() => {
        this.curDelRow = null
        this.delLoading = false
      })
		},
		handleDeleteAll(){
			if(this.total === 0) return this.msgInfo('当前没有需要清空的数据！')

      this.clearLoading = true
			this.$confirm('是否确认删除所有数据项?', "警告", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			}).then(()=>{
				return delAll(this.code, {...this.queryParams, ...this.query});
			}).then(() => {
				this.requestTableData()
				this.msgSuccess('删除成功')
			}).finally(() => {
        this.clearLoading = false
      })
		},
		handleAddUpdate(row) {
			this.tagsViewTitle = row ? row[this.primaryTitle] : '新增'
			this.queryKey = row ? row[this.primaryKey] : null

      console.log('this.queryKey',this.queryKey)
			if (row && !this.queryKey) return
			if(this.popupEdit){
				this.openEdit = true
			}else{
				this.$router.push({
					path: '/curd/tagviewEdit',
					query: {
						queryCode: this.code,
						queryKey: this.queryKey,
						primaryKey: this.primaryKey,
						tagsViewTitle: this.tagsViewTitle,
					},
				})
			}

    },
    handleDoubleClick(row) {
      this.showEdit && this.handleAddUpdate(row)
    },
    closeEditDialog(){
      this.openEdit = false
      this.requestTableData()
    },
    handleCovFormat(wkt) {
      this.formData.wkt = wkt
      const { lon, lat, area } = calculatePolygon(wkt)
      this.formData.lon = lon
      this.formData.lat = lat
      this.formData.areaKm2 = area / (1000 * 1000) // 转为平方公里
    },
    async handleCovUpload(data = []) {
      if (!data.length) return
      const { province, provinceName, city, cityName } = this.$store.getters.user
      const list = data.filter(row => row.geometry).map(row => {
        const { lon, lat, area } = calculatePolygon(row.geometry)
        const areaKm2 = area / (1000 * 1000) // 转为平方公里
        return {
          province,
          provinceName,
          city,
          cityName,
          lon,
          lat,
          areaKm2,
          wkt: row.geometry
        }
      })
      if (this.queryParams.table === 'base.t_gis_building') this.formData.areaKm2 *= 1000 * 1000
      const { code } = await commonQueryByPost('multiSaveCoverageRegion', { ...this.queryParams, list })
      if (code !== 200) {
        this.$message.error('批量导入Shapefile 保存失败，请联系管理员')
        return
      }
      this.$message.success('批量保存成功')
      this.search()
    },
    async handleCovSubmit(search, pitchs = []) {
      const {province, provinceName, city, cityName, name_, scene} = search
      this.formData.province = province
      this.formData.provinceName_ = provinceName
      this.formData.city = city
      this.formData.cityName_ = cityName
      this.formData.scene = scene
      this.formData.name_ = name_

      const elLoadingComponent = this.$loading({
        lock: true,
        text: '正在保存...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.37)'
      })

      if (this.formData.wkt) {
        //
        this.handleCovFormat(this.formData.wkt)
        if (this.queryParams.table === 'base.t_gis_building') this.formData.areaKm2 *= 1000 * 1000
        const {code} = await commonQueryByPost('saveCoverageRegion', {...this.queryParams, ...this.formData}).catch(() => elLoadingComponent.close())

        if (code !== 200) {
          this.$message.error('覆盖区域保存失败，请联系管理员')
          return
        }
      }

      else if (pitchs.length) {
        const {code} = await commonQueryByPost('multiUploadCoverageRegion', {...this.queryParams, ...this.formData, pitchs}).catch(() => elLoadingComponent.close())
        if (code !== 200) {
          this.$message.error('覆盖区域保存失败，请联系管理员')
          return
        }
        await commonQueryByPost('multiClearUploadCoverageRegion', {pitchs})
      }

      this.$message.success('创建成功')
      this.openCov = false
      elLoadingComponent.close()
      this.search()
    },
    async handleCovUpdate(row,key) {
      // 图形编辑
      this.formData = row || {}
      this.regionUpload = !row
      this.openCov = true
      await this.$nextTick()
      await this.$refs.regionEditRef.init(row,key)
    },
    handleRowClick(row, column, event) {
      this.$emit('rowclick', row, column, event)
    },
    handleSelectClick(row, column, event) {
      this.$emit('selectlick', row, column, event)
    },
    areObjectsDifferent(obj1, obj2) {
      return !(JSON.stringify(obj1) === JSON.stringify(obj2));
      // console.log()
      // for (let key of keys) {
      //   // console.log(this.areObjectsDifferent(obj1[key]),obj2[key])
      //   if ( obj2[key]===undefined || obj1[key] !== obj2[key]) {
      //     return true;  // 属性值不相等
      //   }
      // }
      // return false;  // 所有属性值相等
    },
    extendButtonHandle(item) {
      this.$emit("extendButtonHandle", item);
    },
    // 处理单元格组件事件
    handleCellComponentEvent(eventName, data, row, column) {
      this.$emit('cellComponentEvent', eventName, data, row, column,this.mark);
    }
  },
  components: {
    ...dynamicComponents,
    BasicForm,
    BasicTable,
    RegionEdit,
    commonExport: () => import("@/components/CommonQuery/commonExport"),
    importButton: () => import("@/components/CommonImport/importButton"),
    dialogEdit: () => import("@/components/CommonCurd/dialogEdit"),
    FloatingBox: () => import("@/components/floatingBox/FloatingBox"),
    TableTitleSetting: () => import("./tableTitleSetting")
	},
}
</script>
<style lang="scss" scoped>
.preview {
	padding: 20px;
	.search-area {
		::v-deep {
			.el-form-item__label {
				text-align: right;
				vertical-align: middle;
				float: left;
				font-size: 14px;
				color: #606266;
				line-height: 40px;
				padding: 0 12px 0 0;
				-webkit-box-sizing: border-box;
				box-sizing: border-box;
				border: none;
				background: none;
				height: auto;
				line-height: 32px;
			}
			.form-item-slot {
				width: auto !important;
			}
		}
	}
	.buttons-area {
		padding-bottom: 12px;
	}

	::v-deep .pagination-container {
		margin-top: 0;
		margin-bottom: 5px;
	}
	::v-deep .el-button-group {
		margin: 0 20px;
		height: 28.67px;
	}

	.preview-form {
		::v-deep {
			.form-item__textarea {
				display: block;
				width: calc(100% - 30px) !important;
				.el-form-item {
					width: 100%;
				}
				.el-form-item__content {
					width: calc(100% - 120px);
				}
			}
		}
	}
}

.custom-component {
  display: inline-block;
  margin-left: 10px;
}
</style>
