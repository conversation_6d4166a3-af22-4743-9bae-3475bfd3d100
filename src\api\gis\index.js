import request from '@/utils/request'

export const tree = (userId) => {
  return request({
    url: 'cov/gisLayerMenu/tree?status=0&userId='+userId+'&_d=' + new Date().getTime(),
    method: 'get'
  })
}

export const list = id => {
  return request({
    url: 'cov/gisLayerGroup/list?menu_id=' +id + '&_d=' + new Date().getTime(),
    method: 'post'
  })
}

export const detail = code => {
  return request({
    url: 'cov/gisLayer/detailByCode?code=' + code + '&_d=' + new Date().getTime(),
    method: 'get'
  })
}

export const ds3dList= type => request({
  url:'/cov/gisDatasource/detail/all/'+type,
  method:'get'
});

export const geoJson = id => {
  return request({
    url: 'cov/buliding/getBulidingGeoJSON?id=' + id + '&_d=' + new Date().getTime(),
    method: 'get'
  })
}

export const getCenterPosition = () => {
  return request({
    url: 'cov/systemConfig/getByCode',
    method: 'post',
    data: {"CURRENT_PROVINCE_CODE": "", "CURRENT_LONGITUDE": "", "CURRENT_LATITUDE": ""}
  })
}

export const nameRequestGeoJson = param => {
  return request({
    url: 'kernel/dynamic/query/code/building_rsrp_query_by_name',
    method: 'post',
    param
  })
}

export const bulidingGridTable = id => {
  return request({
    url: 'cov/buliding/getBulidingGridTable?id=' + id,
    method: 'get'
  })
}

export const getAntenna = (bbox,id,filter) => {
  return request({
    url:'/cov/antenna/getAntenna?bbox='+bbox+'&dsId='+id+'&filter='+filter,
    method:'get'
  })
}

export const exportLayers = data => {
  return request({
    url: '/cov/export/exportLayers',
    method: 'post',
    data: data
  })
}

export const getStyles = id => {
  return request({
    url: `/cov/service/style/json/${id}`,
    method: 'get'
  })
}
