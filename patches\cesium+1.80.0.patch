diff --git a/node_modules/cesium/Source/Scene/ImageryLayer.js b/node_modules/cesium/Source/Scene/ImageryLayer.js
index eb082de..a3940c8 100644
--- a/node_modules/cesium/Source/Scene/ImageryLayer.js
+++ b/node_modules/cesium/Source/Scene/ImageryLayer.js
@@ -652,11 +652,13 @@ ImageryLayer.prototype._createTileImagerySkeletons = function (
   var imageryTilingScheme = imageryProvider.tilingScheme;
   var northwestTileCoordinates = imageryTilingScheme.positionToTileXY(
     Rectangle.northwest(rectangle),
-    imageryLevel
+    imageryLevel,
+    {type: true}
   );
   var southeastTileCoordinates = imageryTilingScheme.positionToTileXY(
     Rectangle.southeast(rectangle),
-    imageryLevel
+    imageryLevel,
+    {type: false}
   );
 
   // If the southeast corner of the rectangle lies very close to the north or west side
diff --git a/node_modules/cesium/Source/Scene/UrlTemplateImageryProvider.js b/node_modules/cesium/Source/Scene/UrlTemplateImageryProvider.js
index 3a8482c..20ca20c 100644
--- a/node_modules/cesium/Source/Scene/UrlTemplateImageryProvider.js
+++ b/node_modules/cesium/Source/Scene/UrlTemplateImageryProvider.js
@@ -35,6 +35,7 @@ var tags = {
   northProjected: northProjectedTag,
   width: widthTag,
   height: heightTag,
+  zoom: zoomTag,
 };
 
 var pickFeaturesTags = combine(tags, {
@@ -1071,6 +1072,13 @@ function heightTag(imageryProvider, x, y, level) {
   return imageryProvider.tileHeight;
 }
 
+function zoomTag(imageryProvider, x, y, level) {
+  var zoom = 1
+  if (level > 10) zoom = 3
+  else if (level > 6) zoom = 2
+  return zoom;
+}
+
 function iTag(imageryProvider, x, y, level, longitude, latitude, format) {
   computeIJ(imageryProvider, x, y, level, longitude, latitude);
   return ijScratch.x;
diff --git a/node_modules/cesium/Source/Scene/View.js b/node_modules/cesium/Source/Scene/View.js
index de46dd0..a544698 100644
--- a/node_modules/cesium/Source/Scene/View.js
+++ b/node_modules/cesium/Source/Scene/View.js
@@ -171,7 +171,13 @@ function updateFrustums(view, scene, near, far) {
       curFar = Math.min(far, farToNearRatio * curNear);
     }
     var frustumCommands = frustumCommandsList[m];
-    if (!defined(frustumCommands)) {
+
+    // 重置
+    frustumCommands = frustumCommandsList[m] = new FrustumCommands(
+      curNear,
+      curFar
+    );
+    /*if (!defined(frustumCommands)) {
       frustumCommands = frustumCommandsList[m] = new FrustumCommands(
         curNear,
         curFar
@@ -179,7 +185,7 @@ function updateFrustums(view, scene, near, far) {
     } else {
       frustumCommands.near = curNear;
       frustumCommands.far = curFar;
-    }
+    }*/
   }
 }
 
