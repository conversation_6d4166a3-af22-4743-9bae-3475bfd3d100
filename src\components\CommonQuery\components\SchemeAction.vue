<template>
  <div style="margin-left: 10px">
    <el-button size="mini" type="text"
               @click="openScheme">查看结果</el-button>
  </div>
</template>

<script>
import {getSubRouters} from '@/api/system/menu'
import localStorage from '@/utils/localStorage'
import { mapActions } from 'vuex'

export default {
  name: 'schemeAction',
  props: {
    rowData: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      system: 'ZNGH',
      subModule: 'GHGC',
      tabs:[
        {name: 'siteInfoDrawing',label: '物理站',field:  null,tableName: 'yn_plan.t_siteinfo_drawing',queryCode:'query_plan_siteinfo_drawing',exportCode:'YN_PLAN_SITE_REQUIREMENT_EXPORT'},
        {name: 'propertyDrawing',label: '物业点',field:  null,tableName: 'yn_plan.t_property_drawing',queryCode:'query_plan_property_point_info',exportCode:'YN_PLAN_PROPERTY_REQUIREMENT_EXPORT'}
      ]
    }
  },
  created() {
  },
  computed: {
  },
  methods: {
    ...mapActions({setCurrentSiderbar: 'setCurrentSiderbar'}),
    async openScheme() {
    // 查询只模块路由
      const {data: rows} = await getSubRouters({system: this.system, subSystem: this.subModule})
      if (!rows || !rows.length) return

      // 临时修改路径名称
      const currTitle = this.$route.meta.title
      this.$route.meta.title = this.rowData.engineeringname

      rows.forEach(tmpRoute => {
        tmpRoute.path = `${this.$route.path}/${this.rowData.engineeringid}/${tmpRoute.path}`
        tmpRoute.meta.engineeringId = this.rowData.engineeringid

        this.$store.dispatch('addTmpRoute', {route: tmpRoute})
      })
      // 还原路径名称
      this.$route.meta.title = currTitle

      const route = {
        path: this.$router.currentRoute.path,
        subModule: true,
        basePath: this.$route.path,
        children: rows,
        data: {
          id: this.rowData.engineeringid,
          name: this.rowData.engineeringname,
        }
      }

      if (this.rowData.code.startsWith('tabPanePreview/')) {
        let tabs = []
        for(let tab of this.tabs){
          tabs.push({
            label: tab.label,
            code: tab.queryCode,
            expCsv: true,
            showAdd: false,
            expExecl: true,
            showEdit: false,
            showCov: false,
            popupEdit: true,
            exportCode: tab.exportCode,
            showDelete: false,
            showDetail: true,
            showExport: true,
            showBatchDel: false,
            showSearchBtn: false,
            exportCodeType: "config",
            showBatchDelAll: false,
            exportButtonName: "数据导出",
            currentCodeExportRecord: true,
            isCurrentUserFieldShow: true,
            copyRowEnable: false,
            ingorePermission: true,
            queryParams:{
              engineeringId: this.rowData.engineeringid,
              schemeId: this.rowData.schemeid,
              planDim: tab.label
            }
          })
        }
        const tmpRoute = {
          path: `${this.$route.path}/${this.rowData.engineeringid}/${this.rowData.code}`,
          name: "tabPanePreview",
          openType: "2",
          component: "devManager/queryConfig/preview/tabPane.vue",
          meta: {
            engineeringId: this.rowData.engineeringid,
            title: `${this.rowData.engineeringname} / ${this.rowData.type}`,
            cache: false,
            icon: "table",
            menuParams: {
              tabs: JSON.stringify(tabs)
            }
          },
          hidden: true,
        }
        this.$store.dispatch('addTmpRoute', {route: tmpRoute})
        // 导航到 DataManagement 组件
        route.children.push(tmpRoute)
      }
      localStorage.putSessionStorage('moduleRoute', route)
      this.$router.push({path: `${this.$route.path}/${this.rowData.engineeringid}/${this.rowData.code}`}).then(() => this.setCurrentSiderbar(route))
    }
  },
  components: {

  },
}
</script>

<style lang="scss" scoped>

</style>
