# 可视化拓扑图组件 (VisualTopology)

这是一个基于 G6 图形库的可视化拓扑图组件，支持节点的添加、删除、编辑和连接等功能。

## 功能特性

- 添加/删除节点
- 双击节点创建连接
- 拖拽节点调整位置
- 编辑节点属性（名称、类型、颜色、描述）
- 导入/导出拓扑图数据
- 缩放和重置视图

## 使用方法

### 基本用法

```vue
<template>
  <div style="height: 600px;">
    <VisualTopology />
  </div>
</template>

<script>
import VisualTopology from '@/components/VisualTopology'

export default {
  components: {
    VisualTopology
  }
}
</script>
```

### 注意事项

- 组件容器需要设置高度，否则可能无法正常显示
- 组件内部已处理窗口大小变化事件，会自动调整画布大小

## 节点类型

组件支持以下几种预定义的节点类型：

- 默认 (default)
- 服务器 (server)
- 路由器 (router)
- 交换机 (switch)
- 终端 (terminal)

每种类型有不同的视觉样式，可以通过属性面板进行切换。

## 数据格式

导入/导出的数据格式为 JSON，包含 nodes 和 edges 两个数组：

```json
{
  "nodes": [
    {
      "id": "node-1",
      "label": "节点1",
      "nodeType": "server",
      "description": "这是一个服务器节点",
      "x": 100,
      "y": 200,
      "color": "#E1F5FE"
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "node-1",
      "target": "node-2",
      "label": "连接"
    }
  ]
}
```

## 示例页面

可以通过访问 `/topology/example` 路径查看组件的示例和演示。