<template>
  <div class="resource-config-container">
    <el-card class="config-header">
      <div slot="header" class="clearfix">
        <span class="config-title">资源配置管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="saveAllConfig">
          <i class="el-icon-check"></i> 保存所有配置
        </el-button>
      </div>
      <el-alert
        title="配置说明"
        type="info"
        :closable="false"
        show-icon>
        <p>此页面用于配置需求库、规划库、设计库中各类资源的添加/删除权限。各省可根据实际需求差异化配置资源操作权限。</p>
      </el-alert>
    </el-card>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 资源库配置 -->
      <el-col :span="24">
        <el-card class="resource-library-config">
          <div slot="header" class="clearfix">
            <span>资源库配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="resetResourceConfig">
              <i class="el-icon-refresh"></i> 重置
            </el-button>
          </div>

          <el-tabs v-model="activeLibraryTab" type="card">
            <el-tab-pane
              v-for="library in resourceLibraries"
              :key="library.code"
              :label="library.name"
              :name="library.code">

              <div class="library-config-content">
                <el-alert
                  :title="`${library.name}资源配置`"
                  type="success"
                  :closable="false"
                  show-icon
                  style="margin-bottom: 20px;">
                  <p>配置在{{library.name}}中可以进行添加或删除操作的资源类型</p>
                </el-alert>

                <div class="resource-grid">
                  <div
                    v-for="resource in resourceTypes"
                    :key="resource.code"
                    class="resource-item">
                    <el-card shadow="hover" class="resource-card">
                      <div class="resource-header">
                        <i :class="resource.icon" class="resource-icon"></i>
                        <span class="resource-name">{{ resource.name }}</span>
                      </div>

                      <div class="resource-controls">
                        <div class="control-item">
                          <span class="control-label">可添加：</span>
                          <el-switch
                            v-model="resourceConfig[library.code][resource.code].canAdd"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            @change="onResourceConfigChange(library.code, resource.code, 'canAdd', $event)">
                          </el-switch>
                        </div>

                        <div class="control-item">
                          <span class="control-label">可删除：</span>
                          <el-switch
                            v-model="resourceConfig[library.code][resource.code].canDelete"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            @change="onResourceConfigChange(library.code, resource.code, 'canDelete', $event)">
                          </el-switch>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配置历史记录 -->
    <el-card style="margin-top: 20px;" v-if="false">
      <div slot="header" class="clearfix">
        <span>配置历史</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="loadConfigHistory">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <el-table :data="configHistory" style="width: 100%" size="small">
        <el-table-column prop="configType" label="配置类型" width="120">
          <template slot-scope="scope">
            <el-tag type="success">
              资源配置
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="configKey" label="配置项" width="200"></el-table-column>
        <el-table-column prop="oldValue" label="原值" width="150">
          <template slot-scope="scope">
            <el-tag :type="scope.row.oldValue ? 'success' : 'danger'" size="mini">
              {{ scope.row.oldValue ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="newValue" label="新值" width="150">
          <template slot-scope="scope">
            <el-tag :type="scope.row.newValue ? 'success' : 'danger'" size="mini">
              {{ scope.row.newValue ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" width="120"></el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180"></el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getConfigKey, updateConfig, addConfig, updateConfigByCode } from '@/api/system/config'
import NodeStageEnum from '@/utils/nodeStageEnum'

export default {
  name: 'ResourceConfig',
  data() {
    return {
      activeLibraryTab: 'XQK',

      // 资源库定义
      resourceLibraries: [
        { code: NodeStageEnum.XQK.code(), name: NodeStageEnum.XQK.name() },
        { code: NodeStageEnum.GHK.code(), name: NodeStageEnum.GHK.name() },
        { code: NodeStageEnum.SJK.code(), name: NodeStageEnum.SJK.name() }
      ],

      // 资源类型定义
      resourceTypes: [
        { code: 'enodeb', name: '基站', icon: 'el-icon-office-building' },
        // { code: 'baseBand', name: '基带设备', icon: 'el-icon-cpu' },
        { code: 'cell', name: '小区', icon: 'el-icon-connection' },
        { code: 'rru', name: '射频', icon: 'el-icon-lightning' },
        { code: 'antenna', name: '天线', icon: 'el-icon-dish' },
        // { code: 'platform', name: '平台', icon: 'el-icon-postcard' },
        { code: 'equipmentRoom', name: '机房', icon: 'el-icon-house' },
        { code: 'rooftop', name: '天面', icon: 'el-icon-top' },
        // { code: 'supporting', name: '配套', icon: 'el-icon-setting' },
        // { code: 'rack', name: '机架', icon: 'el-icon-menu' },
        // { code: 'boardCard', name: '板卡', icon: 'el-icon-postcard' },
        // { code: 'extendDevice', name: '扩展设备', icon: 'el-icon-plus' }
      ],



      // 资源配置数据
      resourceConfig: {},

      // 配置历史
      configHistory: [],

      loading: false
    }
  },

  created() {
    this.initResourceConfig()
    this.loadConfigHistory()
  },

  methods: {
    // 初始化资源配置
    initResourceConfig() {
      this.resourceLibraries.forEach(library => {
        this.$set(this.resourceConfig, library.code, {})
        this.resourceTypes.forEach(resource => {
          this.$set(this.resourceConfig[library.code], resource.code, {
            canAdd: true,
            canDelete: true
          })
        })
      })
      this.loadResourceConfig()
    },



    // 加载资源配置
    async loadResourceConfig() {
      try {
        const response = await getConfigKey('RESOURCE_LIBRARY_CONFIG')
        if (response.code === 200 && response.msg) {
          const config = JSON.parse(response.msg)
          this.resourceConfig = { ...this.resourceConfig, ...config }
          console.log('this.resourceConfig',this.resourceConfig)
        }
      } catch (error) {
        console.error('加载资源配置失败:', error)
      }
    },



    // 资源配置变更
    onResourceConfigChange(libraryCode, resourceCode, configType, value) {
      if (configType === 'canAdd' && value === false) {
        this.resourceConfig[libraryCode][resourceCode]['canDelete'] = false
      }
      this.resourceConfig[libraryCode][resourceCode][configType] = value
      this.recordConfigChange('resource', `${libraryCode}.${resourceCode}.${configType}`, !value, value)
    },



    // 记录配置变更
    recordConfigChange(configType, configKey, oldValue, newValue) {
      this.configHistory.unshift({
        configType,
        configKey,
        oldValue,
        newValue,
        operator: this.$store.getters.name,
        updateTime: new Date().toLocaleString(),
        remark: `资源配置变更`
      })
    },

    // 保存所有配置
    async saveAllConfig() {
      this.loading = true
      try {
        // 保存资源配置
        await this.saveResourceConfig()

        this.$message.success('配置保存成功！')
      } catch (error) {
        this.$message.error('配置保存失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 保存资源配置
    async saveResourceConfig() {
      const configData = {
        code: 'RESOURCE_LIBRARY_CONFIG',
        name: '资源库配置',
        value: JSON.stringify(this.resourceConfig),
        belongSystem: 'system',
        classify: 'resource',
        remark: '资源库添加删除权限配置',
        dictFlag:'N',
        configType: 'N'
      }

      try {
        await updateConfigByCode(configData)
      } catch (error) {
        await addConfig(configData)
      }
    },



    // 重置资源配置
    resetResourceConfig() {
      this.$confirm('确定要重置资源配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.initResourceConfig()
        this.$message.success('资源配置已重置')
      })
    },



    // 加载配置历史
    loadConfigHistory() {
      // 这里可以从后端API加载历史记录
      // 目前使用本地数据
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-config-container {
  padding: 20px;

  .config-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }

  .resource-library-config {
    .library-config-content {
      .resource-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;

        .resource-item {
          .resource-card {
            transition: all 0.3s;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .resource-header {
              display: flex;
              align-items: center;
              margin-bottom: 16px;

              .resource-icon {
                font-size: 24px;
                color: #409EFF;
                margin-right: 12px;
              }

              .resource-name {
                font-size: 16px;
                font-weight: 500;
                color: #303133;
              }
            }

            .resource-controls {
              .control-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .control-label {
                  font-size: 14px;
                  color: #606266;
                }
              }
            }
          }
        }
      }
    }
  }


}
</style>
