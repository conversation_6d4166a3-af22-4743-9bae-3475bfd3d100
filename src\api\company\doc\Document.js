import request from '@/utils/request'

// 查询资料库列表
export function listDocument(query) {
  return request({
    url: '/doc/document/list',
    method: 'get',
    params: query
  })
}

// 查询资料库详细
export function getDocument(id) {
  return request({
    url: '/doc/document/' + id,
    method: 'get'
  })
}

export function viewDocument(id) {
  return request({
    url: '/doc/document/view/' + id,
    method: 'get'
  })
}

// 新增资料库
export function addDocument(data) {
  return request({
    url: '/doc/document',
    method: 'post',
    data: data
  })
}

// 修改资料库
export function updateDocument(data) {
  return request({
    url: '/doc/document',
    method: 'put',
    data: data
  })
}

// 删除资料库
export function delDocument(id) {
  return request({
    url: '/doc/document/' + id,
    method: 'delete'
  })
}


export function documentApply(data) {
  return request({
    url: '/doc/document/apply',
    method: 'post',
    data: data
  })
}

export function documentAudit(data) {
  return request({
    url: '/doc/document/audit',
    method: 'post',
    data: data
  })
}

export function visits(id) {
  return request({
    url: '/doc/document/visits/' + id,
    method: 'get'
  })
}

export function adminAudit(data) {
  return request({
    url: '/doc/document/adminAudit',
    method: 'post',
    data: data
  })
}
