import request from '@/utils/request'

// 查询根因分析调度列表
export function listDispatch(query) {
  return request({
    url: '/hvpAn/perAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询根因分析调度详细
export function getDispatch(id) {
  return request({
    url: '/hvpAn/perAnalysis/' + id,
    method: 'get'
  })
}

// 新增根因分析调度
export function addDispatch(data) {
  return request({
    url: '/hvpAn/perAnalysis/',
    method: 'post',
    data: data
  })
}

// 修改根因分析调度
export function updateDispatch(data) {
  return request({
    url: '/hvpAn/perAnalysis/',
    method: 'put',
    data: data
  })
}

// 删除根因分析调度
export function delDispatch(id) {
  return request({
    url: '/hvpAn/perAnalysis/' + id,
    method: 'delete'
  })
}
// 根因分析调度
export function executeAnalysis(belongSystem, sdate,configId,analysisType,taskId) {
  return request({
    url: '/hvpAn/perAnalysis/executeAnalysis?belongSystem='+belongSystem+'&sdate=' + sdate + "&configId=" + configId + "&analysisType=" + analysisType + "&id=" + taskId,
    method: 'get',
  })
}

// 快速根因分析调度
export function simpleExecuteAnalysis(query) {
  return request({
    url: '/hvpAn/perAnalysis/simpleExecuteAnalysis',
    method: 'post',
    data: query
  })
}
