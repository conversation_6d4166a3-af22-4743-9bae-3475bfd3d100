# 校验功能开发指南

## 快速开始

### 1. 基本使用
```javascript
import { fieldConfigValidate } from '@/utils/plan/validate'

// 基本校验调用
const validateForm = async () => {
  try {
    const errors = await fieldConfigValidate(fields, planSite, inResource)
    if (errors.length > 0) {
      // 处理校验错误
      handleValidationErrors(errors)
    }
  } catch (error) {
    console.error('校验异常:', error)
  }
}
```

### 2. 敏感词配置
```javascript
// 在系统参数中配置
// 参数键: SENSITIVE_WORDS
// 参数值: "测试,demo,临时,废弃"
```

## 常见开发场景

### 1. 添加新的字段类型支持
```javascript
// 在 sensitiveWordsValidate 函数中修改
const sensitiveFieldTypes = [
  'input',
  'textarea',
  'select',     // 新增支持
  'radio',      // 新增支持
  'checkbox'    // 新增支持
]
```

### 2. 自定义校验规则
```javascript
// 在 fieldsRequiredValid 中添加新的校验类型
if (formType === 'email' && !isNull(data[name])) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(data[name])) {
    result.message = [`${title}邮箱格式不正确`]
    result.errorType = 'formTypeError'
  }
}
```

### 3. 扩展敏感词匹配算法
```javascript
// 替换简单的包含匹配
const advancedSensitiveWordCheck = (text, sensitiveWords) => {
  const foundWords = []
  
  sensitiveWords.forEach(word => {
    // 支持正则表达式
    if (word.startsWith('/') && word.endsWith('/')) {
      const regex = new RegExp(word.slice(1, -1), 'i')
      if (regex.test(text)) {
        foundWords.push(word)
      }
    }
    // 支持模糊匹配
    else if (fuzzyMatch(text, word, 0.8)) {
      foundWords.push(word)
    }
    // 原有的包含匹配
    else if (text.toLowerCase().includes(word.toLowerCase())) {
      foundWords.push(word)
    }
  })
  
  return foundWords
}
```

## 调试技巧

### 1. 开启调试模式
```javascript
// 在开发环境中添加调试日志
const DEBUG_VALIDATION = process.env.NODE_ENV === 'development'

const debugLog = (stage, data) => {
  if (DEBUG_VALIDATION) {
    console.group(`[校验调试] ${stage}`)
    console.log(data)
    console.groupEnd()
  }
}

// 使用示例
debugLog('敏感词配置', sensitiveWords)
debugLog('字段过滤结果', filteredFields)
debugLog('校验结果', validationResult)
```

### 2. 性能分析
```javascript
// 添加性能监控
const performanceTracker = {
  timers: new Map(),
  
  start(name) {
    this.timers.set(name, performance.now())
  },
  
  end(name) {
    const startTime = this.timers.get(name)
    if (startTime) {
      const duration = performance.now() - startTime
      console.log(`[性能] ${name}: ${duration.toFixed(2)}ms`)
      this.timers.delete(name)
    }
  }
}

// 在校验函数中使用
performanceTracker.start('敏感词校验')
const result = await sensitiveWordsValidate(fields, planSite)
performanceTracker.end('敏感词校验')
```

### 3. 错误追踪
```javascript
// 添加详细的错误上下文
const createValidationError = (message, context) => {
  const error = new Error(message)
  error.context = {
    fieldName: context.fieldName,
    fieldValue: context.fieldValue,
    validationType: context.validationType,
    timestamp: new Date().toISOString()
  }
  return error
}
```

## 测试指南

### 1. 单元测试示例
```javascript
import { sensitiveWordsValidate } from '@/utils/plan/validate'
import { getConfigKey } from '@/api/system/config'

// Mock API
jest.mock('@/api/system/config')

describe('敏感词校验', () => {
  beforeEach(() => {
    getConfigKey.mockResolvedValue({
      data: { value: '测试,demo,临时' }
    })
  })

  test('应该检测输入框中的敏感词', async () => {
    const fields = {
      XQJD_site: [
        { name: 'siteName', title: '站点名称', formType: 'input' }
      ]
    }
    
    const planSite = {
      xqSite: { siteName: '测试站点001' }
    }

    const result = await sensitiveWordsValidate(fields, planSite)
    
    expect(result).toHaveLength(1)
    expect(result[0].fields[0].message[0]).toContain('测试')
  })

  test('应该跳过非输入类型字段', async () => {
    const fields = {
      XQJD_site: [
        { name: 'siteType', title: '站点类型', formType: 'select' }
      ]
    }
    
    const planSite = {
      xqSite: { siteType: '测试类型' }
    }

    const result = await sensitiveWordsValidate(fields, planSite)
    
    expect(result).toHaveLength(0)
  })
})
```

### 2. 集成测试
```javascript
describe('完整校验流程', () => {
  test('应该返回所有类型的校验错误', async () => {
    const result = await fieldConfigValidate(mockFields, mockPlanSite)
    
    // 验证包含必填校验、敏感词校验等
    const errorTypes = result.map(r => r.name)
    expect(errorTypes).toContain('basic')
    expect(errorTypes).toContain('sensitiveWords')
  })
})
```

## 常见问题解决

### 1. 敏感词校验不生效
**问题**: 配置了敏感词但校验不触发

**排查步骤**:
```javascript
// 1. 检查系统参数配置
const config = await getConfigKey('SENSITIVE_WORDS')
console.log('敏感词配置:', config)

// 2. 检查字段类型
console.log('字段配置:', fieldConfig.formType)

// 3. 检查字段值
console.log('字段值:', fieldValue)
```

**常见原因**:
- 系统参数未配置或配置错误
- 字段类型不在支持列表中
- 字段值为空或非字符串

### 2. 性能问题
**问题**: 校验耗时过长

**优化方案**:
```javascript
// 1. 减少敏感词数量
const optimizedSensitiveWords = sensitiveWords.slice(0, 50)

// 2. 添加字段值长度限制
if (value.length > 1000) {
  console.warn('字段值过长，跳过敏感词校验')
  return []
}

// 3. 使用防抖处理
const debouncedValidate = debounce(fieldConfigValidate, 300)
```

### 3. 内存泄漏
**问题**: 长时间使用后内存占用过高

**解决方案**:
```javascript
// 1. 清理缓存
const clearValidationCache = () => {
  validationCache.clear()
  fieldConfigCache.clear()
}

// 2. 限制缓存大小
const MAX_CACHE_SIZE = 100
if (cache.size > MAX_CACHE_SIZE) {
  const firstKey = cache.keys().next().value
  cache.delete(firstKey)
}
```

## 最佳实践

### 1. 代码组织
```javascript
// 推荐的文件结构
src/utils/plan/validate/
├── index.js              // 主入口
├── sensitive-words.js    // 敏感词校验
├── field-validation.js  // 字段校验
├── interface-validation.js // 接口校验
├── utils.js             // 工具函数
└── constants.js         // 常量定义
```

### 2. 配置管理
```javascript
// 集中管理校验配置
export const VALIDATION_CONFIG = {
  SENSITIVE_WORDS: {
    CACHE_TTL: 5 * 60 * 1000,
    MAX_WORDS: 100,
    SUPPORTED_TYPES: ['input', 'textarea']
  },
  PERFORMANCE: {
    MAX_FIELD_LENGTH: 1000,
    DEBOUNCE_DELAY: 300
  }
}
```

### 3. 错误处理
```javascript
// 统一的错误处理
const handleValidationError = (error, context) => {
  // 记录错误日志
  console.error('[校验错误]', {
    message: error.message,
    context,
    stack: error.stack
  })
  
  // 发送错误监控
  if (window.errorTracker) {
    window.errorTracker.captureException(error, { context })
  }
  
  // 返回用户友好的错误信息
  return {
    fields: [{
      name: context.fieldName,
      title: context.fieldTitle,
      message: ['校验过程中发生错误，请稍后重试']
    }],
    name: 'systemError',
    title: '系统错误'
  }
}
```

### 4. 性能优化
```javascript
// 使用 Web Worker 处理大量数据
const validateInWorker = (data) => {
  return new Promise((resolve, reject) => {
    const worker = new Worker('/workers/validation-worker.js')
    
    worker.postMessage(data)
    
    worker.onmessage = (e) => {
      resolve(e.data)
      worker.terminate()
    }
    
    worker.onerror = (error) => {
      reject(error)
      worker.terminate()
    }
  })
}
```

## 扩展开发

### 1. 插件开发
```javascript
// 校验插件接口
class ValidationPlugin {
  constructor(name, options = {}) {
    this.name = name
    this.options = options
  }
  
  // 插件初始化
  async init() {
    // 初始化逻辑
  }
  
  // 执行校验
  async validate(context) {
    // 校验逻辑
    return context
  }
  
  // 插件销毁
  destroy() {
    // 清理逻辑
  }
}

// 使用示例
const phoneValidationPlugin = new ValidationPlugin('phoneValidation', {
  pattern: /^1[3-9]\d{9}$/,
  message: '手机号格式不正确'
})
```

### 2. 自定义校验器
```javascript
// 校验器工厂
const createValidator = (name, validatorFn) => {
  return {
    name,
    validate: validatorFn,
    async: validatorFn.constructor.name === 'AsyncFunction'
  }
}

// 注册自定义校验器
const validators = new Map()

const registerValidator = (validator) => {
  validators.set(validator.name, validator)
}

// 使用示例
registerValidator(createValidator('uniqueId', async (value, context) => {
  const exists = await checkIdExists(value)
  return exists ? '该ID已存在' : null
}))
```

### 3. 国际化支持
```javascript
// 多语言错误信息
const i18nMessages = {
  'zh-CN': {
    'required': '此字段为必填项',
    'sensitiveWord': '包含敏感词: {words}',
    'invalidFormat': '格式不正确'
  },
  'en-US': {
    'required': 'This field is required',
    'sensitiveWord': 'Contains sensitive words: {words}',
    'invalidFormat': 'Invalid format'
  }
}

const getMessage = (key, params = {}, locale = 'zh-CN') => {
  let message = i18nMessages[locale][key] || key
  
  Object.keys(params).forEach(param => {
    message = message.replace(`{${param}}`, params[param])
  })
  
  return message
}
```
