// src/utils/nodeStageEnum.js

class NodeStage {
  constructor(id, code, name, stage, stageName, sort) {
    this._id = id;
    this._code = code;
    this._name = name;
    this._stage = stage;
    this._stageName = stageName;
    this._sort = sort;
  }

  id() {
    return this._id;
  }

  code() {
    return this._code;
  }

  name() {
    return this._name;
  }

  stage() {
    return this._stage;
  }

  stageName() {
    return this._stageName;
  }

  sort() {
    return this._sort;
  }
}

const NodeStageEnum = {
  //新站流程各个阶段
  XQJD: new NodeStage(null, null, "需求阶段", "XQJD", "需求阶段", -1),
  GHJD: new NodeStage(null, null, "规划阶段", "GHJD", "规划阶段", -1),
  LXJD: new NodeStage(null, null, "立项阶段", "LXJD", "立项阶段", -1),
  SJJD: new NodeStage(null, null, "设计阶段", "SJJD", "设计阶段", -1),
  SGJD: new NodeStage(null, null, "施工阶段", "SGJD", "施工阶段", -1),
  RWJD: new NodeStage(null, null, "入网阶段", "RWJD", "入网阶段", -1),
  //新站流程节点汇总，与后端枚举一致
  XQK: new NodeStage("jt3b453adde06a4772b465c4d6fb1ccc4e", "XQK", "需求阶段-需求库", "XQJD", "需求阶段", 0),
  XQSH: new NodeStage("jt3624e4a30c434f4c8b988084241825b2", "XQSH", "需求阶段-需求审核", "XQJD", "需求阶段", 1),
  GHK: new NodeStage("jta54ce3bf238a44eab1c6a8ac88a33804", "GHK", "规划阶段-规划库", "GHJD", "规划阶段", 2),
  GHSH: new NodeStage("jt87ae499eb89b4361a6a09fefb25f1d31", "GHSH", "规划阶段-规划审核", "GHJD", "规划阶段", 3),
  LXYJ: new NodeStage("jt7da58b7bb8394b46a57d49104fb6a30f", "LXYJ", "立项阶段-可行性研究", "LXJD", "立项阶段", 4),
  LXPF: new NodeStage("jt62294d6213bb485da11dbda605840718", "LXPF", "立项阶段-立项批复", "LXJD", "立项阶段", 5),
  SJK: new NodeStage("jt3ca48544a50e43ae9dfec174d28b6447", "SJK", "设计阶段-设计库", "SJJD", "设计阶段", 6),
  SJSH: new NodeStage("jt9291323169ad4012878c0a468e96a0f0", "SJSH", "设计阶段-设计审核", "SJJD", "设计阶段", 7),
  SGYY: new NodeStage("jtee5ccbe2cb9d499f89b6f83c3c4d28f1", "SGYY", "施工阶段-施工预约", "SGJD", "施工阶段", 8),
  SGGK: new NodeStage("jt03bce5c9630640ca9e3e4091918051c5","SGGK","施工阶段-施工管控","SGJD","施工阶段",9),
  ZLSH: new NodeStage("jt557774589a66472a81fd4d9266dbd586", "ZLSH", "施工阶段-资料审核", "SGJD", "施工阶段", 9),
  CSDD: new NodeStage("jtf523604e2bb4443a8977a6f1a0e7bf11", "CSDD", "入网阶段-传输调度", "RWJD", "入网阶段", 10),
  SHUJGH: new NodeStage("jt206c9b2e90de4e77a6710540c80d1913", "SHUJGH", "入网阶段-数据规划", "RWJD", "入网阶段", 11),
  SJSH_RW: new NodeStage("jte9246fbfedf749318bfac9e7191ee5f1", "SJSH_RW", "入网阶段-数据审核", "RWJD", "入网阶段", 12),
  JFMY: new NodeStage("jtd64c51a1abfd458eabd30ba9a41f32e8", "JFMY", "入网阶段-计费漫游", "RWJD", "入网阶段", 13),
  BJPH: new NodeStage("jt962badfc35144c499ac9946b709218df", "BJPH", "入网阶段-边界配合", "RWJD", "入网阶段", 14),
  SJJZ: new NodeStage("jt81e119e412314059b554be8b7bccecc8", "SJJZ", "入网阶段-数据加载", "RWJD", "入网阶段", 15),
  GJJC: new NodeStage("jt09aec4cab7144977835d0701d33801cc", "GJJC", "入网阶段-告警核查", "RWJD", "入网阶段", 16),
  DZYZ: new NodeStage("jted1f96cf2b144872a22dc651dc649c86", "DZYZ", "入网阶段-单站验证", "RWJD", "入网阶段", 17),
  DYSH: new NodeStage("jt3c075f6c870f4666b1835fc812737de2", "DYSH", "入网阶段-单验审核", "RWJD", "入网阶段", 18),
  RWPG: new NodeStage("jt7b05825a70604c33be1ef671ae53529c", "RWPG", "入网阶段-入网评估", "RWJD", "入网阶段", 19),
  XQBH: new NodeStage("jtf3c7e328839042598560d87e494fc774", "XQBH", "入网阶段-需求闭环", "RWJD", "入网阶段", 20),
  YJWH: new NodeStage("jta9f295b0d2cb4ea8bbd32e4fe60be1de", "YJWH", "移交维护", "YJJD", "运维阶段", 21),


  // “增删调”流程节点汇总，与后端枚举一致 todo

  /**
   * 根据 id 获取对应的枚举对象（默认返回 XQK）
   * @param {string} id
   * @returns {NodeStage}
   */
  getById(id) {
    return (
      Object.values(NodeStageEnum).find(
        (item) => item instanceof NodeStage && item.id() === id
      ) || NodeStageEnum.XQK
    );
  },
  getByCode(code) {
    return (
      Object.values(NodeStageEnum).find(
        (item) => item instanceof NodeStage && item.code() === code
      ) || NodeStageEnum.XQK
    );
  }
};

export default NodeStageEnum;
