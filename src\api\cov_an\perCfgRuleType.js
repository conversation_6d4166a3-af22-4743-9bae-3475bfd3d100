import request from '@/utils/request'

// 查询规则分类列表
export function listPerCfgRuleType(query) {
  return request({
    url: '/an/perCfgRuleType/list',
    method: 'get',
    params: query
  })
}

// 查询规则分类详细
export function getPerCfgRuleType(id) {
  return request({
    url: '/an/perCfgRuleType/' + id,
    method: 'get'
  })
}

// 新增规则分类
export function addPerCfgRuleType(data) {
  return request({
    url: '/an/perCfgRuleType',
    method: 'post',
    data: data
  })
}

// 修改规则分类
export function updatePerCfgRuleType(data) {
  return request({
    url: '/an/perCfgRuleType',
    method: 'put',
    data: data
  })
}

// 删除规则分类
export function delPerCfgRuleType(id) {
  return request({
    url: '/an/perCfgRuleType/' + id,
    method: 'delete'
  })
}