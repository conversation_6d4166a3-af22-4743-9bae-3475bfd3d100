import request from '@/utils/request'

// 查询cosmic功能过程列表
export function listTCosmicProcess(query) {
  return request({
    url: '/cosmic/process/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic功能过程详细
export function getTCosmicProcess(id) {
  return request({
    url: '/cosmic/process/' + id,
    method: 'get'
  })
}

// 新增cosmic功能过程
export function addTCosmicProcess(data) {
  return request({
    url: '/cosmic/process',
    method: 'post',
    data: data
  })
}

// 修改cosmic功能过程
export function updateTCosmicProcess(data) {
  return request({
    url: '/cosmic/process',
    method: 'put',
    data: data
  })
}

// 删除cosmic功能过程
export function delTCosmicProcess(id) {
  return request({
    url: '/cosmic/process/' + id,
    method: 'delete'
  })
}