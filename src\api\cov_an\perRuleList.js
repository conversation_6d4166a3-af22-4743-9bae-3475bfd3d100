import request from '@/utils/request'

// 查询规则配置列表
export function listPerRuleList(query) {
  return request({
    url: '/an/perRuleList/list',
    method: 'get',
    params: query
  })
}

//获取新对象
export function newPerRuleList() {
  return request({
    url: '/an/perRuleList/new',
    method: 'get'
  })
}

// 回溯
export function backRule(id,cycle) {
  return request({
    url: '/an/perRuleList/backRule?id=' + id+'&cycle='+cycle,
    method: 'get'
  })
}

//重调
export function runRuleTask(sdate,belongSystem) {
  return request({
    url: '/an/perRuleList/runRuleTask?sdate=' + sdate+'&belongSystem='+belongSystem,
    method: 'get'
  })
}

export function copyPerRuleList(id) {
  return request({
    url: '/an/perRuleList/copy/' + id,
    method: 'get'
  })
}

// 查询规则配置详细
export function getPerRuleList(ruleValueFeildId) {
  return request({
    url: '/an/perRuleList/' + ruleValueFeildId,
    method: 'get'
  })
}

// 新增规则配置
export function addPerRuleList(data) {
  return request({
    url: '/an/perRuleList',
    method: 'post',
    data: data
  })
}

// 修改规则配置
export function updatePerRuleList(data) {
  return request({
    url: '/an/perRuleList',
    method: 'put',
    data: data
  })
}

// 删除规则配置
export function delPerRuleList(ruleValueFeildId) {
  return request({
    url: '/an/perRuleList/' + ruleValueFeildId,
    method: 'delete'
  })
}