import request from '@/utils/request'

// 查询建筑物图纸管理列表
export function listDwg(query) {
  return request({
    url: '/cov/dwg/list',
    method: 'get',
    params: query
  })
}

// 查询建筑物图纸管理详细
export function getDwg(id) {
  return request({
    url: '/cov/dwg/' + id,
    method: 'get'
  })
}

// 查询建筑物图纸空间对象
export function getDwgGeom(id) {
  return request({
    url: '/cov/dwg/' + id+'/geom',
    method: 'get'
  })
}

// 查询图纸对应建筑物边界
export function getBuildingBox(id) {
  return request({
    url: '/cov/dwg/' + id+'/building_box',
    method: 'get'
  })
}

// 新增建筑物图纸管理
export function addDwg(data) {
  return request({
    url: '/cov/dwg',
    method: 'post',
    data: data
  })
}

// 新增建筑物图纸批量管理
export function addDwgBatch(data) {
  return request({
    url: '/cov/dwg/batch',
    method: 'post',
    data: data
  })
}


// 修改建筑物图纸管理
export function updateDwg(data) {
  return request({
    url: '/cov/dwg',
    method: 'put',
    data: data
  })
}

// 删除建筑物图纸管理
export function delDwg(id) {
  return request({
    url: '/cov/dwg/' + id,
    method: 'delete'
  })
}