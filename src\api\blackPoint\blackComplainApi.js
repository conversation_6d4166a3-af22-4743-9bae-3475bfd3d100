import axios from "@/utils/request";

/**
 * 小区相关
 */
class BlackComplainApi {
  get base() {
    return "/blackspot/blackComplain";
  }
  // 添加
  add(data) {
    return axios.post(`${this.base}/add`, data);
  }
  // 列表
  list(data) {
    return axios.get(`${this.base}/list`, {
      params: data
    });
  }
  // 周边投诉
  outerEdgeComplainInfo(data) {
    return axios.get(`${this.base}/outerEdgeComplainInfo`, {
      params: data
    });
  }
  //判断是否是最后一条黑点
  updateLast(data) {
    return axios.get(`${this.base}/updateLast?blackId=` + data);
  }
}

export default new BlackComplainApi();
