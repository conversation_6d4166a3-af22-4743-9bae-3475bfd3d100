<template>
	<div>
    <div v-if="tabs.length > 1">
      <el-tabs v-model="activeTab" type="card" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabs"
          :key="index"
          :label="item.label"
          :name="String(index)"
          lazy
        >
          <commonQueryTable showPage showSearchBtn v-bind="item"></commonQueryTable>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-else>
      <commonQueryTable showPage showSearchBtn v-bind="tabs[activeTab]"></commonQueryTable>
    </div>
	</div>
</template>

<script>
export default {
	name: 'tabPane',
	data() {
		return {
			activeTab: 0,
			tabs: [],
		}
	},
	created() {
		const { tabs } = this.$route.query
		if(typeof tabs === 'string'){
			this.tabs = JSON.parse(tabs.replace(/'/g, '"'))
		}else{
			this.tabs = tabs || []
		}
    	console.log(this.tabs);
	},
	methods: {
		handleClick() {},
	},
	components: {
		commonQueryTable: () => import('@/components/CommonQuery/commonQueryTable'),
	},
}
</script>
