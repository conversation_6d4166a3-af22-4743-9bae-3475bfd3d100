import request from '@/utils/request'

// 查询回溯任务列表
export function listPerRuleBackTask(query) {
  return request({
    url: '/hvpAn/perRuleBackTask/list',
    method: 'get',
    params: query
  })
}

// 查询回溯任务详细
export function getPerRuleBackTask(id) {
  return request({
    url: '/hvpAn/perRuleBackTask/' + id,
    method: 'get'
  })
}

// 新增回溯任务
export function addPerRuleBackTask(data) {
  return request({
    url: '/hvpAn/perRuleBackTask',
    method: 'post',
    data: data
  })
}

// 修改回溯任务
export function updatePerRuleBackTask(data) {
  return request({
    url: '/hvpAn/perRuleBackTask',
    method: 'put',
    data: data
  })
}

// 删除回溯任务
export function delPerRuleBackTask(id) {
  return request({
    url: '/hvpAn/perRuleBackTask/' + id,
    method: 'delete'
  })
}