import request from '@/utils/request'

// 查询访问列表
export function listVisits(query) {
  return request({
    url: '/doc/visits/list',
    method: 'get',
    params: query
  })
}

// 查询访问详细
export function getVisits(id) {
  return request({
    url: '/doc/visits/' + id,
    method: 'get'
  })
}

// 新增访问
export function addVisits(data) {
  return request({
    url: '/doc/visits',
    method: 'post',
    data: data
  })
}

// 修改访问
export function updateVisits(data) {
  return request({
    url: '/doc/visits',
    method: 'put',
    data: data
  })
}

// 删除访问
export function delVisits(id) {
  return request({
    url: '/doc/visits/' + id,
    method: 'delete'
  })
}