import request from '@/utils/request'


class PlanSchemeApi {
    get base() {
      return "/smartplan/yn/plan/scheme";
    }
    list(data) {
        return request({
            url: `${this.base}/list`,
            method: 'get',
            params: data
        });
    }

    get(id){
        return request({
            url: `${this.base}/${id}`,
            method: 'get'
        })
    }

    add(data,compute) {
      return request({
        url: `${this.base}/add/${compute}`,
        method: 'post',
        data: data
      });
    }

    update(data,compute){
        return request({
            url: `${this.base}/edit/${compute}`,
            method: 'put',
            data: data
        })
    }

    delete(id){
        return request({
            url: `${this.base}/${id}`,
            method: 'delete'
        })
    }

    copy(data,compute){
        return request({
            url: `${this.base}/copy/${compute}`,
            method: 'post',
            data: data
        })
    }


    runTask(id){
        return request({
            url: `${this.base}/compute/${id}`,
            method: 'put',
        })
    }
}
  
export default new PlanSchemeApi();
