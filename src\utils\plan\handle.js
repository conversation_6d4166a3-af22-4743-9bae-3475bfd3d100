// 任务处理页面特殊处理逻辑
import Vue from 'vue'
import { deepClone } from '@/utils'

export function controlFormFieldsChildren(value, field) {
	// 站型 控制配置的选项
	// 宏站： 配置的选项是S开头  其他是O
	const controlType = value === 'MacroSite' ? 'S' : 'O'

	!field.record && Vue.set(field, 'record', deepClone(field.children))

	const fieldValue = field.record.filter(
		(child) => child.value.charAt(0).toUpperCase() === controlType,
	)

	Vue.set(field, 'children', fieldValue)
}

// 比较两个对象,返回改变后的值组成的对象
export function compareTwoObjectsDiff(newObj, oldObj) {
	const changeObj = {}
	Object.keys(newObj).forEach((key) => {
		if (newObj[key] !== oldObj[key]) {
			changeObj[key] = newObj[key]
		}
	})
	return changeObj
}

// 字段隐藏显示设置
export function configFormHiddenFields(type = 'add', field, sourceFields) {
	const hiddenFields = typeof field === 'string' ? [field] : field
	hiddenFields.forEach((filedName) => {
		if (type === 'add') {
			!sourceFields.includes(filedName) && sourceFields.push(filedName)
		} else {
			if (sourceFields.includes(filedName)) {
				const index = sourceFields.findIndex((item) => item === filedName)
				sourceFields.splice(index, 1)
			}
		}
	})
}

// 根据字段映射同步数据
export function fieldsMapSyncData(map, sourceData, needSyncData, sync = true) {
	const sourceMap = Object.keys(sourceData)
	Object.keys(map).forEach((key) => {
		if (sourceMap.includes(map[key])) {
			if (sync) {
				console.log(key, sourceData[map[key]], needSyncData[key])
				needSyncData[key] = sourceData[map[key]]
			} else {
				// sync = false 没有值才同步
				!needSyncData[key] && (needSyncData[key] = sourceData[map[key]])
			}
		}
	})
}

// 校验数据对比
export function validFormContrastWithData(rules, data) {
	const result = deepClone(rules)
	const InvalidValue = [null, undefined, '']
	Object.keys(rules).forEach((field) => {
		if (!InvalidValue.includes(data[field])) {
			delete result[field]
		}
	})
	return result
}

// 获取字段
export function getFields(fields, type, code, include = true) {
	if (fields) {
		const currentTypeFields = fields && fields[type]
		if (!code) return currentTypeFields

		if (currentTypeFields) {
			const filterCode = Array.isArray(code) ? code : [code]
			return include
				? currentTypeFields.filter((field) =>
						filterCode.includes(field.formType),
				  )
				: currentTypeFields.filter(
						(field) => !filterCode.includes(field.formType),
				  )
		}
	}
}

// exclusiveSet 排他集 只生成自身的下拉选项
export function createFieldSerializeOptions(config, site, fields) {
	const { roomList, roofList } = site

	const countMap = {
		room: roomList.length,
		roof: roofList.length,
	}

	const { code, data, valueName, fieldName, subSet, subSecondSet, otherSet } =
		config

	// 前置值存在才生成下拉选项
	if (!data) return

	const prefixValue = data[valueName]
	if (prefixValue) {
		const currentField = fields[code]?.find((i) => i.name === fieldName)
		const count = countMap[prefixValue]
		currentField && createSerializeChildren(currentField, count)
	}

	const subPrefixValue = data[fieldName]
	if (subSet) {
		// 设置同一表单下的子集 比如需要选择天面下的支架
		const subSetField = fields[code]?.find((i) => i.name === subSet)
		if (subPrefixValue) {
			const subSetCount =
				prefixValue === 'room'
					? roomList[subPrefixValue - 1].rackList?.length
					: roofList[subPrefixValue - 1].platformList?.length
			createSerializeChildren(subSetField, subSetCount)
		}
	}

	// 第三级
	if (subSecondSet) {
		const subSecondSetField = fields[code]?.find((i) => i.name === subSecondSet)
		const subSecondPrefixValue = data[subSet]

		if (subSecondPrefixValue && prefixValue === 'roof') {
			const subSecondSetCount =
				roofList[subPrefixValue - 1]?.platformList[subSecondPrefixValue - 1]
					.bracketList.length
			createSerializeChildren(subSecondSetField, subSecondSetCount)
		}
	}

	// 设置同一表单下的其他集  比如rru ant的安装平台编号
	if (otherSet) {
		const otherSetField = fields[code]?.find((i) => i.name === otherSet)
		createOtherSetSerializeChildren(config, otherSetField, site)
	}
}

export function createOtherSetSerializeChildren(config, field, data) {
	const count =
		config.data[config.valueName] === 'roof'
			? data.roofList[0].roofSupportPlatformNumber
			: 1

	createSerializeChildren(field, count)
}

export function createSerializeChildren(field, count,namePrefix = '') {
	const MAX_COUNT = count && count > 100 ? 100 : count
	const children = getSerializeArray(+MAX_COUNT,namePrefix)
	field && Vue.set(field, 'children', children)
}

export function getSerializeArray(size,namePrefix = '') {
	const count = typeof size === 'number' && !isNaN(size) ? size : 0
	return new Array(count).fill(1).map((item, index) => {
		return { name: namePrefix + (item + index), value: item + index }
	})
}
export function generateArray(size) {
  return Array.from({ length: size }, (_, index) => ({
    name: index + 1,
    value: index + 1
  }));
}
// 转义后台返回的被转义后的字符串
export function transDbBackStr(str) {
	if (typeof str === 'string' && str.length) {
		str
			.replace(/\r/g, '\\r')
			.replace(/\n/g, '\\n')
			.replace(/\n/g, '\\n')
			.replace(/\t/g, '\\t')
			.replace(/\\/g, '\\\\')
			.replace(/("")+/g, '"')
			.replace(/\'/g, '&#39;')
			.replace(/ /g, '&nbsp;')
			.replace(/</g, '&lt;')
			.replace(/>/g, '&gt;')
	}
	return str
}
/**
 * <AUTHOR>  2025/5/19 09:25
 * @description:  清空综资标识
 */
export function clearResourceCode_(tabData){
   tabData['resourceCodeAnt'] = null ;//天线综资标识
   tabData['resourceCodeAntPara'] = null;//天线参数综资标识
   tabData['resourceCodeBoard'] = null;//板卡综资标识
   tabData['resourceCodeCell'] = null;//小区综资标识
   tabData['resourceCodeEnodeb'] = null;//基站综资标识
  //  tabData['resourceCodeEquip'] = null;//设备综资标识,BBU/du/cu不允许新增，故无须删除
   tabData['resourceCodeEu'] = null;//扩展设备综资标识
   tabData['resourceCodeChinaTown'] = null;//铁塔资源编码(非资管综资标识，亦删除)
   tabData['resourceCodePf'] = null;//平台综资标识
   tabData['resourceCodeRoof'] = null;//天面综资标识
   tabData['resourceCodeRoom'] = null;//机房综资标识
   tabData['resourceCodeRru'] = null;//射频单元综资标识
   tabData['resourceCodeShelf'] = null;//机架综资标识
}
