import request from '@/utils/request'

// 查询规则场景配置列表
export function listPerRuleSecen(query) {
  return request({
    url: '/an/perRuleSecen/list',
    method: 'get',
    params: query
  })
}

// 查询规则场景配置详细
export function getPerRuleSecen(id) {
  return request({
    url: '/an/perRuleSecen/' + id,
    method: 'get'
  })
}

// 新增规则场景配置
export function addPerRuleSecen(data) {
  return request({
    url: '/an/perRuleSecen',
    method: 'post',
    data: data
  })
}

// 修改规则场景配置
export function updatePerRuleSecen(data) {
  return request({
    url: '/an/perRuleSecen',
    method: 'put',
    data: data
  })
}

// 删除规则场景配置
export function delPerRuleSecen(id) {
  return request({
    url: '/an/perRuleSecen/' + id,
    method: 'delete'
  })
}