import request from '@/utils/request'

// 查询资料附件列表
export function listAttachment(query) {
  return request({
    url: '/doc/attachment/list',
    method: 'get',
    params: query
  })
}

// 查询资料附件详细
export function getAttachment(id) {
  return request({
    url: '/doc/attachment/' + id,
    method: 'get'
  })
}

// 新增资料附件
export function addAttachment(data) {
  return request({
    url: '/doc/attachment',
    method: 'post',
    data: data
  })
}

// 修改资料附件
export function updateAttachment(data) {
  return request({
    url: '/doc/attachment',
    method: 'put',
    data: data
  })
}

// 删除资料附件
export function delAttachment(id) {
  return request({
    url: '/doc/attachment/' + id,
    method: 'delete'
  })
}