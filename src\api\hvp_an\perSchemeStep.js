import request from '@/utils/request'

// 查询分析-方案列表
export function listSchemeStep(query) {
  return request({
    url: '/hvpAn/schemeStep/list',
    method: 'get',
    params: query
  })
}

// 查询分析-方案详细
export function getSchemeStep(id) {
  return request({
    url: '/hvpAn/schemeStep/' + id,
    method: 'get'
  })
}

// 新增分析-方案
export function addSchemeStep(data) {
  return request({
    url: '/hvpAn/schemeStep',
    method: 'post',
    data: data
  })
}

// 修改分析-方案
export function updateSchemeStep(data) {
  return request({
    url: '/hvpAn/schemeStep',
    method: 'put',
    data: data
  })
}

// 删除分析-方案
export function delSchemeStep(id) {
  return request({
    url: '/hvpAn/schemeStep/' + id,
    method: 'delete'
  })
}

export function getSchemeStepListById(id) {
  return request({
    url: '/hvpAn/schemeStep/getList/' + id,
    method: 'get'
  })
}
