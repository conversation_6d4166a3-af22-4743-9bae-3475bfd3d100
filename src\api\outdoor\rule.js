import request from '@/utils/request'

// 查询覆盖预测规则列表
export function listRule(query) {
  return request({
    url: '/outdoor/rule/list',
    method: 'get',
    params: query
  })
}

// 查询覆盖预测规则详细
export function getRule(id) {
  return request({
    url: '/outdoor/rule/' + id,
    method: 'get'
  })
}

// 新增覆盖预测规则
export function addRule(data) {
  return request({
    url: '/outdoor/rule',
    method: 'post',
    data: data
  })
}

// 修改覆盖预测规则
export function updateRule(data) {
  return request({
    url: '/outdoor/rule',
    method: 'put',
    data: data
  })
}

// 删除覆盖预测规则
export function delRule(id) {
  return request({
    url: '/outdoor/rule/' + id,
    method: 'delete'
  })
}