import request from '@/utils/request'

// 查询评分规则明细列表
export function listConfig(query) {
  return request({
    url: '/hvpVs/score/config/list',
    method: 'get',
    params: query
  })
}

// 查询评分规则明细详细
export function getConfig(id) {
  return request({
    url: '/hvpVs/score/config/' + id,
    method: 'get'
  })
}

// 新增评分规则明细
export function addConfig(data) {
  return request({
    url: '/hvpVs/score/config',
    method: 'post',
    data: data
  })
}

// 修改评分规则明细
export function updateConfig(data) {
  return request({
    url: '/hvpVs/score/config',
    method: 'put',
    data: data
  })
}

// 删除评分规则明细
export function delConfig(id) {
  return request({
    url: '/hvpVs/score/config/' + id,
    method: 'delete'
  })
}
