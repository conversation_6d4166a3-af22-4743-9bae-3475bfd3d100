import request from '@/utils/request'

// 查询GIS图例管理列表
export function listStyle(query) {
  return request({
    url: '/cov/style/list',
    method: 'get',
    params: query
  })
}

// 查询GIS图例管理详细
export function getStyle(id) {
  return request({
    url: '/cov/style/' + id,
    method: 'get'
  })
}

// 新增GIS图例管理
export function addStyle(data) {
  return request({
    url: '/cov/style',
    method: 'post',
    data: data
  })
}

export function addStyleBatch(data) {
  return request({
    url: '/cov/style/addStyleBatch',
    method: 'post',
    data: data
  })
}


// 修改GIS图例管理
export function updateStyle(data) {
  return request({
    url: '/cov/style',
    method: 'put',
    data: data
  })
}

// 删除GIS图例管理
export function delStyle(id) {
  return request({
    url: '/cov/style/' + id,
    method: 'delete'
  })
}