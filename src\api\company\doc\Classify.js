import request from '@/utils/request'

// 查询01 分类数据列表
export function listClassify(query) {
  return request({
    url: '/doc/classify/list',
    method: 'get',
    params: query
  })
}
export function listClassifyByParentId(parentId,type) {
  return request({
    url: '/doc/classify/list/'+parentId+'/'+type,
    method: 'get'
  })
}

// 查询01 分类数据详细
export function getClassify(id) {
  return request({
    url: '/doc/classify/' + id,
    method: 'get'
  })
}

// 新增01 分类数据
export function addClassify(data) {
  return request({
    url: '/doc/classify',
    method: 'post',
    data: data
  })
}

// 修改01 分类数据
export function updateClassify(data) {
  return request({
    url: '/doc/classify',
    method: 'put',
    data: data
  })
}

// 删除01 分类数据
export function delClassify(id) {
  return request({
    url: '/doc/classify/' + id,
    method: 'delete'
  })
}