# 规划处理页面性能优化方案A：渐进式加载优化

## 📋 项目概述

**优化目标**: src/views/plan/handle/index.vue 页面响应速度优化
**优化方案**: 渐进式加载 + 批量字典请求 + 表单虚拟化
**预期提升**: 首屏渲染时间减少60-70%，用户交互响应提升50%
**制定时间**: 2025-01-15
**制定人员**: Claude 4.0 sonnet

## 🔍 性能问题分析

### 当前主要性能瓶颈

1. **多个 BasicForm 组件同时渲染**
   - 页面包含大量 BasicForm 组件，每个都需要处理复杂的字段配置
   - 所有表单在页面加载时同时初始化，造成阻塞

2. **业务字段请求阻塞**
   - `requestFields` 方法会阻塞整个页面加载
   - `await useOrderDictionary('GH')` 和 `await requestFields()` 串行执行

3. **字典数据重复请求**
   - `useFieldsChildren` 为每个字段单独请求字典数据
   - 缺乏批量优化和缓存机制

4. **深度监听导致频繁重渲染**
   - BasicForm 组件的 watch 监听器触发过多响应式更新
   - 缺乏智能的更新策略

5. **缺乏懒加载机制**
   - 所有表单组件在页面加载时同时初始化
   - 没有按需加载和优先级控制

## 🚀 方案A：渐进式加载优化

### 核心设计思路

采用分阶段渲染策略，优先显示关键表单，其他表单按需懒加载，结合智能缓存机制。

### 分层渲染机制

**第一层（立即渲染）**:
- 当前激活的 el-collapse-item 中的表单
- 页面顶部的基本信息表单

**第二层（预加载）**:
- 用户可能立即点击的相邻表单
- 常用的业务表单

**第三层（懒加载）**:
- 其他所有表单组件
- 使用 IntersectionObserver API 检测进入视口时加载

## 📅 实施阶段规划

### 阶段1：基础工具创建（1-2天）

#### 1.1 创建渐进式加载工具
**文件**: `src/utils/useProgressiveLoading.js`

**功能特性**:
- 四个优先级层次：立即加载、高优先级、普通优先级、低优先级
- 使用 `requestIdleCallback` 优化任务调度
- 批量执行机制避免阻塞主线程
- 兼容性处理和错误恢复

#### 1.2 创建批量字典请求工具
**文件**: `src/utils/useBatchDictionary.js`

**功能特性**:
- 收集所有需要字典数据的字段
- 按数据源类型分组进行批量请求
- 智能缓存机制和过期策略
- 错误处理和重试机制

#### 1.3 表单组件懒加载支持
**文件**: `src/components/LazyFormWrapper.vue`

**功能特性**:
- 表单组件的异步加载包装器
- 加载状态和错误状态处理
- 预加载和缓存策略

### 阶段2：核心组件优化（2-3天）

#### 2.1 优化 BasicForm 组件
**文件**: `src/components/BasicComponents/Form/index.vue`

**主要改动**:
- 添加虚拟滚动支持
- 实现字段的按需渲染
- 优化 watch 监听器，减少不必要的更新
- 添加字段可见性检测

#### 2.2 改造主页面逻辑
**文件**: `src/views/plan/handle/index.vue`

**主要改动**:
- 重构 `init()` 方法，实现分层加载
- 添加表单组件的懒加载机制
- 优化 el-collapse 的渲染策略
- 实现表单优先级管理

#### 2.3 字典数据批量优化
**文件**: `src/utils/useDictionary.js`

**主要改动**:
- 扩展现有的 `useFieldsChildren` 函数
- 添加批量请求支持
- 实现智能缓存和预取机制

### 阶段3：性能优化和测试（1-2天）

#### 3.1 性能监控
- 添加关键性能指标收集
- 实现性能基准测试
- 添加错误监控和报告

#### 3.2 功能测试
- 全面的功能回归测试
- 兼容性测试
- 边界情况测试

#### 3.3 优化细节调整
- 根据测试结果进行微调
- 优化加载策略
- 完善错误处理

## 📊 预期性能提升

| 性能指标 | 优化前 | 优化后 | 提升幅度 |
|----------|--------|--------|----------|
| 首屏渲染时间 | 3-5秒 | 1-2秒 | **60-70%** |
| 用户交互响应 | 500-1000ms | 200-300ms | **50%** |
| 内存占用 | 基准值 | -40% | **40%减少** |
| 网络请求数 | 基准值 | -70% | **70%减少** |
| 页面可交互时间 | 5-8秒 | 2-3秒 | **60%** |

## 🛡️ 风险控制措施

### 向后兼容性保证
- 所有现有API保持不变
- 渐进式部署，不影响现有功能
- 详细的迁移指南和文档

### 部署策略
- 功能开关控制新特性启用
- 灰度发布，逐步推广
- 实时监控和快速回滚机制

### 质量保证
- 完整的单元测试和集成测试
- 性能基准测试和对比
- 代码审查和最佳实践检查

## 🔧 技术实现细节

### 渐进式加载优先级定义

```javascript
export const LOADING_PRIORITY = {
  IMMEDIATE: 0,  // 立即加载 - 当前激活表单
  HIGH: 1,       // 高优先级 - 相邻表单
  NORMAL: 2,     // 普通优先级 - 常用表单
  LOW: 3         // 低优先级 - 其他表单
}
```

### 批量字典请求策略

```javascript
// 收集字典请求 -> 分组批量请求 -> 分发结果
const batchStrategy = {
  collectRequests: (fields) => Map<dataSource, field[]>,
  batchRequest: (requests) => Promise<results>,
  distributeResults: (results, fields) => void
}
```

### 表单虚拟化机制

```javascript
// 使用 IntersectionObserver 检测字段可见性
const virtualizeStrategy = {
  observeFields: (fieldContainers) => void,
  renderVisibleFields: (visibleFields) => void,
  preloadNearbyFields: (currentField) => void
}
```

## 📝 实施检查清单

### 阶段1检查项
- [ ] useProgressiveLoading.js 工具创建完成
- [ ] useBatchDictionary.js 工具创建完成
- [ ] LazyFormWrapper.vue 组件创建完成
- [ ] 基础工具单元测试通过

### 阶段2检查项
- [ ] BasicForm 组件虚拟化改造完成
- [ ] 主页面分层加载逻辑实现完成
- [ ] 字典批量请求集成完成
- [ ] 功能测试通过

### 阶段3检查项
- [ ] 性能监控系统部署完成
- [ ] 性能基准测试完成
- [ ] 全面功能测试通过
- [ ] 文档和部署指南完成

## 📈 成功指标

### 技术指标
- 首屏渲染时间 < 2秒
- 用户交互响应时间 < 300ms
- 内存占用减少 > 35%
- 网络请求减少 > 65%

### 用户体验指标
- 页面加载体验评分 > 4.5/5
- 用户操作流畅度评分 > 4.5/5
- 错误率 < 0.1%

### 业务指标
- 页面跳出率减少 > 20%
- 用户操作完成率提升 > 15%
- 系统稳定性保持 99.9%+

## 💻 详细技术实现

### useProgressiveLoading.js 核心代码

```javascript
import Vue from 'vue'

export const LOADING_PRIORITY = {
  IMMEDIATE: 0,  // 立即加载
  HIGH: 1,       // 高优先级
  NORMAL: 2,     // 普通优先级
  LOW: 3         // 低优先级
}

export function useProgressiveLoading() {
  const loadingQueue = {
    [LOADING_PRIORITY.IMMEDIATE]: [],
    [LOADING_PRIORITY.HIGH]: [],
    [LOADING_PRIORITY.NORMAL]: [],
    [LOADING_PRIORITY.LOW]: []
  }

  // 添加加载任务
  function addLoadingTask(priority, task) {
    loadingQueue[priority].push(task)
  }

  // 执行加载队列
  async function executeLoadingQueue() {
    // 立即执行最高优先级任务
    await Promise.all(loadingQueue[LOADING_PRIORITY.IMMEDIATE].map(task => task()))
    loadingQueue[LOADING_PRIORITY.IMMEDIATE] = []

    // 使用 requestIdleCallback 执行其他优先级任务
    scheduleNextPriority()
  }

  // 调度下一个优先级的任务
  function scheduleNextPriority() {
    const priorities = [LOADING_PRIORITY.HIGH, LOADING_PRIORITY.NORMAL, LOADING_PRIORITY.LOW]

    for (const priority of priorities) {
      if (loadingQueue[priority].length > 0) {
        requestIdleCallback(() => {
          executeTaskBatch(priority, () => scheduleNextPriority())
        })
        break
      }
    }
  }

  // 执行一批任务
  async function executeTaskBatch(priority, callback) {
    const tasks = loadingQueue[priority].splice(0, 5) // 每批执行5个任务
    await Promise.all(tasks.map(task => task()))

    if (loadingQueue[priority].length > 0) {
      // 还有任务，继续执行当前优先级
      requestIdleCallback(() => executeTaskBatch(priority, callback))
    } else if (callback) {
      // 当前优先级任务完成，执行回调
      callback()
    }
  }

  return { addLoadingTask, executeLoadingQueue, LOADING_PRIORITY }
}
```

### useBatchDictionary.js 核心代码

```javascript
import Vue from 'vue'
import { useActions, useGetters } from '@/utils/useMappers'

const getDictionary = useGetters('dictionary')
const getDictionaryName = useGetters('dictionaryName')

export async function useBatchDictionary(fields) {
  const dictRequests = new Map()
  const actionDictionary = useActions('plan/addDictionary')

  // 收集所有字典请求
  fields.forEach(field => {
    const { dataSource, dataSourceType, href, cascadeCode, formType } = field
    if (!cascadeCode && dataSourceType && formType !== 'selectRemoteSearch') {
      let requestUrl = href || dataSource
      if (requestUrl) {
        const { code } = getDictionaryCode(requestUrl)

        // 检查是否已缓存
        if (!getDictionaryName().includes(code)) {
          if (!dictRequests.has(requestUrl)) {
            dictRequests.set(requestUrl, {
              fields: [],
              code,
              dataSourceType,
              requestUrl
            })
          }
          dictRequests.get(requestUrl).fields.push(field)
        } else {
          // 直接使用缓存数据
          Vue.set(field, 'children', getDictionary()[code])
        }
      }
    }
  })

  // 批量请求字典数据
  const promises = Array.from(dictRequests.values()).map(async (request) => {
    try {
      const data = await actionDictionary(request)
      return { ...request, data }
    } catch (error) {
      console.error(`字典请求失败: ${request.requestUrl}`, error)
      return { ...request, data: [], error }
    }
  })

  const results = await Promise.all(promises)

  // 将结果分配给对应的字段
  results.forEach(({ fields, data, error }) => {
    if (!error && data) {
      fields.forEach(field => {
        Vue.set(field, 'children', data)
      })
    }
  })

  return results
}

function getDictionaryCode(url) {
  if (!url.includes('?')) return { code: url }
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}
```

### 主页面改造关键代码

```javascript
// src/views/plan/handle/index.vue 关键改造部分

import { useProgressiveLoading, LOADING_PRIORITY } from '@/utils/useProgressiveLoading'
import { useBatchDictionary } from '@/utils/useBatchDictionary'

export default {
  // ... 现有代码

  setup() {
    const { addLoadingTask, executeLoadingQueue } = useProgressiveLoading()

    // 改造后的初始化方法
    const init = async () => {
      const { nodeId, currentStage, currentChildStage, siteCmccId } = state

      // 第一优先级：立即加载基础数据
      addLoadingTask(LOADING_PRIORITY.IMMEDIATE, async () => {
        siteCmccId && rewriteStages()
        await useOrderDictionary('GH')
      })

      // 第一优先级：立即加载当前激活的表单字段
      addLoadingTask(LOADING_PRIORITY.IMMEDIATE, async () => {
        await requestFieldsProgressive(currentStage, nodeId, true)
        state.loading = false
      })

      // 第二优先级：预加载站点数据
      addLoadingTask(LOADING_PRIORITY.HIGH, async () => {
        siteCmccId && requestPlanSite(currentStage, nodeId, true)
      })

      // 第二优先级：预加载操作按钮
      addLoadingTask(LOADING_PRIORITY.HIGH, async () => {
        ableToHandleTask.value && requestHandleButtons()
      })

      // 第三优先级：加载其他数据
      addLoadingTask(LOADING_PRIORITY.NORMAL, async () => {
        preHandleProvince()
        hiddenSchemeCollapse()
        taskId && (state.ck = strSum(strCutNumber(taskId)))
      })

      // 第四优先级：加载历史数据
      addLoadingTask(LOADING_PRIORITY.LOW, async () => {
        state.advanceRequestHistoryStages.includes(currentChildStage) && requestHistory()
        switchTabTrigger(state.currentChildStage, state.nodeId)
        requestSiteEnchosuerData(nodeId)
      })

      // 执行加载队列
      await executeLoadingQueue()
    }

    // 渐进式字段请求
    const requestFieldsProgressive = async (stage, nodeId, record) => {
      const data = await businessFieldConfig.queryFields('GH', nodeId)
      const fields = data.data

      if (fields) {
        // 处理字段数据
        Object.keys(fields).forEach((key, index) => {
          const replaceKey = key.replace('t_5g_', '')
          fields[replaceKey] = fields[key]
          replaceKey !== key && delete fields[key]

          const currentFields = fields[replaceKey]
          if (currentFields) {
            // 取消必填
            if (sysconfig.PLAN_CANCEL_REQUIRE == 'ENABLE') {
              currentFields.forEach(field => field.required = false)
            }
          }
          useShowTemporarySaveButton(replaceKey, currentFields)
        })

        // 批量处理字典数据
        const allFields = Object.values(fields).flat()
        await useBatchDictionary(allFields)
      }

      $set(state, 'fields', fields)
    }

    return {
      init,
      requestFieldsProgressive,
      // ... 其他返回值
    }
  }
}
```

## 🔄 迁移指南

### 现有代码兼容性

1. **API保持不变**: 所有现有的组件调用方式保持不变
2. **渐进式启用**: 通过配置开关控制新功能的启用
3. **回滚支持**: 可以快速回滚到原有实现

### 配置开关

```javascript
// 在 src/config/performance.js 中添加
export const PERFORMANCE_CONFIG = {
  enableProgressiveLoading: true,  // 启用渐进式加载
  enableBatchDictionary: true,     // 启用批量字典请求
  enableFormVirtualization: true,  // 启用表单虚拟化
  loadingBatchSize: 5,             // 批量加载任务数量
  idleCallbackTimeout: 50          // 空闲回调超时时间
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-15
**负责人**: Claude 4.0 sonnet
**审核状态**: 待审核
