import request from '@/utils/request'


// planSite 工单数据
export function planSite(siteIdCmcc, stageCode, nodeId) {
  return request({
    url: `/site/planSite/query/${siteIdCmcc}/${stageCode}/${nodeId}`
  })
}

// 保存
export function save(data) {
  return request({
    url: '/site/planSite/save',
    method: 'post',
    data
  })
}

// 保存
export function saveBanqian(data) {
  return request({
    url: '/site/planSite/saveBanqian',
    method: 'post',
    data
  })
}

// 处理任务
export function handleTask(data) {
  return request({
    url: '/site/planSite/taskComplete',
    method: 'post',
    data
  })
}

// 云南需求变更-处理任务
export function taskCompleteYNXQBG(data) {
  return request({
    url: '/site/planSite/ynDemandChangedTaskComplete',
    method: 'post',
    data
  })
}

// 批量处理任务
export function batchHandleTask(data) {
  return request({
    url: '/site/planSite/batchFlow',
    method: 'post',
    data
  })
}


// 转派任务
export function transferDispatch(data) {
  return request({
    url: '/kernel/flow/transferDispatch',
    method: 'post',
    data
  })
}

// 转派任务(北京)
export function transferDispatch_bj(data) {
  return request({
    url: '/site/planSite/transferDispatch',
    method: 'post',
    data
  })
}

//删除附件

export function delAttachment(id) {
  return request({
    url: '/site/plan/attachment/' + id,
    method: 'delete'
  })
}

// 批量立项批复
export function batchReplyOrder(data) {
  return request({
    url: '/site/planSite/batch/reply/order',
    method: 'post',
    data
  })
}

// 湖南获取最新告警
export function qryHnNewAlarm(data) {
  return request({
    url: '/service/alarm/getHnNewAlarm',
    method: 'post',
    data
  })
}

// 江西电调list
export function qryJxElectronic(data) {
  return request({
    url: '/site/jx/circuit/dispatcher/list',
    method: 'post',
    data
  })
}

// 江西电调保存
export function saveJxElectronic(data) {
  return request({
    url: '/site/jx/circuit/dispatcher/save',
    method: 'post',
    data
  })
}

// 工单挂起
export function orderHangUp(data) {
  return request({
    url: '/site/planSite/hangUp',
    method: 'post',
    data
  })
}

export function unHookOrder(data){
  return request({
    url: '/site/planSite/unhook/' + data,
    method: 'get',
  })
}

// 获取告警状态
export function qryAlarmStatus(data) {
  return request({
    url: '/site/service/alarm/alarm_status?code=' + data
  })
}

// 获取线网逻辑基站
export function qryOpenSite(data) {
  return request({
    url: '/site/openSite/query/' + data
  })
}

// 工单置顶
export function orderToTop(data) {
  return request({
    url: '/site/plan/orderTop',
    method: 'post',
    data
  })
}
// 撤销工单置顶
export function cancelOrderToTop(data) {
  return request({
    url: '/site/plan/orderTop/del',
    method: 'post',
    data
  })
}

// 浙江勘察报告导出
export function surveyReportExport(data) {
  return request({
    url: '/site/planSite/export/' + data.site_id_cmcc
  })
}

// 湖南触发光调接口
export function lightData(data) {
  return request({
    url: '/service/hn/light/api/light_data',
    method: 'post',
    data,
  })
}

export function auditStateChange(data) {
  return request({
    url: '/site/planSite/change/auditStatus',
    method: 'put',
    data,
  })
}



//坐标系转换
export function wgs84ToGcj02(query) {
  return request({
    url: '/wgs84ToGcj02',
    method: 'get',
    params: query
  })
}

export function exportNodeData(code, params) {
  return request({
		url: '/site/plan/import/export',
		method: 'post',
		data: {
      code,
      paramMap: params
    }
	})
}

export function noQueryExport(data, fileName) {
  return request({
    url: `/site/plan/export/noQueryExport/${fileName}`,
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 浙江版本单独适用浙江地图 鉴权 V3.2版本,获取地图授权码
export function getMapAk() {
  return request({
    url: '/site/zj/gis/auth',
    method: 'get'
  })
}


// 广西资管校验数据显示
export function getCrsErrorTips(siteIdCmcc) {
  return request({
    url: '/service/plan/gx/getErrorTips/',
    method: 'get',
    params: {
      siteIdCmcc
    }
  })
}
//内蒙dtn获取iframe基础信息
export function findIframeInfo(siteIdCmcc){
  return request({
    url: '/service/nm/dtn/siteElevation/findIframeInfo',
    method: 'get',
    params: {
      siteIdCmcc
    }
  })
}
//项目信息更新
export function updateProjInfo(data){
  return request({
    url: '/site/zgProjInfoUpd/updateProjInfo',
    method: 'post',
    data: data
  })
}
// 新疆需求导入进度
export function getImportTaskStatus(taskId) {
  return request({
    url: '/service/xj/task/' + taskId,
    method: 'get',
  })
}
//AI工艺验收信息查询
export function aiCheckInfo(params) {
  return request({
    url: '/site/plan/key/data/info',
    method: 'get',
    params: params
  })
}

//开站数据批量导出
export function openSiteDataBatchExport(data) {
  return request({
    url: '/site/openSite/batchExport' ,
    method: 'post',
    data: data
  })
}

//流转时忽略工单-模版导出
export function flowSkipOrderExportTemplate(data) {
  return request({
    url: '/site/flowSkipOrder/exportTemplate' ,
    method: 'post',
    data: data,
    responseType:'blob'
  })
}

//流转时忽略工单-删除
export function flowSkipOrderDeleteByIds(data) {
  return request({
    url: '/site/flowSkipOrder/deleteByIds' ,
    method: 'post',
    data: data
  })
}
