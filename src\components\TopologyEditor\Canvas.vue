<template>
  <div class="topology-canvas" ref="container"></div>
</template>

<script>
import G6 from '@antv/g6'

export default {
  name: 'Canvas',
  props: {
    nodes: { type: Array, default: () => [] },
    edges: { type: Array, default: () => [] },
    selected: { type: [Object, null], default: null }
  },
  data() {
    return {
      graph: null
    }
  },
  watch: {
    nodes: {
      handler() { this.renderGraph() },
      deep: true
    },
    edges: {
      handler() { this.renderGraph() },
      deep: true
    }
  },
  mounted() {
    this.initGraph()
  },
  beforeDestroy() {
    if (this.graph) this.graph.destroy()
  },
  methods: {
    // 初始化G6画布
    initGraph() {
      if (this.graph) this.graph.destroy()
      this.graph = new G6.Graph({
        container: this.$refs.container,
        width: this.$refs.container.offsetWidth || 800,
        height: this.$refs.container.offsetHeight || 600,
        modes: {
          default: [
            'drag-canvas',
            'zoom-canvas',
            'drag-node',
            'click-select',
            {
              type: 'brush-select',
              trigger: 'shift',
            }
          ]
        },
        layout: {
          type: 'dagre',
          rankdir: 'LR',
          nodesep: 50,
          ranksep: 100
        },
        defaultNode: {
          type: 'rect',
          size: [120, 40],
          style: {
            fill: '#E1F5FE',
            stroke: '#1890ff',
            radius: 6
          },
          labelCfg: {
            style: {
              fill: '#333',
              fontSize: 14,
              fontWeight: 700
            }
          }
        },
        defaultEdge: {
          type: 'polyline',
          style: {
            stroke: '#909399',
            lineWidth: 2,
            endArrow: true
          },
          labelCfg: {
            style: {
              fill: '#666',
              fontSize: 12
            }
          }
        },
        animate: true
      })
      this.bindEvents()
      this.renderGraph()
    },
    // 渲染节点和连线
    renderGraph() {
      if (!this.graph) return
      const data = {
        nodes: this.nodes.map(n => ({ ...n })),
        edges: this.edges.map(e => ({ ...e }))
      }
      this.graph.data(data)
      this.graph.render()
      this.graph.fitView()
    },
    // 绑定G6事件
    bindEvents() {
      // 节点/边选中
      this.graph.on('node:click', evt => {
        this.$emit('select', evt.item.getModel())
      })
      this.graph.on('edge:click', evt => {
        this.$emit('select', evt.item.getModel())
      })
      // 右键菜单
      this.graph.on('node:contextmenu', evt => {
        evt.preventDefault()
        this.$emit('contextmenu', {
          position: { x: evt.canvasX, y: evt.canvasY },
          target: evt.item.getModel()
        })
      })
      // 拖拽、缩放等可扩展
    }
  }
}
</script>

<style scoped>
.topology-canvas {
  flex: 1;
  min-width: 0;
  min-height: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}
</style>
