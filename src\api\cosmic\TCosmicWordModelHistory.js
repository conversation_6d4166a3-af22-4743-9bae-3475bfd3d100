import request from '@/utils/request'

// 查询10 word导出模板-修改历史列表
export function listTCosmicWordModelHistory(query) {
  return request({
    url: '/cosmic/wordModelHistory/list',
    method: 'get',
    params: query
  })
}

// 查询10 word导出模板-修改历史详细
export function getTCosmicWordModelHistory(id) {
  return request({
    url: '/cosmic/wordModelHistory/' + id,
    method: 'get'
  })
}

// 新增10 word导出模板-修改历史
export function addTCosmicWordModelHistory(data) {
  return request({
    url: '/cosmic/wordModelHistory',
    method: 'post',
    data: data
  })
}

// 修改10 word导出模板-修改历史
export function updateTCosmicWordModelHistory(data) {
  return request({
    url: '/cosmic/wordModelHistory',
    method: 'put',
    data: data
  })
}

// 删除10 word导出模板-修改历史
export function delTCosmicWordModelHistory(id) {
  return request({
    url: '/cosmic/wordModelHistory/' + id,
    method: 'delete'
  })
}