import request from '@/utils/request'

// 查询地市配置列表
export function listPerCfgArea(query) {
  return request({
    url: '/an/perCfgArea/list',
    method: 'get',
    params: query
  })
}

// 查询地市配置详细
export function getPerCfgArea(province) {
  return request({
    url: '/an/perCfgArea/' + province,
    method: 'get'
  })
}

// 新增地市配置
export function addPerCfgArea(data) {
  return request({
    url: '/an/perCfgArea',
    method: 'post',
    data: data
  })
}

// 修改地市配置
export function updatePerCfgArea(data) {
  return request({
    url: '/an/perCfgArea',
    method: 'put',
    data: data
  })
}

// 删除地市配置
export function delPerCfgArea(province) {
  return request({
    url: '/an/perCfgArea/' + province,
    method: 'delete'
  })
}