<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="流出分支" prop="sequenceCode">
            <el-select v-model="form.sequenceCode">
              <el-option
                v-for="item in processBranch"
                :key="item.condition"
                :label="item.conditionName"
                :value="item.condition"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="通用查询" prop="queryCode">
            <el-input
              v-model="form.queryCode"
              disabled
            >
            <el-button slot="append" @click="searchQueryCode" icon="el-icon-search"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in statusOptions"
                :key="dict.code"
                :label="dict.code"
                >{{ dict.name }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扩展参数" prop="queryExtendParam">
            <CodeMirror ref="codemirrorqueryExtendParam"
                v-model="form.queryExtendParam"
                :height="'150px'"
                mode="javascript"></CodeMirror>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="校验提示信息" prop="tipInfo">
            <el-input v-model="form.tipInfo" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="规则条件">
          <el-table
          :data="form.ruleConditionList"
          style="width: 100%"
          border
          size="mini">
          <el-table-column
            align="center"
            label="且/或"
            prop="andOr"
          >
            <template slot-scope="scope" v-if="scope.$index >0">
              <el-select
                size="mini"
                v-model="scope.row.andOr"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in andOrTypeList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
            </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="字段名"
            prop="fieldName"
          >
            <template slot-scope="scope">
              <el-select
                size="mini"
                v-model="scope.row.fieldName"
                @change="fieldNameChange(scope.$index)"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in queryFields"
                  :key="item.name"
                  :label="item.title"
                  :value="item.name"
                >
                </el-option>
            </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="条件"
            prop="conditionType"
          >
          <template slot-scope="scope">
            <el-select
              size="mini"
              v-model="scope.row.conditionType"
              @change="handleConditionTypeChange(scope.row)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in conditionOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="条件取值"
            prop="fieldValue"
          >
            <template slot-scope="scope">
              <el-input
                size="mini"
                clearable
                v-model="scope.row.fieldValue"
              >
              </el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                class="el-icon-plus"
                type="success"
                @click="addRuleCondition(scope.$index)"
              ></el-button>
              <el-button
                :disabled="scope.$index == 0"
                size="mini"
                class="el-icon-close"
                :type="scope.$index >0 ? 'danger':''"
                @click="delRuleCondition(scope.$index)"
              ></el-button>
            </template>
          </el-table-column>
          </el-table>
      </el-form-item>
    </el-form>
    <el-dialog title="选择查询" :visible.sync="openCommonQuery"
      width="1200px"
      append-to-body>
      <queryConfig isChoice :multipleCheck="false" @choice="queryChoice"></queryConfig>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addChoiceQuery">确 定</el-button>
        <el-button @click="openCommonQuery = !openCommonQuery">取 消</el-button>
      </div>
     </el-dialog>
  </div>
</template>

<script>
import queryConfig from  "@/views/devManager/queryConfig"
import { commonQueryFieldALL } from "@/api/kernel/query";
import { addRule, updateRule, getRule } from "@/api/kernel/flow";
import CodeMirror from "@/components/Editor/CodeMirror";

export default {
  name: 'ruleEdit',
  components: {
    queryConfig,
    CodeMirror
  },
  props: {
    ruleId: {
      type: String,
    },
    nodeId:{
      type: String,
      require
    },
    processBranch:{
      type: Array,
      default:()=>[]
    },
    statusOptions: {
      type: Array,
      default: [],
    },
    procDefKey:{
      type: String,
      require
    }
  },
  data() {
    return {
      openCommonQuery: false,
      choiceQuery: null,
      queryFields: [],
      form: {
        id: null,
        nodeId: this.nodeId,
        procDefKey: this.procDefKey,
        name: null,
        sequenceCode: '1',
        queryCode: null,
        queryExtendParam: "",
        status: 'ENABLE',
        tipInfo: null,
        triggerType: 'PreProcessBofore',
        ruleType: "2",
        ruleConditionList: []
      },
      triggerTypeOptions: [],
      andOrTypeList: [
        {code:'and',name:"且"},
        {code:'or',name:"或"}
      ],
      conditionOptions: [
        {code: 'eq',name: '等于'},
        {code: 'ne',name: '不等于'},
        {code: 'lt',name: '小于'},
        {code: 'gt',name: '大于'},
        {code: 'le',name: '小于等于'},
        {code: 'ge',name: '大于等于'},
        {code: 'contain',name: '包含'},
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "审核内容不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.ruleId && this.getRuleById()
    this.getDicts("FLOW_CHECK_TRIGGER_TYPE").then((response) => {
      this.triggerTypeOptions = response.data;
    });
  },
  methods: {
    // 添加条件类型变更处理
    handleConditionTypeChange(row) {
      const numericConditions = ['lt', 'gt', 'le', 'ge']
      const numericTypes = ['Integer', 'Long', 'Double', 'BigDecimal']

      if (numericConditions.includes(row.conditionType) &&
        !numericTypes.includes(row.fieldType)) {
        this.$message.error('大小比较条件仅适用于数字类型字段')
        row.conditionType = 'eq' // 重置为默认条件
      }
    },
    // 修改获取规则时的初始化逻辑
    async getRuleById() {
      try {
        const res = await getRule(this.ruleId)
        this.form = res.data
        await this.queryConfigField()

        // 初始化时校验历史数据
        this.form.ruleConditionList.forEach(row => {
          const fieldInfo = this.queryFields.find(f => f.name === row.fieldName)
          if (row.fieldNameCn === '记录数' && row.fieldName === 'cnt') {
            return
          }
          if (fieldInfo) {
            row.fieldType = fieldInfo.javaType || 'String'
          }

          const numericConditions = ['lt', 'gt', 'le', 'ge']
          const numericTypes = ['Integer', 'Long', 'Double', 'BigDecimal']

          if (numericConditions.includes(row.conditionType) &&
            !numericTypes.includes(row.fieldType)) {
            this.$message.error('当前字段类型不支持大小比较，已重置条件为等于').then(() => {
              setTimeout(() => {
                this.$message.closeAll();
              }, 5000);
            })
            row.conditionType = 'eq'
          }
        })
      } catch (error) {
        console.error('获取规则失败:', error)
      }
    },
    searchQueryCode(){
      this.openCommonQuery = true;
    },
    columnWidth(){
      if(this.form.ruleType == 'flowAudit')  return '650'
      return null
    },
    queryChoice(selection){
      if(!selection || selection.length == 0) return
      this.choiceQuery = selection
    },
    addRuleCondition(){
      this.form.ruleConditionList.push({
        andOr: 'and',
        fieldName: "",
        fieldNameCn: "",
        fieldType: 'String',
        conditionType: 'eq',
        fieldValue: null
      })
    },
    delRuleCondition(index){
      this.form.ruleConditionList.splice(index, 1)
    },
    addChoiceQuery(){
       this.openCommonQuery = false
       this.form.queryCode = this.choiceQuery[0].code;
       this.queryConfigField()
       this.form.ruleConditionList = [{
          andOr: "",
          fieldName: "",
          fieldNameCn: "",
          conditionType: 'eq',
          fieldType: 'String',
          fieldValue: null
        }]
    },
    // 修改字段选择处理
    fieldNameChange(conditionIndex) {
      const row = this.form.ruleConditionList[conditionIndex]
      const fieldInfo = this.queryFields.find(s => s.name === row.fieldName)

      if (fieldInfo) {
        row.fieldType = fieldInfo.javaType || 'String'
        row.fieldNameCn = fieldInfo.title || row.fieldName

        // 跳过对特殊字段（fieldNameCn等于"记录数"且 fieldName等于"cnt"）的条件校验
        if (row.fieldNameCn === '记录数' && row.fieldName === 'cnt') {
          return
        }
        // 检查现有条件类型是否合法
        const numericConditions = ['lt', 'gt', 'le', 'ge']
        const numericTypes = ['Integer', 'Long', 'Double', 'BigDecimal']

        if (numericConditions.includes(row.conditionType) &&
          !numericTypes.includes(row.fieldType)) {
          this.$message.error('当前字段类型不支持大小比较，已重置条件为等于').then(() => {
            setTimeout(() => {
              this.$message.closeAll();
            }, 5000);
          })
          row.conditionType = 'eq'
        }
      }
    },
    async queryConfigField() {
      try {
        const { code, data } = await commonQueryFieldALL(this.form.queryCode)
        if (code === 200) {
          this.queryFields = data.fields || []
          if (!this.queryFields.some(f => f.name === 'cnt')) {
            this.queryFields.push({ name: 'cnt', title: '记录数' })
          }
        }
      } catch (error) {
        console.error('获取查询字段失败:', error)
      }
    },
    /** 提交按钮 */
    submitForm() {
      console.log("this.form",this.form)
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRule(this.form).then((response) => {
              this.msgSuccess('修改成功')
              this.$emit('cancel',true);
            }).catch(()=>{
              this.$emit('cancelLoding',false);
            });
          } else {
            addRule(this.form).then((response) => {
              this.msgSuccess('新增成功')
              this.$emit('cancel',true);
            }).catch(()=>{
              this.$emit('cancelLoding',false);
            });
          }
        }
      })
      this.$emit('cancelLoding',false);
    },
  },
}
</script>

<style lang="scss" scoped>

.el-row{
    display:flex;
    flex-wrap: wrap;
}

.desc{
  line-height: 10px;
}
</style>
