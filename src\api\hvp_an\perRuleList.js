import request from '@/utils/request'

// 查询规则配置列表
export function listPerRuleList(query) {
  return request({
    url: '/hvpAn/perRuleList/list',
    method: 'get',
    params: query
  })
}

//获取新对象
export function newPerRuleList() {
  return request({
    url: '/hvpAn/perRuleList/new',
    method: 'get'
  })
}

// 回溯
export function backRule(id,cycle) {
  return request({
    url: '/hvpAn/perRuleList/backRule?id=' + id+'&cycle='+cycle,
    method: 'get'
  })
}

//重调
export function runRuleTask(sdate,belongSystem) {
  return request({
    url: '/hvpAn/perRuleList/runRuleTask?sdate=' + sdate+'&belongSystem='+belongSystem,
    method: 'get'
  })
}

export function copyPerRuleList(id) {
  return request({
    url: '/hvpAn/perRuleList/copy/' + id,
    method: 'get'
  })
}

// 查询规则配置详细
export function getPerRuleList(ruleValueFeildId) {
  return request({
    url: '/hvpAn/perRuleList/' + ruleValueFeildId,
    method: 'get'
  })
}

// 新增规则配置
export function addPerRuleList(data) {
  return request({
    url: '/hvpAn/perRuleList',
    method: 'post',
    data: data
  })
}

// 修改规则配置
export function updatePerRuleList(data) {
  return request({
    url: '/hvpAn/perRuleList',
    method: 'put',
    data: data
  })
}

// 删除规则配置
export function delPerRuleList(ruleValueFeildId) {
  return request({
    url: '/hvpAn/perRuleList/' + ruleValueFeildId,
    method: 'delete'
  })
}