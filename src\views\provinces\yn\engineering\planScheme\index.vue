<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="方案名称" prop="schemeName">
          <el-input v-model="queryParams.schemeName" placeholder="请输入方案名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            v-hasPermi="['yn:plan:scheme:add']"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增方案</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" border :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" v-if="false" width="55" align="center" />
        <el-table-column label="工程名称" v-if="false" align="center" prop="name" >
          <template slot-scope="scope">
            {{engineeringData.name}}
          </template>
        </el-table-column>
        <el-table-column label="方案名称" align="center" prop="schemeName" />
        <el-table-column label="创建人" align="center" prop="createUserName" />
        <el-table-column label="创建时间" align="center" prop="createTime" />
        <el-table-column label="更新时间" align="center" prop="updateTime" />
        <el-table-column label="规划需求数量" align="center" prop="requirementNum" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope" >
            <el-tag :type="scope.row.status == 'ENABLE' ? '' : 'info' ">{{ statusFormat(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-thumb"
              v-if="scope.row.status == 'ENABLE' "
              :loading="curRowId == scope.row.id"
              v-hasPermi="['yn:plan:scheme:compute']"
              @click="runTask(scope.row)"
            >计算</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-suitcase"
              v-if="scope.row.status == 'ENABLE' "
              v-hasPermi="['yn:plan:scheme:compute']"
              @click="runTaskLog(scope.row)"
            >日志</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              v-if="scope.row.status == 'ENABLE' "
              @click="viewComputeResult(scope.row)"
            >查看结果</el-button>
            <el-button
              v-hasPermi="['yn:plan:scheme:copy']"
              @click="handleUpdate(scope.row,true)"
              icon='el-icon-document-copy'
              size='mini'
              type='text'
            >复制
            </el-button>
            <el-button
              v-hasPermi="['yn:plan:scheme:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row,false)"
            >修改</el-button>
            <el-button
              v-hasPermi="['yn:plan:scheme:delete']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加或修改任务编排对话框 -->
      <el-dialog v-if="open" :title="title" :visible.sync="open"
        width="90%" append-to-body :close-on-press-escape="false" :close-on-click-modal="false" >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="6">
              <el-form-item label="工程名称" prop="engineeringName">
                <el-input disabled v-model="form.engineeringName"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创建人" prop="createUserName">
                <el-input disabled v-model="form.createUserName"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="方案名称" prop="schemeName">
                <el-input v-model="form.schemeName" placeholder="请输入方案名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.code"
                  :label="dict.code"
                >{{dict.name}}</el-radio>
              </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="scheme-rule">
            <el-tabs v-model="activeTab" type="border-card">
                <el-tab-pane
                    v-for="(item, index) in tabs"
                    :key="index"
                    :label="item.label"
                    :name="item.name"
                    >
                    <span v-if="!item.field">未配置画像，请先配置画像</span>
                    <template v-else>
                      <ConditionRule rootCondition :field="item.field"
                        :operators="operators"
                        :groupTags="form.schemeConfig[item.name].groupTags"
                        :condition="form.schemeConfig[item.name].ruleInfo"
                        :tableName="item.tableName"
                        :engineeringId="engineeringData.id"
                        ></ConditionRule>
                      <el-divider content-position="left">需求规则预览</el-divider>
                      <div class="sql-preview">
                        <pre v-html="highlightBrackets('pseudoCode')"></pre>
                      </div>
                      <el-divider content-position="left">SQL预览</el-divider>
                      <div class="sql-preview">
                        <pre v-html="highlightBrackets('preview')"></pre>
                      </div>
                    </template>

                </el-tab-pane>
            </el-tabs>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="saveLoading" type="primary" @click="submitForm(false)">保 存</el-button>
          <el-button :loading="saveLoading" v-if="form.status == 'ENABLE' " type="primary" @click="submitForm(true)">保存并计算</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <el-dialog title="执行日志" :visible.sync="dialogVisibleLog" width="85%" >
      <el-table v-loading="logLoading" :data="logList" size="mini" style="width: 100%;margin-bottom: 20px;" row-key="id" border :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <el-table-column prop="sub_title" label="标 题" min-width="230" show-tooltip-when-overflow>
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{'计算任务：' + scope.row.start_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{`【日志${scope.row.message ? ': ' + scope.row.message.substring(0, scope.row.message.indexOf(' ')).replace('步骤', '') : ''}】` + scope.row.sub_title}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="160" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.start_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.start_time}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="160" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.end_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.end_time}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="compute_time" label="耗 时" width="115" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.compute_time}}</span>
            <span style="color: #736A6AFF;" v-else>
              <span style="color: #2FAD75FF;" v-if="!!scope.row.compute_time && scope.row.compute_time.startsWith('0分')">{{scope.row.compute_time}}</span>
              <span style="color:#e45416;" v-else-if="!!scope.row.compute_time &&
               (!scope.row.compute_time.startsWith('0分')
                && !scope.row.compute_time.startsWith('1分')
                && !scope.row.compute_time.startsWith('2分')
                && !scope.row.compute_time.startsWith('3分')
                )">
                {{scope.row.compute_time}}
              </span>
              <span v-else>{{scope.row.compute_time}}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消 息" min-width="200" show-tooltip-when-overflow >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.message ? scope.row.message.substring(scope.row.message.indexOf(' ') + 1) : scope.row.message}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.message ? scope.row.message.substring(scope.row.message.indexOf(' ') + 1) : scope.row.message}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="exception" label="异常信息" width="110" show-tooltip-when-overflow >
          <template slot-scope="scope">
            <el-button
              class="copy-btn" v-if="!!scope.row.exception" size="mini" type="text"
              icon="el-icon-document-copy" @click="copyText(scope.row, 'exception')">复制异常信息</el-button>
            <span v-else>{{scope.row.exception}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="110">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 'PROCESS'" type="primary"><i class="el-icon-loading"></i> 执行中</el-tag>
            <el-tag v-if="scope.row.status === 'FAILED'" type="danger"><i class="el-icon-error"></i> 执行失败</el-tag>
            <el-tag v-if="scope.row.status === 'SUCCEED'" type="success"><i class="el-icon-success"></i> 执行成功</el-tag>
            <el-tag v-if="scope.row.status === 'STOP'" type="warning"><i class="el-icon-warning"></i> 意外终止</el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope='scope'>
            <el-button
              class="copy-btn" v-if="scope.row.sql" size="mini" type="text"
              icon="el-icon-document-copy" @click="copyText(scope.row, 'sql')">复制SQL
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="logTotal>0"
        :total="logTotal"
        :page.sync="logParams.pageNum"
        :limit.sync="logParams.pageSize"
        @pagination="getLogList"
      />


    </el-dialog>
    </div>
  </template>

  <script>
  import PlanSchemeApi from '@/api/provinces/yn/planScheme'
  import localStorage from '@/utils/localStorage'
  import ConditionRule from './ConditionRule'
  import { commonQueryByPost } from '@/api/kernel/query'
  import { to } from '@/utils'
  import { mapActions } from 'vuex'

  export default {
    name: "YnPlanScheme",
    components: {
        ConditionRule,
      commonQueryTable: () => import('@/components/CommonQuery/commonQueryTable')
    },
    data() {
      return {
        // 遮罩层
        loading: true,
        logLoading: false,
        saveLoading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        logTotal: 0,
        // 任务编排表格数据
        dataList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        isCopy: false,
        showRunRecord: false,
        // 状态字典
        statusOptions: [{
          code: 'ENABLE',
          name: '启用',
          tag: 'success'
        },{
          code: 'DISABLE',
          name: '停用',
          tag: 'info'
        }],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          engineeringId: null,
          schemeName: null,
        },
        logParams:{
          pageNum: 1,
          pageSize: 10,
        },
        dialogVisibleLog: false,
        logList: [],
        curRow: null,
        curRowId: null,
        // 表单参数
        form: {
          schemeConfig:{

          }
        },
        // 表单校验
        rules: {
          schemeName: [
            { required: true, message: "方案名称不能为空", trigger: "blur" }
          ],
        },
        operators: [
          { label: '等于', value: '=', allowTyps:['VARCHAR','NUMERIC','DATE','GEOMETRY'] },
          { label: '不等于', value: '!=', allowTyps: ['VARCHAR','NUMERIC','DATE','GEOMETRY']  },
          { label: '大于', value: '>', allowTyps:['NUMERIC'] },
          { label: '大于等于', value: '>=', allowTyps:['NUMERIC'] },
          { label: '小于', value: '<' , allowTyps:['NUMERIC']},
          { label: '小于等于', value: '<=', allowTyps:['NUMERIC'] },
          { label: '包含', value: '~', allowTyps:['VARCHAR','GEOMETRY'] },
          { label: '不包含', value: '!~', allowTyps:['VARCHAR','GEOMETRY']  },
          { label: '为空', value: 'IS_NULL' },
          { label: '不为空', value: 'IS_NOT_NULL' },
          { label: '在列表中', value: 'IN', allowTyps:['VARCHAR','GEOMETRY'] },
          { label: '不在列表中', value: 'NOT_IN', allowTyps:['VARCHAR','GEOMETRY'] }
        ],
        engineeringData: null,
        activeTab: 'siteInfoDrawing',
        tabs:[
            {name: 'siteInfoDrawing',label: '物理站',field:  null,tableName: 'yn_plan.t_siteinfo_drawing',queryCode:'query_plan_siteinfo_drawing',exportCode:'YN_PLAN_SITE_REQUIREMENT_EXPORT'},
            {name: 'propertyDrawing',label: '物业点',field:  null,tableName: 'yn_plan.t_property_drawing',queryCode:'query_plan_property_point_info',exportCode:'YN_PLAN_PROPERTY_REQUIREMENT_EXPORT'}
        ]
      };
    },
    watch: {

      activeTab(newVal, oldVal) {
        this.generatePre()
      },
      'form.schemeConfig': {
        handler(newVal, oldVal) {
          this.generatePre()
        },
        deep: true
      }
    },
    created() {
      const route = localStorage.getSessionStorage('moduleRoute')
      this.engineeringData = route.data
      this.queryParams.engineeringId = this.engineeringData.id
      this.getList();
      this.getFields()
    },
    methods: {
      ...mapActions({setCurrentSiderbar: 'setCurrentSiderbar'}),
      /** 查询任务编排列表 */
      async getList() {
        this.loading = true;
        try {
          const { data,total } = await PlanSchemeApi.list(this.queryParams)
          this.dataList = data;
          this.total = total;
        } finally {
          this.loading = false;
        }
      },
      // 状态字典翻译
      statusFormat(row, column) {
        return this.selectDictLabel(this.statusOptions, row.status);
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          engineeringId: this.engineeringData.id,
          engineeringName: this.engineeringData.name,
          schemeName: null,
          status: 'ENABLE',
          schemeConfig: {
            siteInfoDrawing: {
              ruleInfo:{
                rule: 'and',
                type: 'group',
                isCollapsed: true,
                items: [{ field: '',fieldType: '' , operator: '=', conditions: '',value: null,  type: 'condition'}]
              },
              groupTags:[],
              preview: '',
              pseudoCode: ''
            },
            propertyDrawing: {
              ruleInfo:{
                rule: 'and',
                type: 'group',
                isCollapsed: true,
                items: [{ field: '',fieldType: '' , operator: '=', conditions: '',value: null, type: 'condition'}]
              },
              groupTags:[],
              preview: '',
              pseudoCode: ''
            }
          },
          createUserName: this.$store.state.user.name,
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加规划需求方案";
      },
      /** 修改按钮操作 */
      handleUpdate(row,isCopy) {
        this.isCopy = isCopy
        const id = row.id || this.ids
        PlanSchemeApi.get(id).then(response => {
          this.form = response.data;
          this.form.engineeringName = this.engineeringData.name
          this.form.schemeConfig = JSON.parse(this.form.schemeConfig)
          this.open = true;
          this.title =`${isCopy?'复制':'修改'}规划需求方案`;
        });
      },
      getFields(){
        this.tabs.forEach(tab => {
          commonQueryByPost('queryPlanParameterConfig', {type: tab.label}).then(res => {
            /*if(res.data.length > 0){
              const tabFields =  JSON.parse(res.data[0].params)
              tab.field = tabFields.find(tab => ['物理站画像计算配置','物业点评估画像'].includes(tab.tabName))
            }*/
            const mainTypeOption = []
            const fieldList = res.data.reduce((res, item) => {
              if (!res[item.dim]) {
                res[item.dim] = []
                mainTypeOption.push({
                  label: item.dim_title,
                  value: item.dim
                })
              }
              res[item.dim].push({
                fieldName: item.field_name,
                fieldTitle: item.dim_field,
                mappingFieldType: item.field_type
              })
              return res
            }, {})

            tab.field = {
              mainTypeOption,
              fieldList
            }
          })
        });
      },
      async runTask(row){
        this.curRowId = row.id
        const [res, err] = await to(PlanSchemeApi.runTask(row.id))
        if(res){
          this.msgSuccess("运行成功");
          this.getList()
        }
        this.curRowId = null
      },
      runTaskLog(row){
        this.curRow = row
        this.getLogList()
        this.dialogVisibleLog = true
      },
      getLogList() {
        this.logLoading = true
        const code = "PLAN_SCHEME_COMPUTE_" + this.curRow.id
        commonQueryByPost('queryExecLog', {...this.logParams, code: code}).then(res => {
          this.logList = res.data.map(item => {
            const children = item.children?.value ? JSON.parse(item.children.value) : [];
            return {
              ...item,
              children: children
            }
          })
          this.logTotal = res.total
          this.logLoading = false
        })
      },
      viewComputeResult(row){
        let tabs = []
        for(let tab of this.tabs){
          tabs.push({
            label: tab.label,
            code: tab.queryCode,
            expCsv: true,
            showAdd: false,
            expExecl: true,
            showEdit: false,
            showCov: false,
            popupEdit: true,
            exportCode: tab.exportCode,
            showDelete: false,
            showDetail: true,
            showExport: true,
            showBatchDel: false,
            showSearchBtn: false,
            exportCodeType: "config",
            showBatchDelAll: false,
            exportButtonName: "数据导出",
            currentCodeExportRecord: true,
            isCurrentUserFieldShow: true,
            copyRowEnable: false,
            ingorePermission: true,
            queryParams:{
              engineeringId: this.engineeringData.id,
              schemeId: row.id,
              planDim: tab.label
            }
          })
        }
        const tmpRoute = {
          path: `/projectManager/${this.engineeringData.id}/tabPanePreview/${row.id}`,
          name: "tabPanePreview",
          openType: "2",
          component: "devManager/queryConfig/preview/tabPane.vue",
          meta: {
            engineeringId: this.engineeringData.id,
            title: `${this.engineeringData.name} / ${row.schemeName}`,
            cache: false,
            icon: "table",
            menuParams: {
              tabs: JSON.stringify(tabs)
            }
          },
          hidden: true,
        }
        this.$store.dispatch('addTmpRoute', {route: tmpRoute})
        // 导航到 DataManagement 组件
        const route = localStorage.getSessionStorage('moduleRoute')
        if (!route.children.some(r => r.path === tmpRoute.path)) {
          route.children.push(tmpRoute)
          localStorage.putSessionStorage('moduleRoute', route)
        }
        this.$router.push({path: tmpRoute.path}).then(() => this.setCurrentSiderbar(route))
      },
      checkCanCompute(){
        for(let nodeType of ['siteInfoDrawing','propertyDrawing']){
          const nodeSchemeConfig = this.form.schemeConfig[nodeType]
          if(nodeSchemeConfig.preview){
            return true
          }
        }
        return false
      },
      /** 提交按钮 */
      async submitForm(compute) {
        const valid = await this.$refs["form"].validate();
        if (!valid) return

        const requires = document.querySelectorAll('.yn-rule-input-require')
        if(requires.length > 0){
          this.$message.error('请完善[物理站/物业点]规则必填项')
          return
        }

        // this.buildRuleTag();

        if(compute && !this.checkCanCompute()){
          this.$message.error('物理站/物业点至少配置一个有效规则才能计算！')
          return
        }

        this.saveLoading = true
        if (this.form.id != null) {
          if(this.isCopy){
            const [res, err] = await to(PlanSchemeApi.copy(this.form,compute))
            if(res){
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }
          }else{
            const [res, err] = await to(PlanSchemeApi.update(this.form,compute))
            if(res){
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            };
          }
        } else {
          const [res, err] = await to(PlanSchemeApi.add(this.form,compute))
          if(res){
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }
        }
        this.saveLoading = false

      },
      /** 删除按钮操作 */
      handleDelete(row) {
        this.$confirm('是否确认删除规划需求方案[' + row.schemeName + ']的数据项?', "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(function() {
            return PlanSchemeApi.delete(row.id);
          }).then(() => {
            this.getList();
            this.msgSuccess("删除成功");
          })
      },
      generatePre(){
        const ruleCondition = this.form.schemeConfig[this.activeTab].ruleInfo
        this.form.schemeConfig[this.activeTab].preview = this.recursionRuleSql(ruleCondition)
        this.form.schemeConfig[this.activeTab].pseudoCode = this.recursionRulePseudoCode(ruleCondition)
        // 标签生成sql
        this.form.schemeConfig[this.activeTab].labelCase = this.recursionLabelSql(ruleCondition)
      },
      recursionRulePseudoCode(config){
        let conditions = ''
        conditions = config.items.map(item => {
          if(item.type === 'group'){
            return this.recursionRulePseudoCode(item)
          }else{
            let value = item.value;
            if(item.formType == 'select' && value){
              if(Array.isArray(value)){
                if(['~','!~'].includes(item.operator)){
                  value =  value.join('|')
                  value = `'${value}'`
                }else{
                  value = '( ' + value.map(val => `'${val}'`).join(',') + ' )'
                }
              }else{
                value = `'${value}'`
              }
            }

            const operator = this.operators.find(oper => oper.value == item.operator)
            return `${item.fieldTypeName}.${item.fieldTitle} ${operator.label} ${value}`
          }
        }).join(` ${config.rule == 'and' ? '且' :'或'} `)
        if(conditions){
          conditions = ` ( ${conditions} ) `
        }
        return conditions
      },
      recursionRuleSql(config){
        let conditions = ''
        conditions = config.items.map(item => {
          if(item.type === 'group'){
            item.condition = this.recursionRuleSql(item)
            return item.condition
          }else{
            const operator = item.operator.replace(/_/g, ' ')
            let value = item.value;
            if(item.formType == 'select' && value){
              if(Array.isArray(value)){
                if(['~','!~'].includes(item.operator)){
                  value =  value.join('|')
                  value = `'${value}'`
                }else{
                  value = '( ' + value.map(val => `'${val}'`).join(',') + ' )'
                }
              }else{
                value = `'${value}'`
              }
            }
            item.condition = `${item.field} ${operator} ${value}`
            return item.condition
          }
        }).join(` ${config.rule} `)
        if(conditions){
          conditions = ` ( ${conditions} ) `
        }
        return conditions
      },
      recursionLabelSql(config, labels = '', conditions = '', labelCase = [], detailCase = []) {
        // 获取公共参数
        const commonConditions = (config.items?.filter(item => item.type !== 'group' || (item.rule === 'and' && !item.name))
          .map(item => item.condition) || []).join(' and ')
        conditions = [conditions, commonConditions].filter(o => o).join(' and ')
        if (config.name) {
          // 存在子集时, 删除上级
          if (labels) {
            const index = labelCase.findIndex(l => l.endsWith(`then '${labels}' END, NULL)`))
            if (index > -1) {
              labelCase.splice(index, 1)
              detailCase.splice(index, 1)
            }
          }
          labels = [labels, config.name].filter(o => o).join('-')
          labelCase.push(`NULLIF(CASE when ${conditions} then '${labels}' END, NULL)`)
          detailCase.push(`NULLIF(CASE when ${conditions} then '${labels}：${this.recursionRulePseudoCode(config).replaceAll("'", '"')}' END, NULL)`)
        }
        config.items?.forEach(item => {
          if (item.type === 'group') {
            this.recursionLabelSql(item, labels, conditions, labelCase, detailCase)
          }
        })
        return `TRIM( BOTH ',' FROM CONCAT_WS(';\n', ${labelCase.reverse().join(', ')})) as pr_tag, TRIM( BOTH ',' FROM CONCAT_WS(';\n', ${detailCase.reverse().join(',')})) as pr_tag_rule`
      },
      findRuleTag(config,tagName){
        if(!config.items) return
        for(let node of config.items){
          if(node.type === 'group' && node.rule == 'and' && node.name == tagName){
            return node
          }
          const found = this.findRuleTag(node,tagName);
          if(found) return found
        }
        return null
      },
      buildRuleTag(){
        for(let nodeType of ['siteInfoDrawing','propertyDrawing']){
          const nodeSchemeConfig = this.form.schemeConfig[nodeType]
          let tagRules = []
          for(let tag of nodeSchemeConfig.groupTags){
             const ruleTagCondition = this.findRuleTag(nodeSchemeConfig.ruleInfo,tag)
             if(ruleTagCondition){
              const pseudoCode = this.recursionRulePseudoCode(ruleTagCondition)
              tagRules.push(`${tag}：${pseudoCode}`)
             }
          }
          nodeSchemeConfig.tagRules = tagRules.join(',\n')
        }

      },
      // 高亮括号函数
      highlightBrackets(type) {
        let code = this.form.schemeConfig[this.activeTab][type];

        const stack = [];
        const bracketTypes = {'(': ')'};
        const closeBrackets = {')': '('};
        const colors = ['bracket-1', 'bracket-2', 'bracket-3', 'bracket-4', 'bracket-5', 'bracket-6', 'bracket-7', 'bracket-8'];
        const keywords = ['AND', 'OR', 'LIKE', 'IN', 'BETWEEN', 'IS NULL', 'IS NOT NULL', 'NOT IN', 'NOT LIKE'];

        let highlightedCode = '';
        for (let i = 0; i < code.length; i++) {
            const char = code[i];
            const nextChar = code[i + 1];

            // 处理左括号
            if (bracketTypes[char]) {
                const depth = stack.length;
                const colorClass = colors[depth % colors.length];
                stack.push({ char, depth });
                highlightedCode += `<span class="${colorClass}">${char}</span>`;
            }
            // 处理右括号
            else if (closeBrackets[char]) {
                const lastBracket = stack.pop();
                if (lastBracket && lastBracket.char === closeBrackets[char]) {
                    const colorClass = colors[lastBracket.depth % colors.length];
                    highlightedCode += `<span class="${colorClass}">${char}</span>`;
                } else {
                    // 不匹配的括号
                    highlightedCode += `<span class="text-red-500">${char}</span>`;
                }
            }
            else {
                highlightedCode += char
            }
        }

        // 高亮关键字
        keywords.forEach(keyword => {
          const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
          highlightedCode = highlightedCode.replace(regex, `<span class="sql-keyword">${keyword}</span>`);
        })

        return highlightedCode
      }
    }
  }
  </script>

<style scoped lang="scss">
$dark-bg: #2c3e50;

.sql-preview {
  background: $dark-bg;
  color: #e0e0e0;
  font-family: 'Fira Code', 'Courier New', monospace;
  padding: 10px;
  border-radius: 8px;
  margin-top: 20px;
  max-height: 400px;
  overflow: auto;
  line-height: 1.6;
  box-shadow: inset 0 0 10px rgba(0,0,0,0.5);

  ::v-deep{
    .bracket-1 { color: #FF5252;}
    .bracket-2 { color: #2196F3; }
    .bracket-3 { color: #FF9800; }
    .bracket-4 { color: #9C27B0; }
    .bracket-5 { color: #FFEB3B; }
    .bracket-6 { color: #8BC34A; }
    .bracket-7 { color: #E91E63; }
    .bracket-8 { color: #00BCD4; }

    .sql-keyword {
      color: #f92672;
      font-weight: bold;
    }
  }
}



</style>
