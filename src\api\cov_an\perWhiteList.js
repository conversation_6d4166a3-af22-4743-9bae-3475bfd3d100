import request from '@/utils/request'

// 查询白名单列表
export function listPerWhiteList(query) {
  return request({
    url: '/an/perWhiteList/list',
    method: 'get',
    params: query
  })
}

// 查询白名单详细
export function getPerWhiteList(id) {
  return request({
    url: '/an/perWhiteList/' + id,
    method: 'get'
  })
}

// 新增白名单
export function addPerWhiteList(data) {
  return request({
    url: '/an/perWhiteList',
    method: 'post',
    data: data
  })
}

// 修改白名单
export function updatePerWhiteList(data) {
  return request({
    url: '/an/perWhiteList',
    method: 'put',
    data: data
  })
}

// 删除白名单
export function delPerWhiteList(id) {
  return request({
    url: '/an/perWhiteList/' + id,
    method: 'delete'
  })
}