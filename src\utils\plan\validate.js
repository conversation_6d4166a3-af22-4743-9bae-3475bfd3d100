// 工单处理表单数据校验
import { deepClone, isJsonResolve } from '@/utils'
import { transDbBackStr } from './handle'
import { isNull, isCurrentProvince } from '@/utils/common'
import { commonQuery, commonQueryByPost } from '@/api/kernel/query'
import { arrayGroupBy } from '../common'
import { useGetters } from '../useMappers'
import store from '@/store'

// 校验enodebId
async function enodebIdRangeValid(fields, planSite) {
  const validFieldName = 'enodebId'
  const currentField = fields?.enodeb?.find(
    (field) => field.name === validFieldName
  )

  if (currentField && currentField.required && currentField.formEdit) {
    const data = await commonQuery('qryEnodebValidator', {
      type: 'enodebId',
      city: planSite.xqSite.city,
      area: planSite.xqSite.area
    })
    if (data?.data.length) {
      const validResult = []
      planSite.siteList.forEach((scheme, schemeIndex) => {
        scheme.enodebList.forEach((enodeb, enodebIndex) => {
          const result = {
            fields: [{ name: validFieldName, title: currentField.title }],
            title:
              getValidTitle('scheme', schemeIndex) +
              '--' +
              getValidTitle('enodeb', enodebIndex)
          }

          const ranges = data.data
            .filter((i) => i.network_standard === enodeb.networkStandard)
            .map((i) => [i.enodeb_id_begin, i.enodeb_id_end])

          if (ranges.length) {
            const isRange = withInRangeValid(enodeb[validFieldName], ranges)
            if (!isRange) {
              result.fields[0].message = [
                `不在${ranges.map((i) => `[${i[0]}, ${i[1]}]`)}范围内`
              ]
              validResult.push(result)
            }
          }
        })
      })
      return validResult
    }
  }
}

// cellTac 校验
async function cellTacRangValid(fields, planSite) {
  const validFieldName = 'cellTac'
  const currentField = fields?.cell?.find(
    (field) => field.name === validFieldName
  )
  if (currentField && currentField.required && currentField.formEdit) {
    const data = await commonQuery('qryEnodebValidator', {
      type: 'tac',
      city: planSite.xqSite.city,
      area: planSite.xqSite.area
    })

    if (data?.data.length) {
      const validResult = []
      planSite.siteList.forEach((scheme, schemeIndex) => {
        scheme.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.cellList.forEach((cell, cellIndex) => {
            const result = {
              fields: [{ name: validFieldName, title: currentField.title }],
              title:
                getValidTitle('scheme', schemeIndex) +
                '--' +
                getValidTitle('enodeb', enodebIndex) +
                '--' +
                getValidTitle('cell', cellIndex)
            }

            const ranges = data.data
              .filter((i) => i.network_standard === cell.networkStandard)
              .map((i) => [i.enodeb_id_begin, i.enodeb_id_end])

            if (ranges.length) {
              const isRange = withInRangeValid(cell[validFieldName], ranges)
              if (!isRange) {
                result.fields[0].message = [
                  `不在${ranges.map((i) => `[${i[0]}, ${i[1]}]`)}范围内`
                ]
                validResult.push(result)
              }
            }
          })
        })
      })
      return validResult
    }
  }
}

async function repeatFieldsValueValid(fields, { xqSite, siteList }) {
  const params = {
    siteIdCmcc: xqSite.xqSiteIdCmcc,
    enodebList: [],
    baseBandList: [],
    cellList: [],
    antennaList: []
  }
  siteList.forEach((scheme, schemeIndex) => {
    const schemeTitle = getValidTitle('scheme', schemeIndex)

    scheme.enodebList.forEach((enodeb, enodebIndex) => {
      const enodebTitle = getValidTitle('enodeb', enodebIndex)
      params.enodebList.push(
        handleTopLevelList(enodeb, {
          validKey: 'enodebList',
          validTitle: schemeTitle + '--' + enodebTitle
        })
      )

      enodeb.baseBandList.forEach((baseBand) => {
        const baseBandTitle = getValidTitle('baseBand', '', baseBand.subType)
        params.baseBandList.push(
          handleTopLevelList(baseBand, {
            enodebName: enodeb.enodebName,
            validKey: 'baseBandList',
            validTitle: schemeTitle + '--' + enodebTitle + '--' + baseBandTitle
          })
        )
      })

      enodeb.cellList.forEach((cell, cellIndex) => {
        const cellTitle = getValidTitle('cell', cellIndex)
        params.cellList.push(
          handleTopLevelList(cell, {
            validKey: 'cellList',
            validTitle: schemeTitle + '--' + enodebTitle + '--' + cellTitle
          })
        )

        cell.rruList.forEach((rru, rruIndex) => {
          const rruTitle = getValidTitle('rru', rruIndex)

          rru.antennaList.forEach((antenna, antennaIndex) => {
            const antennaTitle = getValidTitle('antenna', antennaIndex)
            params.antennaList.push(
              handleTopLevelList(antenna, {
                validKey: 'antennaList',
                validTitle:
                  schemeTitle +
                  '--' +
                  enodebTitle +
                  '--' +
                  cellTitle +
                  '--' +
                  rruTitle +
                  '--' +
                  antennaTitle
              })
            )
          })
        })
      })
    })
  })

  const data = await commonQueryByPost('QUERY_REPEAT_DATA', params)
  if (data?.data?.length) {
    const result = []
    const groupByType = arrayGroupBy(data.data, (o) => o.type)
    const flatFields = Object.values(fields).flat()

    Object.keys(groupByType).forEach((key) => {
      // key包含_则为两个字段对比 没有_则判断是否有重复
      const valid = {
        name: key,
        title: groupByType[key][0].validTitle,
        fields: [{}]
      }
      if (key.includes('_')) {
        const title = flatFields.find(
          (field) => field.name === key.split('_')[0]
        )?.title
        const compareTitle = flatFields.find(
          (field) => field.name === key.split('_')[1]
        )?.title
        if (title && compareTitle) {
          valid.fields[0].title = title
          valid.fields[0].message = [
            `${title}与${groupByType[key].map(
              (i) => i.xq_site_id_cmcc
            )}相同,但对应的${compareTitle}不同, 不能流转`
          ]
        }
      } else {
        const title = flatFields.find((field) => field.name === key)?.title
        if (title) {
          valid.fields[0].title = title
          valid.fields[0].message = [
            `${title}与${groupByType[key].map(
              (i) => i.xq_site_id_cmcc
            )}重复, 不能流转`
          ]
        }
      }
      result.push(valid)
    })
    return result
  }
}

// 后台接口校验
export async function interfaceValid(fields, planSite) {
  let result = []
  result = await enodebIdRangeValid(fields, planSite)

  // TAC/位置区(cellTac) 校验
  const tacvalidResult = await cellTacRangValid(fields, planSite)
  tacvalidResult &&
  (result = result ? [...result, ...tacvalidResult] : tacvalidResult)

  /**
   * 小区名称，ECI CGI相同，不能流转，修改扇区ID自动生成ECI、CGI,提示修改扇区ID。
   * 天线名称校验，重复不能流转
   * 1、校验本地提交数据是否相同
   * 2、校验数据库数据,检验CGI是否有相同
   * 与规划数据库入网阶段中相同ENODEB名称对应的DU设备名称必须相同
   * 与规划数据库入网阶段中相同ENODEB_ID对应的ENODEB名称必须相同
   * 与规划数据库入网阶段中相同ENODEB名称对应的ENODEB_ID必须相同
   * 接口校验  当前工单数据对比 给字段配置唯一性校验
   */
  if (isCurrentProvince('HN')) {
    const repeatValidResult = await repeatFieldsValueValid(fields, planSite)
    repeatValidResult &&
    (result = result ? [...result, ...repeatValidResult] : repeatValidResult)
  }

  return result
}

// 规划库 设计库校验多个机房 经纬度 高度
export function roomListContentValid({ siteList }) {
  const validRoomList = []
  siteList.forEach((scheme, schemeIndex) => {
    const name = `【方案${schemeIndex + 1}】`
    scheme.roomList?.forEach((room, roomIndex) => {
      const cloneRoom = deepClone(room)
      cloneRoom.validName = `${name}【机房${roomIndex + 1}】`
      validRoomList.push(cloneRoom)
    })
  })

  if (validRoomList.length > 1) {
    let message = ''
    const compareKeys = {
      roomLon: '经度',
      roomLat: '纬度',
      roomAltitude: '高度'
    }

    Object.keys(compareKeys).forEach((key) => {
      const compareRoom = validRoomList.map((i) => i[key])
      const compareResult = isRepeatArrayElement(compareRoom, true)
      if (compareResult && compareResult.length) {
        compareResult.forEach((result) => {
          message += `${validRoomList[result[0]].validName}和${
            validRoomList[result[1]].validName
          }：${compareKeys[key]}相同<br />`
        })
      }
    })
    return message
  }
}

// 对比数组元素是否重复
function isRepeatArrayElement(array, filterNull = false) {
  const compareResult = []
  for (let i = 0; i < array.length; i++) {
    for (let k = 0; k < array.length - 1 - i; k++) {
      //当前元素
      const a = array[i]

      //当前要对比的元素
      const b = array[i + 1 + k]

      if (filterNull) {
        if (!isNull(a) && !isNull(b) && a === b) {
          compareResult.push([i, i + 1 + k])
        }
      } else {
        a === b && compareResult.push([i, i + 1 + k])
      }
    }
  }
  return compareResult
}

// 字段配置无法满足 自定义校验
export function fieldCustomValidate(fields, planSite) {
  // 天线的高度取值区间为[地面高度,地面高度+塔身高度]
  const antHight = fields.antenna?.find((field) => field.name === 'antHight')
  if (antHight?.formEdit) {
    const result = []
    planSite.siteList.forEach((scheme, schemeIndex) => {
      scheme.enodebList.forEach((enodeb, enodebIndex) => {
        enodeb.cellList.forEach((cell, cellIndex) => {
          cell.rruList.forEach((rru, rruIndex) => {
            rru.antennaList.forEach((ant, antIndex) => {
              const { antPosition, antPositionNo } = ant
              if (antPosition === 'roof' && antPositionNo) {
                const selectRoof = scheme.roofList[antPositionNo - 1]
                const { roofHight, roofInstallAltitude } = selectRoof
                if (+roofHight && +roofInstallAltitude) {
                  const isRange = withInRangeValid(ant.antHight, [
                    Number(roofHight),
                    Number(roofHight) + Number(roofInstallAltitude)
                  ])

                  if (!isRange) {
                    result.push({
                      fields: [
                        {
                          title: '天线挂高',
                          message: [
                            '天线的高度取值区间为安装位置编号选择的天面：[地面高度,地面高度+塔身高度]'
                          ]
                        }
                      ],
                      name: 'ant',
                      title: `方案${schemeIndex + 1}--基站${
                        enodebIndex + 1
                      }--小区${cellIndex + 1}--射频单元${rruIndex + 1}--天线${
                        antIndex + 1
                      }`
                    })
                  }
                }
              }
            })
          })
        })
      })
    })
    return result
  }
}

// 敏感词校验性能优化配置
const SENSITIVE_WORDS_CONFIG = {
  MAX_FIELD_LENGTH: 1000,        // 字段值最大长度限制
  MAX_DEPTH: 10,                 // 最大递归深度
  MAX_ITEMS_PER_LIST: 100,       // 每个列表最大处理项数
  BATCH_SIZE: 50                 // 批处理大小
}

// Trie树节点类
class TrieNode {
  constructor() {
    this.children = new Map()     // 子节点映射
    this.isEndOfWord = false      // 是否为敏感词结尾
    this.word = null              // 完整的敏感词
  }
}

// Trie树类 - 用于高效敏感词匹配
class SensitiveWordsTrie {
  constructor() {
    this.root = new TrieNode()
    this.wordsCount = 0
  }

  // 插入敏感词到Trie树
  insert(word) {
    if (!word || typeof word !== 'string') return

    let node = this.root
    const lowerWord = word.toLowerCase()

    for (const char of lowerWord) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode())
      }
      node = node.children.get(char)
    }

    if (!node.isEndOfWord) {
      node.isEndOfWord = true
      node.word = word // 保存原始敏感词
      this.wordsCount++
    }
  }

  // 在文本中查找所有敏感词
  findSensitiveWords(text) {
    if (!text || typeof text !== 'string') return []

    const foundWords = new Set() // 使用Set避免重复
    const lowerText = text.toLowerCase()

    // 从每个位置开始尝试匹配
    for (let i = 0; i < lowerText.length; i++) {
      let node = this.root
      let j = i

      // 尝试从当前位置匹配敏感词
      while (j < lowerText.length && node.children.has(lowerText[j])) {
        node = node.children.get(lowerText[j])
        j++

        // 如果找到完整的敏感词
        if (node.isEndOfWord) {
          foundWords.add(node.word)
        }
      }
    }

    return Array.from(foundWords)
  }

  // 检查是否包含敏感词
  containsSensitiveWords(text) {
    return this.findSensitiveWords(text).length > 0
  }

  // 获取Trie树统计信息
  getStats() {
    return {
      wordsCount: this.wordsCount,
      memoryUsage: this.estimateMemoryUsage()
    }
  }

  // 估算内存使用量
  estimateMemoryUsage() {
    const nodeSize = 64 // 估算每个节点的内存占用(字节)
    return this.countNodes() * nodeSize
  }

  // 计算节点总数
  countNodes() {
    const countNodesRecursive = (node) => {
      let count = 1
      for (const child of node.children.values()) {
        count += countNodesRecursive(child)
      }
      return count
    }
    return countNodesRecursive(this.root)
  }
}

// 全局Trie树实例缓存
let sensitiveWordsTrie = null
let lastSensitiveWordsConfig = null

// 获取或创建Trie树实例
function getSensitiveWordsTrie(sensitiveWordsValue) {
  // 如果配置没有变化且Trie树已存在，直接返回缓存的实例
  if (lastSensitiveWordsConfig === sensitiveWordsValue && sensitiveWordsTrie) {
    return sensitiveWordsTrie
  }

  // 解析敏感词
  const sensitiveWords = sensitiveWordsValue
    .split(',')
    .map(word => word.trim())
    .filter(word => word.length > 0)

  if (sensitiveWords.length === 0) {
    return null
  }

  // 创建新的Trie树实例
  sensitiveWordsTrie = new SensitiveWordsTrie()

  // 将所有敏感词插入Trie树
  sensitiveWords.forEach(word => {
    sensitiveWordsTrie.insert(word)
  })

  // 缓存配置
  lastSensitiveWordsConfig = sensitiveWordsValue

  // 输出Trie树统计信息（开发环境）
  if (process.env.NODE_ENV === 'development') {
    const stats = sensitiveWordsTrie.getStats()
    console.log(`[敏感词Trie树] 词汇数量: ${stats.wordsCount}, 预估内存: ${(stats.memoryUsage / 1024).toFixed(2)}KB`)
  }

  return sensitiveWordsTrie
}

// 敏感词校验函数 - Trie树优化版本
function sensitiveWordsValidate(fields, planSite) {
  try {
    // 使用缓存的系统配置，避免网络请求
    const sysconfig = useGetters('sysconfig')()
    const sensitiveWordsValue = sysconfig?.SENSITIVE_WORDS

    if (!sensitiveWordsValue) {
      console.log('没有配置敏感词')
      return [] // 如果没有配置敏感词，返回空数组
    }

    // 获取或创建Trie树实例
    const trie = getSensitiveWordsTrie(sensitiveWordsValue)
    if (!trie) {
      return []
    }

    const result = []

    // 定义需要进行敏感词校验的字段类型
    const sensitiveFieldTypes = [
      'input',      // 输入框
      'textarea',   // 文本域
      'text'
      // 'select',     // 下拉选择 - 暂时不校验
      // 'radio',      // 单选框 - 暂时不校验
      // 'checkbox',   // 复选框 - 暂时不校验
    ]

    // 检查字段是否需要进行敏感词校验
    const shouldValidateField = (fieldConfig, value) => {
      if (!fieldConfig || !value) return false

      // 字段值长度限制，避免处理过长的字符串
      if (value.length > SENSITIVE_WORDS_CONFIG.MAX_FIELD_LENGTH) {
        console.warn(`字段 ${fieldConfig.name} 值过长(${value.length}字符)，跳过敏感词校验`)
        return false
      }

      // 检查字段类型是否在需要校验的类型列表中
      const fieldType = fieldConfig.formType || fieldConfig.type || 'input'
      // console.log('field.name',fieldConfig.fieldName, fieldConfig.formType)
      return sensitiveFieldTypes.includes(fieldType)
    }

    // 批量检查敏感词，使用Trie树提升性能
    const batchCheckSensitiveWords = (items) => {
      const batchResults = []

      for (let i = 0; i < items.length; i += SENSITIVE_WORDS_CONFIG.BATCH_SIZE) {
        const batch = items.slice(i, i + SENSITIVE_WORDS_CONFIG.BATCH_SIZE)

        batch.forEach(({ value, fieldTitle, parentTitle, key }) => {
          // 使用Trie树快速查找敏感词
          const foundSensitiveWords = trie.findSensitiveWords(value)

          if (foundSensitiveWords.length > 0) {
            batchResults.push({
              fields: [{
                name: key,
                title: fieldTitle,
                message: [`包含敏感词: ${foundSensitiveWords.join(', ')}`]
              }],
              name: 'sensitiveWords',
              title: parentTitle || '敏感词校验'
            })
          }
        })
      }

      return batchResults
    }

    // 优化的递归检查函数 - 限制深度和数量
    const checkSensitiveWords = (data, parentTitle = '', currentFields = null, depth = 0) => {
      if (!data || typeof data !== 'object' || depth > SENSITIVE_WORDS_CONFIG.MAX_DEPTH) {
        return
      }

      const pendingChecks = [] // 收集待检查的项目，用于批处理

      Object.entries(data).forEach(([key, value]) => {
        if (key.includes('List') && Array.isArray(value)) {
          // 处理列表数据 - 限制处理数量
          const childKey = key.slice(0, -4)
          const limitedValue = value.slice(0, SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST)

          if (value.length > SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST) {
            console.warn(`列表 ${key} 项目过多(${value.length}项)，仅处理前${SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST}项`)
          }

          limitedValue.forEach((item, index) => {
            const itemTitle = parentTitle ? `${parentTitle}--${getValidTitle(childKey, index)}` : getValidTitle(childKey, index)
            checkSensitiveWords(item, itemTitle, getCurrentKeyFields(fields, childKey, item), depth + 1)
          })
        } else if (typeof value === 'string' && value.trim()) {
          // 查找字段配置信息 - 确保 currentFields 是数组
          const fieldConfig = Array.isArray(currentFields)
            ? currentFields.find(field => field.name === key)
            : null

          // 只对指定类型的字段进行敏感词校验
          if (shouldValidateField(fieldConfig, value)) {
            const fieldTitle = fieldConfig?.title || key

            // 添加到批处理队列
            pendingChecks.push({
              value,
              fieldTitle,
              parentTitle,
              key
            })
          }
        } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 递归检查嵌套对象
          checkSensitiveWords(value, parentTitle, currentFields, depth + 1)
        }
      })

      // 批量处理敏感词检查
      if (pendingChecks.length > 0) {
        const batchResults = batchCheckSensitiveWords(pendingChecks)
        result.push(...batchResults)
      }
    }

    // 检查基础信息
    if (planSite.xqSite) {
      checkSensitiveWords(planSite.xqSite, '基本信息', fields.XQJD_site)
    }

    // 检查站点列表 - 限制处理数量
    if (planSite.siteList && Array.isArray(planSite.siteList)) {
      const limitedSiteList = planSite.siteList.slice(0, SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST)

      if (planSite.siteList.length > SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST) {
        console.warn(`站点列表过多(${planSite.siteList.length}个)，仅处理前${SENSITIVE_WORDS_CONFIG.MAX_ITEMS_PER_LIST}个`)
      }

      limitedSiteList.forEach((scheme, schemeIndex) => {
        const schemeTitle = getValidTitle('scheme', schemeIndex)
        // 获取 scheme 对应的字段配置
        const schemeFields = getCurrentKeyFields(fields, 'scheme', scheme)
        checkSensitiveWords(scheme, schemeTitle, schemeFields)
      })
    }

    // console.log('result',result)
    return result
  } catch (error) {
    console.error('敏感词校验失败:', error)
    return []
  }
}

// 字段配置校验
export function fieldConfigValidate(fields, planSite, inResource) {
  if (fields && planSite) {
    // 校验xqSite && siteList 分开校验
    // xqSite 获取表单字段和list的key不对应 同时planSite存的数据不止表单数据
    const result = []
    let jump = []

    if (inResource) {
      // 需求库时 校验基础信息 并且site校验跳过scheme、rru
      jump = ['scheme', 'rru']
      const xqSite = deepClone(planSite.xqSite)
      const valid = fieldsRequiredValid(fields.XQJD_site, xqSite)
      result.push({ fields: valid, name: 'basic', title: '基本信息' })
    }
    //新站字段校验
    const siteValid = siteFieldsValid(fields, planSite.siteList, jump)

    // 添加敏感词校验 - 现在是同步的，性能更好
    const sensitiveWordsValid = sensitiveWordsValidate(fields, planSite)

    return [...result, ...siteValid, ...sensitiveWordsValid].filter((i) => i.fields.length)
  }
}

function siteFieldsValid(fields, data, jump) {
  let result = []
  data?.forEach((scheme, schemeIndex) => {
    const siteCode = 'scheme'
    const validData = deepClone(scheme)
    const validMessage = {
      name: siteCode,
      title: jump.includes(siteCode) ? '逻辑基站信息' : getValidTitle(siteCode, schemeIndex)
    }
    const valid = recursivePlanList(fields, validData, siteCode, validMessage, jump)

    // valid 校验结果重新排序
    const order = Object.keys(fieldGroupTitleMap)
    valid.sort((a, b) => {
      return order.indexOf(a.name) - order.indexOf(b.name)
    })

    result = [...result, ...valid]
  })
  return result
}

// jump:跃级 -- 递归校验时跳过某一层级
function recursivePlanList(fields, data, validKey, parentTitle, jump) {
  // list和字段数据分开校验 初始化时还没有生成数据 以字段为准
  // 同时避免多次遍历字段
  let result = []
  const validPlanList = {}
  Object.entries(data).forEach(([key, value] = item) => {
    if (key.includes('List')) {
      const childKey = key.slice(0, -4)

      if (value && Array.isArray(value)) {
        // 跃级 list有数据 但是没有字段 跳过这一级
        if (jump?.includes(childKey)) {
          validPlanList[key] = value
        } else {
          getCurrentKeyFields(fields, childKey, data) && (validPlanList[key] = value)
        }
      }
      delete data[key]
    }
  })
  const childType = validTitleSubType[validKey] ? data[validTitleSubType[validKey]] : ''
  const currentFields = getCurrentKeyFields(fields, validKey, data, childType)
  if (currentFields) {
    const valid = fieldsRequiredValid(currentFields, data)
    result.push({ fields: valid, name: validKey, title: parentTitle.title })
  }

  if (Object.keys(validPlanList).length) {
    Object.entries(validPlanList).forEach(([key, value] = item) => {
      const childKey = key.slice(0, -4)
      value.forEach((child, childIndex) => {
        child.parent = getParentData(value)
        const type = validTitleSubType[childKey] ? child[validTitleSubType[childKey]] : ''
        const title = parentTitle.title + (jump && jump.includes(childKey) ? '' : '--' + getValidTitle(childKey, childIndex, type))
        const valid = recursivePlanList(fields, child, childKey, { title }, jump)
        result = [...result, ...valid]
      })
    })
  }

  return result
}

// 复制parent信息 避免对象内容过多 只复制一层
function getParentData(value) {
  const data = []
  value?.forEach((item) => {
    const copyData = {}
    Object.keys(item).forEach((key) => {
      if (!key.includes('List')) {
        copyData[key] = item[key]
      }
    })
    data.push(copyData)
  })
  return data
}

// 处理list传递参数
function handleTopLevelList(json, addObj) {
  const result = {}
  Object.keys(json).forEach((key) => {
    if (!key.includes('List')) {
      result[key] = json[key]
    }
  })

  if (addObj && typeof addObj === 'object') {
    // 简单判断
    Object.assign(result, addObj)
  }
  return result
}

// 获取字段
function getCurrentKeyFields(fields, key, data, type) {
  // du cu 机架、传输配置、配套 板卡、端口特殊
  let currentKey = key
  const currNodeId = store.state.plan.currentNodeId
  if (key === 'transferNetwork') {
    currentKey = 'transfer_network'
  } else if (key === 'transferConfig') {
    // 数据还是list的时候没有type 任意返回一个传输配置的类型
    currentKey = typeof type === 'undefined' ? 'transfer_config_transfer_control_plane' : 'transfer_config_' + type
  } else if (['baseBand', 'peitao'].includes(key)) {
    currentKey = 'equipment_' + key
  } else if (['board', 'port'].includes(key)) {
    currentKey = data.equipment?.toLowerCase() === 'rru' ? key + '_rru' : key + '_baseBand'
  } else if ('bbuExtendDevice' === key) {
    currentKey = 'extend_device_baseband'
  } else if ('rruExtendDevice' === key) {
    currentKey = 'extend_device_rru'
  } else if ('enodeb' === key && data && data['sort'] == 0 && currNodeId === 'jt3b453adde06a4772b465c4d6fb1ccc4e') {//只有需求库的基站1需特殊处理
    const enodebField = store.state.plan.enodebFirstFields//需求阶段基站1字段配置，在FormArea.vue中的getFormFields()设置
    return enodebField
  }
  return fields[currentKey]
}

// 校验字段的required && jsValid
export function fieldsRequiredValid(fields, data) {
  const validResult = []
  fields?.forEach((field) => {
    const {
      name,
      title,
      formShow,
      initHide,
      formEdit,
      param5,
      required,
      jsValid,
      conditionRequired,
      formType,
    } = field
// 解析 param2 检查隐藏条件
// 检查是否有其他字段的 param2 隐藏了当前字段
    let isHiddenByParam2 = false
    fields.forEach((otherField) => {
      if (otherField.param2) {
        try {
          const param2Obj = JSON.parse(otherField.param2)
          const selectedValue = data[otherField.name] // 获取其他字段的当前值
          if (param2Obj[selectedValue] && param2Obj[selectedValue].hide) {
            const hiddenFields = param2Obj[selectedValue].hide.split(',').map((f) => f.trim())
// 如果当前字段在隐藏列表中
            if (hiddenFields.includes(name)) {
              isHiddenByParam2 = true
            }
          }
        } catch (e) {
          console.error(`解析字段 ${otherField.name} 的 param2 失败:`, e)
        }
      }
    })
    // console.log('selectedValue',title,name,data.resourceCode,data.antReuse,isHiddenByParam2,required,formShow,formEdit,!initHide,!isHiddenByParam2)
    /**
     * 对于新站，如果开启编辑且配置了校验规则，则校验；
     * 对于增删调，如果是新增且param5为true且配置了校验规则，则校验
     * 仅当 formShow 为 true、未被 initHide 隐藏且未被 param2 隐藏时进行校验
     * 仅字段必须满足条件显示，显示，编辑，再根据js判断是否符合条件，否则根据required字段判断是否必填，required字段不在任何回调方法中更改
     **/
    if (((formShow && formEdit && !isHiddenByParam2 ) || (data.modify === 'add' && param5))) {
      const result = { name, title }
      result.errorType = 'normal'

      if (required && conditionRequired && isJsonResolve(conditionRequired)) {
        const config = JSON.parse(conditionRequired)
        eval(config.rule) && isNull(data[name]) && (result.message = [config.tipMsg])
      } else {
        required && isNull(data[name]) && (result.required = true)
      }
// 数据类型校验
      if (formType === 'number' && !isNull(data[name])) {
        const value = data[name]
        const numberRegex = /^(?:-?\d+(?:\.\d+)?)$/ // 匹配整数和浮点数 (包括正负数)
        if (!numberRegex.test(value)) {
          result.message = [`${title}必须为数字`]
          result.errorType = 'formTypeError'
        }
      }


// js正则校验
      jsValid && !isNull(data[name]) && (result.message = fieldJsValid(jsValid, field, data))
      validResult.push(result)
    }
  })
  return validResult.filter((item) => item.required || item.message?.length)
}

function fieldJsValid(jsValid, field, data) {
  const result = []
  try {
    const validConfig = JSON.parse(jsValid)

    if (Array.isArray(validConfig)) {
      validConfig.forEach((config) => {
        const validResult = configItemsValid(field, data, config)
        validResult && result.push(validResult)
      })
    }
  } catch (e) {
    console.log('fieldJsValid', jsValid, field, data)
  }
  return result
}

function configItemsValid(field, data, valid) {
  try {
    let result = ''
    const value = data[field.name]
    const { type, body, relevance, message } = valid

    switch (type) {
      case 'reg': {
        // \需要转义
        const reg = new RegExp(body)
        !reg.test(value) && (result = message)
      }
        break
      case 'range': {
        // body: [[0,200],[500,1000]]
        // 区间校验 先判断填写的是数字 再比较

        const validBody =
          typeof body === 'string' ? JSON.parse(transDbBackStr(body)) : body
        const rangeValidResult = withInRangeValid(value, validBody)
        !rangeValidResult && (result = message)
      }
        break
      case 'relevance': {
        // body reg: { key1: reg1, key2: reg2}
        // body range: { key1: [0,1], key2: [1,2]}

        if (relevance && !isNull(data[relevance])) {
          const validBody =
            typeof body === 'string' ? JSON.parse(transDbBackStr(body)) : body
          const validData = validBody[data[relevance]]

          if (Array.isArray(validData)) {
            // 关联区间

            const relRangeValid = withInRangeValid(value, validData)
            !relRangeValid && (result = message)
          } else {
            // 关联正则

            const relReg = new RegExp(validData)
            !relReg.test(value) && (result = message)
          }
        }
      }
        break
      case 'only': {
        const { parent } = data
        if (parent && Array.isArray(parent)) {
          if (
            parent.length > 1 &&
            [...new Set(parent.map((i) => String(i[field.name])))].length <
            parent.length
          ) {
            result = message
          }
        }
      }
        break
    }

    return result
  } catch (e) {
    console.log(e, valid, field, data)
  }
}

// 区间校验
function withInRangeValid(value, ranges) {
  if (typeof +value === 'number' && !isNaN(value)) {
    // 如果只有一个范围区间 转为二维数组
    let withInRange = false
    if (ranges?.length) {
      const validData = Array.isArray(ranges[0]) ? ranges : [ranges]
      validData.forEach((range) => {
        if (isRange(range[0], range[1], value)) {
          withInRange = true
        }
      })
    }

    return withInRange
  }
}

function getValidTitle(code, index, type) {
  const sort = isNull(index) || type ? '' : index + 1
  const currentCode = type ? type : code
  const title = currentCode.includes('transfer_')
    ? '传输配置--' + getValidDictionaryTitle('TRANSFER_TABS', currentCode)
    : fieldGroupTitleMap[currentCode]
  return title + sort
}

function isRange(min, max, number) {
  return number >= min && number <= max
}

function getValidDictionaryTitle(code, type) {
  const getDictionary = useGetters('dictionary')
  const getDictionaryName = useGetters('dictionaryName')
  if (getDictionaryName().includes(code)) {
    const tmp = getDictionary()[code]?.find((i) => i.code === type)
    if (tmp && tmp.name) {
      return tmp.name
    } else {
      return ''
    }
  }
  return ''
}


// 从文本中提取出tab的code和index
export function extractTabIndexesFromText(text) {
  const results = []
  if (text === undefined) return//添加判断，解决控制台爆红
  for (const [key, value] of Object.entries(fieldGroupTitleMap)) {
    const regex = new RegExp(`${value}(\\d*)`)
    const match = text.match(regex)

    if (match) {
      let index = 0 // 默认索引为0
      if (match[1]) {
        index = parseInt(match[1], 10) - 1 // 如果有数字，转为索引
      }
      results.push({ code: key, index })
    }
  }

  return results
}

export const fieldGroupTitleMap = {
  basic: '基本信息',
  scheme: '方案',
  room: '机房',
  roof: '天面',
  peitao: '配套',
  enodeb: '基站',
  baseBand: '基带设备',
  DU: 'DU',
  CU: 'CU',
  BBU: 'BBU',
  shelf: '机架',
  platform: '平台',
  lattice: '机框',
  board: '板卡',
  port: '接口',
  cell: '小区',
  rru: '射频单元',
  antenna: '天线',
  bbuExtendDevice: '扩展设备',
  rruExtendDevice: '扩展设备'
}

const validTitleSubType = {
  transferConfig: 'type',
  transferNetwork: 'type',
  baseBand: 'subType'
}
