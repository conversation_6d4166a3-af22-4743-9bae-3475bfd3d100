<template>
  <div id="zj_base_map">
    <el-dialog id="el_dialog_map" :visible.sync="dialogVisible" width="100%" fullscreen  :before-close="handleClose">
      <div id="base" style="width:100%;height:100%;position:absolute;">
        <el-row  style="position: absolute;left: 350px;z-index:999;margin-top: 20px;">
          <el-button icon="el-icon-circle-plus-outline" @click="createLayerList" v-if="!hasCreateLayer">创建图层</el-button>
          <el-button icon="el-icon-error" @click="destroyLayerList" v-if="hasCreateLayer">销毁图层</el-button>
          <el-button icon="el-icon-plus" @click="showLayerList" v-if="!showLayer && hasCreateLayer">展示图层</el-button>
          <el-button icon="el-icon-minus" @click="hiddenLayerList" v-if="showLayer && hasCreateLayer">隐藏图层</el-button>
          <el-button icon="el-icon-folder-add" @click="createToolBar" v-if="!hasCreateToolsBar">工具栏创建</el-button>
          <el-button icon="el-icon-delete" @click="destroyToolBar" v-if="hasCreateToolsBar">工具栏销毁</el-button>
<!--          <el-button icon="el-icon-folder-opened" @click="popup">弹窗展示</el-button>-->
        </el-row>
        <div id="map" :style="style"></div>
        <div id="legend" style="width:180px;height:auto;background-color: #fff; opacity: 1;position: absolute;bottom: 15px;right:5px;z-index: 999; " ></div>
      </div>
    </el-dialog>
    <div :id='state.layerId' style="width: 100%;height: 500px;" ref='layer'></div>
  </div>

</template>

<script>
import { reactive, defineComponent } from '@vue/composition-api'
import { commonQuery } from '@/api/kernel/query'
import * as api from '@/api/site/plan/handle'
import { useGetters, useActions } from '@/utils/useMappers'
import store from "@/store";

export default defineComponent({
  name: 'MapZJ',
  props: {
    id: String,
    centerIcon: {
      type: String,
      default: 'xqjd-sm',
    },
    queryLayerDetail: Boolean, // 鼠标点击后查询详细信息 经纬度||网格||城市区县
    preventInteraction: Boolean, //阻止鼠标点击
  },
  data() {
    return {
      state: reactive({
        cLongitude: '',
        cLatitude: '',
        layerUrl: '',
        gridGeom: '',
        queryDistance: 1000,
        vectorSourceList: [],
        showPopup: false,
        map: null,
        layerId: this.id + '_' + +new Date(),
        layerIdBack:'',
        mapKey: '',//地图的key
        layerGroup: null,
      }),
      dialogVisible: false,
      style: '',
      hasCreateLayer: false,//是否已经创建图层，默认false
      showLayer:true,//是否展示图层
      hasCreateToolsBar: false,//是否创建工具栏，默认未创建
    }
  },
  async created() {
    console.log('创建方法被执行');
    this.initBasicData();
  },

  methods: {
    //设置地图密钥
    async setMapAk() {
      try {
        const result = await api.getMapAk();
        if (result && result.code === 200) {
          this.state.mapKey = result.data.ak;
        }
      } catch (error) {
        console.error('请求地图授权码失败', error);
      }
    },
    async renderLayers() {
       const indexMapEel = document.getElementById(this.state.layerId);
       console.log('indexMapEel:',indexMapEel,',dialogVisible:',this.dialogVisible,',map:',this.state.map);
       if(!indexMapEel && this.dialogVisible===false && this.state.map==null){
         const divDialogMap  = document.createElement("div")
         divDialogMap.setAttribute("id", this.state.layerId);
         divDialogMap.setAttribute("style","width: 100%;height: 500px;");
         divDialogMap.setAttribute("ref","layer");
         const zj_base_map = document.getElementById("zj_base_map");
         zj_base_map.appendChild(divDialogMap);
       }
      if (this.state.map === null) {
        console.log('创建地图，id:',this.state.layerId);
        this.state.map = new CMMap.Map(this.state.layerId, {
          ak: this.state.mapKey,
          center: [this.state.cLongitude, this.state.cLatitude],
          zoom: 10,
          minZoom:5,
          // mapStyle: 'zjimg',
          isShowAllChina:1
        });
        console.log('创建地图完成:',this.state.map);
        const layer = CMMap.WMTSLayer('https://zjgis.zj.chinamobile.com/gisserver/gwc/service/ygistile/WMTSServer');//窗口组件
        this.state.map.addLayer(layer);

        this.state.map.center([120.147873919111, 30.268944042149], 13);
        // //获取地图点击事件的地图坐标
        this.state.map.on("click", this.mapClick);
        //定义图层组
        const layerGroup = new CMMap.LayerGroup();
        this.state.layerGroup = layerGroup;

        //将图层组添加到地图
        this.state.map.addLayer(layerGroup)

        // 添加地图缩放控件，左侧的+-图标
        this.state.map.addZoomControl({ zoomInTitle: '放大', zoomOutTitle: '缩小', position: "topleft" });
        console.log('this.state.map:', this.state.map);
      }
      this.preventInteraction.value && this.setMapCenter(+this.state.cLongitude, +this.state.cLatitude)
      console.log('map:',this.state.map);
    },
    //地图点击事件
    mapClick(e) {
      //全屏模式下点击时不定位中心
      if(this.dialogVisible===true) return
      const lonLat = this.state.map.getLonLat(e);
      // console.log('点击事件被触发,lonLat=',lonLat);
      this.setMapCenter(lonLat[0], lonLat[1]);
      this.queryLayerDetail && this.requestLayerDetail(lonLat[0], lonLat[1]);
    },
    //设置地图中心点
    async setMapCenter(lon, lat) {
      console.log('设置中心点:',lon,lat);
      if (this.state.map == null) {
        await this.renderLayers();
      }
      this.state.map.center([lon, lat])
      this.clearLayers()
      this.centerIcon.value && this.renderCenterPoint(lon, lat)
    },
    renderCenterPoint(lon, lat) {
      const icon = new CMMap.Icon({
        iconUrl: require(`@/assets/layer_images/${this.centerIcon.value}.png`),
        iconSize: [88, 68],
      })
      //初始化点图标
      const marker = CMMap.Marker([lon, lat], { rotationAngle: 0, icon, })
      this.state.map.addLayer(marker)
      this.state.vectorSourceList.push(marker)
    },
    // 绘制标注
    async renderMark(x, y, icon, dist, markProperty) {
      // console.log('x:', x, ',y:', y, ',icon:', icon, ',dist:', dist, ',markProperty:', markProperty);
      const { name, siteIdCmcc } = markProperty
      const size = siteIdCmcc ? [26, 32] : [88, 68]
      const makeIcon = new CMMap.Icon({ iconUrl: icon, iconSize: size, })

      const marker = CMMap.Marker([x, y], { icon: makeIcon })

      if (siteIdCmcc) {
        marker.setAttributes({ siteIdCmcc })

        const htmlContent = `
              <div>${name}</div>
              <el-button style=' background: #1890ff; margin: 0 auto;color: #ffffff;outline: none;border: 0;border-radius: 2px;padding: 6px 12px;font-weight: 400;' @click='selectUsedSite'>选择资源</el-button>`

        marker.bindPopup(htmlContent)
        const property = marker.getAttributes()

        marker.on('click', (e) => {
          this.$emit('mapClick', property)
        })
      } else {
        marker.bindTooltip('<div>' + name + '</div>')
      }
      // console.log('state.map:', this.state.map);
      if (!this.state.map) {
        await this.renderLayers();
      }
      this.state.map.addLayer(marker)
      this.state.vectorSourceList.push(marker)
    },
    // 绘制线条
    async renderLine(points, color) {
      const line = CMMap.Polyline(points, { opacity: 0.6, weight: 2, color })
      this.state.vectorSourceList.push(line);
      if (!this.state.map) await this.renderLayers();
      this.state.map.addLayer(line);
    },
    //清空图层
    clearLayers() {
      this.state.vectorSourceList.forEach((layer) => layer.remove());
      this.state.vectorSourceList = []
    },
    //请求图层详细信息
    async requestLayerDetail(lon, lat) {
      const lonTmp = lon.toFixed(6);
      const latTmp = lat.toFixed(6);
      const params = { lon: lonTmp, lat: latTmp }
      // console.log('返回前端的经纬度:', params);
      this.$emit('callBackDetail', { longitude: lonTmp, latitude: latTmp })

      const belongToARegion = await commonQuery('qryCityAreaByLonLat', params)
      if (belongToARegion?.data?.length) {
        const data = belongToARegion.data[0]
        const detail = {}
        detail.city = data.city
        detail.area = data.area
        detail.cityCompany = data.city_company
        detail.areaCompany = data.area_company
        detail.province = data.province
        data.address && (detail.address = data.address)
        data.name && (detail.siteNameCmcc = data.name)
        this.$emit('callBackDetail', detail)
      }

      const grid = await commonQuery('qryGridByLonLat', Object.assign({}, params, { dist: this.state.queryDistance }),)
      grid?.data.length && this.$emit('callBackDetail', { grid: grid?.data[0].gridid })
    },
    //创建图层列表
    createLayerList() {
      CMMap.componentInit();
      CMMap.createWYLayerList({ id: 'layerList' });
      const layerListDiv = document.getElementById('layerList')
      layerListDiv.style.cssText = 'z-index:6000;position:absolute;width:300px;left:90px;top:250px;background: white;';
      this.hasCreateLayer = true;
      this.showLayer=true;//创建完毕默认展示
    },
    //销毁图层列表
    destroyLayerList() {
      const layerListDiv = document.getElementById('layerList');// CMMap.destroyWYLayerList({ id: 'layerList' });
      if(layerListDiv) layerListDiv.remove();
      this.hasCreateLayer = false;
    },
    //显示图层列表
    showLayerList() {
      CMMap.showWYLayerList({ id: 'layerList', show: true });
      this.showLayer = true;
    },
    //隐藏图层列表
    hiddenLayerList() {
      CMMap.showWYLayerList({ id: 'layerList', show: false });
      this.showLayer = false;
    },
    //工具兰创建
    createToolBar() {
      CMMap.componentInit();
      CMMap.createMapToolBar({ id: 'mapTool' });
      this.hasCreateToolsBar = true;
    },
    //工具栏销毁
    destroyToolBar() {
      CMMap.destroyMapToolBar({ id: 'mapTool' });
      this.hasCreateToolsBar = false;
    },
    //弹窗展示
    async popup() {
      this.state.showPopup = !this.state.showPopup;
      await this.createLayerList();
      CMMap.setWYLayerInfoPopup({ show: this.state.showPopup });
    },
    // 全屏
    async setMapToFullScreen() {
      const height = $(window).height() -100;
      this.style = 'margin: auto;width: 100%; min-height:' + height +'px; height: ' + height + 'px;'
      this.state.map = null;
      //移除主页的dom，否则无法创建模态框的地图
      if(this.state.layerId && this.state.layerId !== 'map'){
        this.state.layerIdBack = this.state.layerId
        const div = document.getElementById(this.state.layerId);
        if(div) div.remove();
      }
      this.dialogVisible = true;//必须在initBasicData前执行，否则没有加载style会导致无法显示地图
      this.state.layerId = 'map';
      this.initBasicData();
    },
    //模态框关闭时触发
    async handleClose() {
      this.destroyLayerList();
      this.dialogVisible = false;
      const dialog_map = document.getElementById('map');
      if(dialog_map) dialog_map.remove();
      await this.insertDomChildrenNode();//还原div元素,下一次打开模态框时能正常显示

      this.state.map = null;
      this.state.layerId = this.state.layerIdBack;
      await this.initBasicData();
      // this.$forceUpdate();
      // console.log('zj_map_dialog_div:',zj_map_dialog_div,' ,主页地图元素是否存在：',document.getElementById(this.state.layerId));
    },
    async initBasicData (){
      const sysconfig = useGetters('sysconfig')
      const { CURRENT_LATITUDE, CURRENT_LONGITUDE, CURRENT_PROVINCE_CODE, zheJiangMapConf } = sysconfig();
      this.state.cLongitude = CURRENT_LONGITUDE
      this.state.cLatitude = CURRENT_LATITUDE
      if (CURRENT_PROVINCE_CODE && CURRENT_PROVINCE_CODE !== '') {
        this.state.gridGeom = CURRENT_PROVINCE_CODE + '_grid.json'
      } else {
        this.state.gridGeom = 'grid_gcj02_china.json'
      }
      await this.setMapAk();
      await this.renderLayers();
      window.selectUsedSite = () => this.$emit('selectUsedSite');
      window.map = this.state.map;//设置全局属性，否则云地图部分功能无法使用
    },
    //添加子节点
    insertDomChildrenNode() {
      // //移除元素后必须添加
      const divDialogMap  = document.createElement("div")
      divDialogMap.setAttribute("id", "map");
      divDialogMap.setAttribute("style",this.style);
      const base = document.getElementById("base");
      base.appendChild(divDialogMap);
      // console.log('zj_map_dialog_div:',zj_map_dialog_div);
    }
  },
})
</script>

<style lang='scss' scoped>

</style>
