<template>
  <div>
    <el-button
      v-if="isDetailsExport"
      slot="reference"
      icon="el-icon-download"
      size="mini"
      type="primary"
      @click="handleDetailsExport"
      :disabled="false"
      :loading="detailsExportLoading"
    >详表导出
    </el-button>
    <el-button
      v-if="isSHowExec"
      slot="reference"
      icon="el-icon-s-promotion"
      size="mini"
      type="primary"
      @click="handleClick"
      :disabled="false"
      :loading="loading"
    >执行计算
    </el-button>
    <el-button
      slot="reference"
      icon="el-icon-info"
      size="mini"
      type="info"
      @click="handleLog"
      :disabled="false"
    >执行日志
    </el-button>

    <el-dialog title="执行计算-参数配置" :visible.sync="dialogVisible" width="85%" :before-close="handleClose">
      <el-form ref="form" :model="form" label-width="120px">
        <el-row>
          <el-col v-if="isDeveloper" :span="8">
            <el-form-item label="计算逻辑CODE">
              <el-select style="width: 100%" filterable v-if="isDeveloper" v-model="form.calculLogicCode" placeholder="请选择">
                <el-option v-for="item in calculLogicCodeOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="使用介绍">
              <el-input :disabled="!isDeveloper" v-model="form.remark" type="textarea" :rows="7" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs
        v-model="currentTabCode"
        @tab-click="handleTabClick"
        :type="isDeveloper ? 'card' : ''"
        :editable="isDeveloper"
        @edit="handleTabsEdit"
      >
        <el-tab-pane
          v-for="(tab, index) in stepConfig"
          :key="index"
          :label="tab.tabName"
          :name="tab.tabCode"
          :editableTabs="isDeveloper"
        >
          <template slot="label">
            <span v-if="!tab.inputFlag" @dblclick="tabsContent(tab,tab.tabCode)">
              <el-badge
                :hidden="!currentTabTodoNum.hasOwnProperty(tab.tabCode) || !currentTabTodoNum[tab.tabCode]"
                :value="currentTabTodoNum.hasOwnProperty(tab.tabCode) ? currentTabTodoNum[tab.tabCode] : 0"
                style="margin-top: 10px; margin-right: 40px;"
                type="warning"
              >
              {{tab.tabName}}
              </el-badge>
            </span>
            <el-input size="mini" v-else-if="tab.inputFlag" :ref="`myInput${tab.tabCode}`" v-model="tab.tabName" @keydown.native.stop="handleKeydown" @blur="tab.inputFlag=false" />
          </template>
          <el-form ref="form" :model="tab" label-width="100px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="字段配置">
                  <el-row>
                    <el-col :span="24">
                      <span style="color: #ff0000;font-size: 12px;"><i class="el-icon-warning"></i> 提示: 同一数据类型分组下的每个字段, 对应的映射表名称和数量必须一致.</span>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2">
                      <el-tabs :tab-position="'left'" v-model="currentMain" @tab-click="handleMainTabClick" style="height: 400px;">
                        <el-tab-pane
                        v-for="(main, idx) in tab.mainTypeOption"
                        :key="idx"
                        :label="main.label"
                        :name="main.value"
                        >
                          <template slot="label">
                            <el-badge
                              :hidden="!currentTabFieldTodoNum.hasOwnProperty(main.value) || currentTabFieldTodoNum[main.value] === 0"
                              :value="currentTabFieldTodoNum.hasOwnProperty(main.value) ? currentTabFieldTodoNum[main.value] : 0"
                              style="margin-top: 10px; margin-right: 40px;"
                              type="warning"
                            >
                              <span>{{main.label}}</span>
                            </el-badge>
                          </template>
                        </el-tab-pane>
                      </el-tabs>
                    </el-col>
                    <el-col :span="22" :class="!isDeveloper ? 'item-table-x' : ''">
                      <el-button v-if="isDeveloper" type="text" @click="handleRow(tab.fieldList[currentMain], -1, 'add', 'field')">添加行</el-button>
                      <el-table width="100%" height="400px" size="mini" :data="tab.fieldList[currentMain]" :span-method="(params) => spanMethod(params, mergeFields)" :row-class-name="({row, rowIndex}) => getRowClassName(tab.fieldList[currentMain], row, rowIndex)" border>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="所属表" prop="tableType" show-tooltip-when-overflow :width="!isDeveloper ? 200 : 150" >
                          <template slot-scope="scope">
                            <el-select v-if="scope.row.devEdit" v-model="scope.row.tableType" placeholder="请选择">
                              <el-option v-for="item in tableTypeOption" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                            <span v-else>{{tableTypeOption.find(item => item.value === scope.row.tableType) ? tableTypeOption.find(item => item.value === scope.row.tableType).label : scope.row.tableType}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="字段名称" prop="fieldName" show-tooltip-when-overflow :width="!isDeveloper ? 180 : 150" >
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.devEdit" v-model="scope.row.fieldName" />
                            <span v-else>{{scope.row.fieldName}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column :label="isDeveloper ? '字段标题' : '字 段'" prop="fieldTitle" show-tooltip-when-overflow :width="!isDeveloper ? 210 : 180" >
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.devEdit" v-model="scope.row.fieldTitle" />
                            <span style="font-weight: bold; color: #1890ff; font-size: 13px;" v-else>{{scope.row.fieldTitle}}</span>
                            <br />
                            <el-tag type="warning" size="mini" v-if="scope.row.tag">{{scope.row.tag.split(':')[1]}}</el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="数据类型" prop="busiType" :width="!isDeveloper ? 310 : 180">
                          <template slot-scope="scope">
                            <el-select :class="!scope.row.busiType ? 'item-class-warning' : 'item--class-success'" v-if="scope.row.tableType !== 'other'" v-model="scope.row.busiType" @change="val => handleBusiType(val, tab.fieldList[currentMain], scope.row, scope.$index)">
                              <el-option v-for="item in busiTypeOption" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label="映射表" prop="mappingTableName" :width="!isDeveloper ? 140 : 180">
                          <template slot-scope="scope">
                            <el-select
                              :class="!scope.row.mappingTableName ? 'item-class-warning' : 'item--class-success'"
                              v-if="scope.row.devEdit || (!scope.row.devEdit && scope.row.isEdit)"
                              v-model="scope.row.mappingTableName"
                              @visible-change="val => saveOrign(scope.row.mappingTableName)"
                              @change="val => handleMappingTableName(val, tab.fieldList[currentMain], scope.row, scope.$index)"
                              :clearable="!tab.fieldList[currentMain].find(item => item.tableType === scope.row.tableType
                                && item.tag === scope.row.tag
                                && item.fieldName === scope.row.fieldName
                                && !item.mappingTableName)"
                              filterable
                              placeholder="请选择">
                              <el-option
                                v-for="item in tableOption[scope.row.busiType]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :disabled="!!tab.fieldList[currentMain].find(t => t.tableType === scope.row.tableType && t.fieldName === scope.row.fieldName && t.mappingTableName === item.value)"
                              />
                            </el-select>
                            <span v-else>{{!!tableOptionList.find(item => item.value === scope.row.mappingTableName) ? tableOptionList.find(item => item.value === scope.row.mappingTableName).label : scope.row.mappingTableName}}</span>
                          </template>
                        </el-table-column>

                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="映射字段集" prop="mappingFieldSet" :width="!isDeveloper ? 0 : 200">
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.isMain && tab.fieldList[currentMain].filter(t => (
                          t.tableType === scope.row.tableType &&
                          t.fieldName === scope.row.fieldName &&
                          t.mappingTableName === scope.row.mappingTableName
                          )).length <= 1" v-model="scope.row.mappingFieldSet" placeholder="field1:名称1:dataType,field2:字段2:dataType" />
                            <span v-else>{{ scope.row.mappingFieldSet}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="描 述" prop="description" :width="!isDeveloper ? 0 : 150">
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.devEdit" v-model="scope.row.description" />
                            <span v-else>{{scope.row.description}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="映射字段" prop="mappingFieldName" :width="!isDeveloper ? 0 : 150"/>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="数据类型" prop="mappingFieldType" :width="!isDeveloper ? 0 : 150"/>
<!--                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" :label="isDeveloper ? '映射字段标题' : '映射字段'" prop="mappingFieldTitle" :width="!isDeveloper ? 0 : 150"/>-->
                        <el-table-column :class-name="!isXhxkTester ? 'custom-hidden-column' : ''" :label="'映射字段标题'" prop="mappingFieldTitle" :width="!isXhxkTester ? 0 : 150">
                          <template slot-scope="scope">
                            <el-input  v-model="scope.row.mappingFieldTitle" placeholder="映射字段标题" />
                          </template>
                        </el-table-column>
                        <el-table-column label="值来源" prop="mappingFieldValue" :width="!isDeveloper ? 0 : 150">
                          <template slot-scope="scope">
                            <span v-if="scope.row.devEdit || (!scope.row.devEdit && scope.row.isEdit)">
                              <span :class="!scope.row.mappingFieldValue ? 'item-class-danger' : 'item--class-success'" v-if="scope.row.mappingFieldName === 'tableTag'">
                                <el-input v-model="scope.row.mappingFieldValue" placeholder="请输入标签" />
                              </span>
                              <el-select style="width: 100%" :class="!scope.row.mappingFieldValue ? (scope.row.isRequired ? 'item-class-danger' : 'item-class-warning') : 'item--class-success'" v-else v-model="scope.row.mappingFieldValue" filterable @change="() => $forceUpdate()" clearable :placeholder="`${scope.row.mappingFieldTitle}`">
                                <el-option v-for="item in fieldOption[scope.row.mappingTableName]" :key="item.value" :label="item.label" :value="item.value" />
                              </el-select>
                            </span>
                            <span v-else>{{scope.row.mappingFieldValue}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="可编辑" prop="isEdit" show-tooltip-when-overflow :width="!isDeveloper ? 0 : 66" >
                          <template slot-scope="scope">
                            <el-switch v-model="scope.row.isEdit" active-color="#13ce66" inactive-color="#ff4949" />
                          </template>
                        </el-table-column>
                        <el-table-column :class-name="!isXhxkTester ? 'custom-hidden-column' : ''"  label="必 填" prop="isRequired" show-tooltip-when-overflow :width="!isXhxkTester ? 0 : 66" >
                          <template slot-scope="scope">
                            <el-switch v-model="scope.row.isRequired" active-color="#13ce66" inactive-color="#ff4949" />
                          </template>
                        </el-table-column>

                        <el-table-column label="操 作" :width="!isDeveloper ? 0 : 140">
                          <template slot-scope="scope">
                            <el-button v-if="isDeveloper && scope.row.isMain && tab.fieldList[currentMain].filter(t => (
                          t.tableType === scope.row.tableType &&
                          t.fieldName === scope.row.fieldName
                          )).length <= 1 && scope.row.mappingFieldSet && (scope.row.mappingFieldName === null)" type="text" size="mini" @click="handleSplit(tab.fieldList[currentMain], scope.row, scope.$index)">拆分
                            </el-button>
                            <el-button v-if="(scope.row.isMain && !scope.row.isAdminCreate) || (isDeveloper && scope.row.isMain)" type="text" size="mini" @click="handleRow(tab.fieldList[currentMain], scope.$index, 'remove', 'field')">删除</el-button>
                            <el-button v-if="scope.row.isMain && scope.row.tableType !== 'other' && !!scope.row.busiType && isShowAddLine(tab.fieldList[currentMain], scope.row, scope.$index)" type="text" size="mini" @click="handleRow(tab.fieldList[currentMain], scope.$index, 'addLine')">添加表</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-show="isDeveloper" :class="!isDeveloper ? 'item-table-x' : ''">
                <el-form-item label="关系字段">
                  <el-button type="text" v-if="isDeveloper" @click="refreshRelationList">刷新</el-button>
                  <el-table size="mini" height="400px" :data="tab.relationList" :row-style="rowStyle" :span-method="(params) => spanMethod3(params, mergeFields3)" border>
                    <el-table-column label="所属表" prop="tableType" show-tooltip-when-overflow :width="!isDeveloper ? 200 : 150" >
                      <template slot-scope="scope">
                        <span>{{tableTypeOption.find(item => item.value === scope.row.tableType) ? tableTypeOption.find(item => item.value === scope.row.tableType).label : scope.row.tableType}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="业务类型" prop="tag" show-tooltip-when-overflow :width="!isDeveloper ? 200 : 150" >
                      <template slot-scope="scope">
                        <el-tag v-if="!!scope.row.tag" type="warning">{{scope.row.tag.split(':').length === 2 ? scope.row.tag.split(':')[1] :scope.row.tag}}</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column :class-name="'custom-hidden-column'"  label="描 述" prop="description" :width="!isDeveloper ? 200 : 150">
                      <template slot-scope="scope">
                        <el-input v-if="isDeveloper" v-model="scope.row.description" />
                        <span v-else>{{scope.row.description}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="映射表" prop="mappingTableName" :width="!isDeveloper ? 200 : 180">
                      <template slot-scope="scope">
                        <span>{{!!tableOptionList && tableOptionList.find(item => item.value === scope.row.mappingTableName) ? tableOptionList.find(item => item.value === scope.row.mappingTableName).label : scope.row.mappingTableName}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="映射字段集" prop="mappingFieldSet"  :width="!isDeveloper ? 337 : 200">
                      <template slot-scope="scope">
                        <el-input v-if="tab.relationList.filter(t => (
                          t.tableType === scope.row.tableType &&
                          t.tag === scope.row.tag &&
                          t.mappingTableName === scope.row.mappingTableName
                          )).length <= 1" v-model="scope.row.mappingFieldSet" placeholder="field1:名称1,field2:字段2" />
                        <span v-else>{{ scope.row.mappingFieldSet}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="字段名称" prop="mappingFieldName" show-tooltip-when-overflow width="180" />
                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="数据类型" prop="mappingFieldType" show-tooltip-when-overflow width="180" />
                    <el-table-column :label="isDeveloper ? '字段标题' : '字段'" prop="mappingFieldTitle" show-tooltip-when-overflow width="180" />
                    <el-table-column label="映射字段" prop="mappingFieldValue" width="250">
                      <template slot-scope="scope">
                        <el-select style="width: 100%" filterable v-if="scope.row.devEdit || (!scope.row.devEdit && scope.row.isEdit)" v-model="scope.row.mappingFielderrorMessageValue" @change="() => $forceUpdate()" placeholder="请选择">
                          <el-option v-for="item in fieldOption[scope.row.mappingTableName]" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                        <span v-else>{{scope.row.mappingFieldValue}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="可显示" prop="isShow" show-tooltip-when-overflow width="66" >
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.isShow" active-color="#13ce66" inactive-color="#ff4949" />
                      </template>
                    </el-table-column>
                    <el-table-column :class-name="!isDeveloper ? 'custom-hidden-column' : ''" label="可编辑" prop="isEdit" show-tooltip-when-overflow width="66" >
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.isEdit" active-color="#13ce66" inactive-color="#ff4949" />
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="筛选条件">
                  <el-button type="text" style="margin-right: 4px;" @click="handleRow(tab.whenList, -1, 'add', 'when')">添加行</el-button>
                  <span v-if="form.calculLogicCode === 'computeSiteInfoDrawing' && currentTabCode === 'tab1'" style="color: #ff0000;font-size: 12px;"><i class="el-icon-warning"></i> 提示: 工参数据若包含室分站型的数据, 可通过定义筛选条件进行过滤</span>
                   <el-table size="mini" :data="tab.whenList" :span-method="(params) => spanMethod2(params, mergeFields2)" border><!-- :row-style="rowStyle" -->
<!--                   <el-table size="mini" :data="tab.whenList" border>-->
                    <el-table-column label="映射表" prop="tableName" width="180" >
                      <template slot-scope="scope">
                        <el-select :disabled="scope.row.type === 'systemCreate'" style="width: 100%" filterable @visible-change="val => saveOrign(scope.row.tableName)" v-model="scope.row.tableName" @change="val => handleTableName(val, tab.whenList, scope.row, scope.$index)" placeholder="请选择">
                          <el-option v-for="item in whenTableOption" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="字 段" prop="fieldName" width="180" >
                      <template slot-scope="scope">
                        <el-select :key="updateIndex" :disabled="scope.row.type === 'systemCreate'" filterable style="width: 100%" @visible-change="val => saveOrign(scope.row.fieldName)" v-model="scope.row.fieldName" @change="val => handleFieldName(val, tab.whenList, scope.row, scope.$index)" placeholder="请选择">
                          <el-option v-for="item in fieldOption[scope.row.tableName]" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="筛选类型" prop="filterType" width="150" >
                      <template slot-scope="scope">
                        <el-select :key="updateIndex" style="width: 100%" v-model="scope.row.filterType" :disabled="scope.row.type === 'systemCreate'" placeholder="请选择">
                          <el-option v-for="item in filterTypeOption" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="筛选值" prop="filterValue" min-width="150" >
                      <template slot-scope="scope">
                        <span :class="(!scope.row.filterValue || !scope.row.filterValue.length) && (!scope.row.fieldName || !scope.row.filterType || (scope.row.filterType !== 'eq' && scope.row.filterType !== 'neq')) ? 'item-class-danger' : ''">
                          <el-select :disabled="scope.row.type === 'systemCreate'" style="width: 100%" v-if="scope.row.filterType === 'in' || scope.row.filterType === 'nin'" filterable allow-create multiple v-model="scope.row.filterValue" placeholder="请选择">
                            <el-option label="请输入" :value="''" disabled />
                          </el-select>
                          <el-input v-else v-model="scope.row.filterValue" :disabled="scope.row.type === 'systemCreate'" :placeholder="scope.row.filterType === 'eq' || scope.row.filterType === 'neq' ? '空' : '请输入筛选值'" />
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" width="100">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" v-if="scope.row.type !== 'systemCreate'" @click="handleRow(tab.whenList, scope.$index, 'remove', 'when')">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="参数配置">
                  <el-button v-if="isDeveloper" type="text" @click="handleRow(tab.paramsList, -1, 'add', 'params')">添加行</el-button>
                  <el-table height="150" size="mini" :data="tab.paramsList" border>
                    <el-table-column label="名 称" prop="name" show-tooltip-when-overflow width="150" >
                      <template slot-scope="scope">
                        <el-input v-if="scope.row.devEdit" v-model="scope.row.name" />
                        <span v-else>{{scope.row.name}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="isDeveloper" label="类 型" prop="type" show-tooltip-when-overflow width="150" >
                      <template slot-scope="scope">
                        <el-select v-if="scope.row.devEdit" style="width: 100%" v-model="scope.row.type" placeholder="请选择">
                          <el-option label="文本" :value="'String'" />
                          <el-option label="日期" :value="'Date'" />
                          <el-option label="日期时间" :value="'DateTime'" />
                          <el-option label="数组" :value="'List'" />
                        </el-select>
                        <span v-else>{{scope.row.type}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="参数值" prop="value" show-tooltip-when-overflow width="350" >
                      <template slot-scope="scope">
                        <span :class="scope.row.isRequired && (!scope.row.value || scope.row.value.length === 0) ? 'item-class-danger' : '' " v-if="scope.row.isEdit || scope.row.devEdit">
                          <el-date-picker style="width: 100%" v-if="scope.row.type === 'Date'" v-model="scope.row.value" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期"></el-date-picker>
                          <el-date-picker style="width: 100%" v-else-if="scope.row.type === 'DateTime'" v-model="scope.row.value" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择日期时间"></el-date-picker>
                          <el-select style="width: 100%" v-else-if="scope.row.type === 'List'" filterable allow-create multiple v-model="scope.row.value" placeholder="请选择">
                            <el-option label="请输入" :value="''" disabled />
                          </el-select>
                          <el-input v-else v-model="scope.row.value" />
                        </span>
                        <span v-else>{{scope.row.value}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="参数描述" prop="description" show-tooltip-when-overflow min-width="150" >
                      <template slot-scope="scope">
                        <el-input v-if="scope.row.devEdit" v-model="scope.row.description" />
                        <span v-else>{{scope.row.description}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="isDeveloper" label="可编辑" prop="isEdit" show-tooltip-when-overflow width="66" >
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.isEdit" active-color="#13ce66" inactive-color="#ff4949" />
                      </template>
                    </el-table-column>
                    <el-table-column v-if="isDeveloper" label="必 填" prop="isRequired" show-tooltip-when-overflow width="66" >
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.isRequired" active-color="#13ce66" inactive-color="#ff4949" />
                      </template>
                    </el-table-column>
                    <el-table-column v-if="isDeveloper" label="可显示" prop="isEdit" show-tooltip-when-overflow width="66" >
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.isShow" active-color="#13ce66" inactive-color="#ff4949" />
                      </template>
                    </el-table-column>
                    <el-table-column v-if="isDeveloper" fixed="right" label="操 作" width="100">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="handleRow(tab.paramsList, scope.$index, 'edit')">{{scope.row.devEdit ? '确认' : '编辑'}}</el-button>
                        <el-button type="text" size="mini" @click="handleRow(tab.paramsList, scope.$index, 'remove')">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="isXhxkTester" @click="saveTemplate">作为模板</el-button>
        <el-button type="waring" @click="saveStepConfig">保 存</el-button>
        <el-button type="primary" :loading="pushLoading" @click="pushData">开始计算</el-button>
      </span>
    </el-dialog>

    <el-dialog title="执行日志" :visible.sync="dialogVisibleLog" width="85%" :before-close="handleCloseLog">
      <el-button style="margin: 5px 0px" type="primary" size="mini" icon="el-icon-refresh" @click="refreshLogList">刷新</el-button>
      <el-table v-loading="logLoading" :data="logList" size="mini" style="width: 100%;margin-bottom: 20px;" row-key="id" border :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <el-table-column prop="sub_title" label="标 题" min-width="230" show-tooltip-when-overflow>
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{'计算任务：' + scope.row.start_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{`【日志${scope.row.message ? ': ' + scope.row.message.substring(0, scope.row.message.indexOf(' ')).replace('步骤', '') : ''}】` + scope.row.sub_title}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="160" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.start_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.start_time}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="160" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.end_time}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.end_time}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="compute_time" label="耗 时" width="115" >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.compute_time}}</span>
            <span style="color: #736A6AFF;" v-else>
              <span style="color: #2FAD75FF;" v-if="!!scope.row.compute_time && scope.row.compute_time.startsWith('0分')">{{scope.row.compute_time}}</span>
              <span style="color:#e45416;" v-else-if="!!scope.row.compute_time &&
               (!scope.row.compute_time.startsWith('0分')
                && !scope.row.compute_time.startsWith('1分')
                && !scope.row.compute_time.startsWith('2分')
                && !scope.row.compute_time.startsWith('3分')
                )">
                {{scope.row.compute_time}}
              </span>
              <span v-else>{{scope.row.compute_time}}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消 息" min-width="200" show-tooltip-when-overflow >
          <template slot-scope="scope">
            <span style="font-weight: bold; font-size: 12px; color: #736A6AFF;" v-if="scope.row.parent_id === 0">{{scope.row.message ? scope.row.message.substring(scope.row.message.indexOf(' ') + 1) : scope.row.message}}</span>
            <span style="color: #736A6AFF;" v-else>{{scope.row.message ? scope.row.message.substring(scope.row.message.indexOf(' ') + 1) : scope.row.message}}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="isViewLogException" prop="exception" label="异常信息" width="110" show-tooltip-when-overflow >
          <template slot-scope="scope">
            <el-button
              class="copy-btn" v-if="!!scope.row.exception" size="mini" type="text"
              icon="el-icon-document-copy" @click="copyText(scope.row, 'exception')">复制异常信息</el-button>
            <span v-else>{{scope.row.exception}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="110">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 'PROCESS'" type="primary"><i class="el-icon-loading"></i> 执行中</el-tag>
            <el-tag v-if="scope.row.status === 'FAILED'" type="danger"><i class="el-icon-error"></i> 执行失败</el-tag>
            <el-tag v-if="scope.row.status === 'SUCCEED'" type="success"><i class="el-icon-success"></i> 执行成功</el-tag>
            <el-tag v-if="scope.row.status === 'STOP'" type="warning"><i class="el-icon-warning"></i> 意外终止</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="isViewLogSQL" fixed="right" label="操作" width="100">
          <template slot-scope='scope'>
            <el-button
              class="copy-btn" v-if="scope.row.sql" size="mini" type="text"
              icon="el-icon-document-copy" @click="copyText(scope.row, 'sql')">复制SQL
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total">
      </el-pagination>

    </el-dialog>

  </div>
</template>
<script>
import {commonQueryByPost, commonQueryExportData} from "@/api/kernel/query";
import * as API from '@/api/provinces/yn/projectManager'
import {findDataTypeConfigById, info} from '@/api/yn_plan/engineering'
import user from "@/store/modules/user";

export default {
  name: 'ExecuteCalculation',
  props: {
    choiceIds: Array,
    queryParams: {
      type: Object,
      required: true,
    },
    menuQuery: {
      type: Object,
      required: true,
    }
  },
  data() {
    return {
      isAdmin: false,
      isDeveloper: false,
      isXhxkTester: false,

      loading: false,
      pushLoading: false,
      dialogVisible: false,

      tableTypeOption: [],
      calculLogicCodeOption: [],
      tableOption: {},
      tableOptionList: [],
      fieldOption: {},
      filterTypeOption: [
        {label: '等于', value: 'eq'},
        {label: '不等于', value: 'neq'},
        {label: '大于', value: 'gt'},
        {label: '大于等于', value: 'gte'},
        {label: '小于', value: 'lt'},
        {label: '小于等于', value: 'lte'},
        {label: '包含', value: 'in'},
        {label: '不包含', value: 'nin'},
        {label: '模糊匹配', value: 'like'}
      ],

      // 变更映射表时保存旧值
      orignValue: null,

      stepConfig: [],
      oldStepConfig: [],
      currentTabCode: 'New_001',
      form: {
        calculLogicCode: null,
        remark: null,
      },
      stepType: null,

      // 业务分类&表分类
      currentMain: null,
      busiTypeOption: [],

      spanArrMap: new Map(),
      mergeFields: ['tableType', 'fieldName', 'fieldTitle', 'mappingTableName', 'mappingFieldSet', 'description'],
      additionalMerges: [{
        field: 'busiType',        // 需要合并的目标列
        dependencies: ['tableType']  // 合并依据的字段（支持多字段）
      }],

      spanArrMap2: new Map(),
      mergeFields2: ['tableName', 'fieldName'],

      spanArrMap3: new Map(),
      mergeFields3: ['tableType', 'tag', 'description', 'mappingTableName'],

      dialogVisibleLog: false,
      logList: [],
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      logLoading: false,
      interval: null,

      isViewLogException: false,
      isViewLogSQL: false,

      updateIndex: 0,

      engineering: {},
      isSHowExec: false,

      isDetailsExport: false,
      detailsExportLoading: false,
      showWhenSystemCreate: false
    }
  },
  async created() {
    this.isDeveloper = user.state.roles.includes('PROJECT_EXEC_CONFIG')
    // 业务需求：保存模版、映射字段标题、是否必填 这部分配置直接写死在账号 xhxk-tester 里
    this.isXhxkTester = user.state.user.account.includes("xhxk-tester")
    this.isViewLogException = user.state.roles.includes('admin') || user.state.roles.includes('isViewLogException')
    this.isViewLogSQL = user.state.roles.includes('admin') || user.state.roles.includes('isViewLogSQL')

    this.isDetailsExport = this.menuQuery.code === 'query_siteinfo_drawing'
    this.isDeveloper && (this.showWhenSystemCreate = true)

    await commonQueryByPost('queryEngineeringById', {engineeringId: this.$route.meta.engineeringId}).then(res => {
      this.engineering = res.data[0]
      this.engineering.user_id === user.state.user.userId && (this.isSHowExec = true)
    })

    // 加载通用查询列表
    await this.querySmartPlanCommonQueryList()
    // 获取数据类型
    await this.queryDataTableTypeOptions()
    // 加载数据中心字段列表
    await this.queryFieldList()

    // 加载数据
    await this.initData()
  },
  mounted() {
    const that = this
    this.interval = setInterval(() => {
      const code = that.menuQuery.execParamsCode + '_' + that.$route.meta.engineeringId
      commonQueryByPost('queryExecLog', {...that.page, code: code}).then(res => {
        that.logList = res.data.map(item => {
          const children = item.children?.value ? JSON.parse(item.children.value) : [];
          return {
            ...item,
            children: children
          }
        })
        that.page.total = res.total
      })
    }, 5000)
  },
  // 退出组件销毁interval
  beforeDestroy() {
    !!this.interval && clearInterval(this.interval)
  },
  watch: {
    stepConfig: {
      handler(val,  old) {
        this.oldStepConfig = JSON.parse(JSON.stringify(old))
      },
      deep: true
    },
  },
  computed: {
    currentTabConfig() {
      return this.stepConfig.find(item => item.tabCode === this.currentTabCode)
    },
    currentTabIndex() {
      return this.stepConfig.findIndex(item => item.tabCode === this.currentTabCode)
    },
    whenTableOption() {
      const currentTab = this.stepConfig.find(item => item.tabCode === this.currentTabCode);
      const types = Object.keys(currentTab.fieldList)
      let tableNames = []
      types.forEach(type => {
        tableNames = tableNames.concat(currentTab.fieldList[type].map(item => item.mappingTableName))
      })
      let result = []
      Object.keys(this.tableOption).forEach(key => {
        const filter = this.tableOption[key].filter(item => tableNames.includes(item.value))
        if (filter.length > 0) {
          result = [...result, ...filter]
        }
      })
      return result
    },
    currentTabTodoNum() {
      let todoNum = {}
      this.stepConfig.forEach(tab => {
        const types = Object.keys(tab.fieldList)
        types.forEach(type => {
          tab.fieldList[type].forEach(item => {
            if ((item.tableType !== 'other' && item.isEdit) && (!item.mappingFieldValue ||  item.mappingFieldValue.length === 0)) {
              todoNum[tab.tabCode] = (todoNum[tab.tabCode] || 0) + 1
            }
          })
        })
      })
      return todoNum
    },
    currentTabFieldTodoNum() {
      let todoNum = {}
      const currentTab = this.stepConfig.find(item => item.tabCode === this.currentTabCode);
      const types = Object.keys(currentTab.fieldList)
      types.forEach(type => {
        currentTab.fieldList[type].forEach(item => {
          if ((item.tableType !== 'other' && item.isEdit) && (!item.mappingFieldValue ||  item.mappingFieldValue.length === 0)) {
            todoNum[type] = (todoNum[type] || 0) + 1
          }
        })
      })
      return todoNum
    }
  },
  methods: {
    async queryDataTableTypeOptions() {
      const busiTypes = await this.getDataTree('YN_DATA_TYPE');
      for (const parent of busiTypes.data) {
        parent.children?.forEach((child) => {
          this.busiTypeOption.push({ label: child.label, value: child.code })
        })
      }


      const { data: engineering } = await info(this.$route.meta.engineeringId)
      if (!engineering) {
        this.$message.error('工程信息查询失败')

      } else {

      }
      let params = {
        dataSource: engineering.datasource,
      }
      const dataMap = await findDataTypeConfigById(params);
      const dataClassifyOptions = await this.getDataTree('TABLE_TYPE');

      for (const parent of dataClassifyOptions.data) {
        parent.children?.forEach((child) => {
          this.tableTypeOption.push({ label: child.label, value: child.code })
        })
      }
      this.tableOption = dataMap.data;

      this.tableTypeOption.push({label: '其他', value: 'other'})
      this.tableOption['other'] = [{label: '系统生成', value: 'system_create'}]

      this.tableOptionList = []
        Object.keys(this.tableOption).map(key => {
        const tableList = this.tableOption[key]
        this.tableOptionList = [...this.tableOptionList, ...tableList]
      })
    },
    querySmartPlanCommonQueryList() {
      commonQueryByPost('querySmartPlanCommonQueryList', {}).then(res => {
        this.calculLogicCodeOption = res.data.map(item => ({label: item.name, value: item.code}))
      })
    },
    queryFieldList() {
      commonQueryByPost('queryFieldList', {}).then(res => {
        res.data.forEach(item => {
          this.fieldOption[item.table_name] = JSON.parse(item.field_list.value)
        })
      })
    },
    getRowClassName(list, row, rowIndex) {
      if (rowIndex === list.length - 1) {
        return '';
      }

      const currentFieldName = row.fieldName;
      const nextFieldName = list[rowIndex + 1].fieldName;

      return currentFieldName !== nextFieldName ? 'table-blue-border-bottom' : '';
    },
    isShowAddLine(list, row, index) {
      let lastIndex = 0
      list.forEach((item, i) => {
        if (item.tableType === row.tableType && item.tag === row.tag && item.fieldName === row.fieldName && item.isMain) {
          lastIndex = i
        }
      })
      let groupStart = -1
      const gList = list.filter(item => item.tableType === row.tableType && item.tag === row.tag && item.isAdminCreate && item.isMain)
      gList.forEach((item, i) => {
        if (item.fieldName === row.fieldName) {
            groupStart = i
        }
      })
      if (gList.length > 1) {
        return lastIndex === index && groupStart === 0
      }
      return lastIndex === index
    },
    handleSplit(list, row, index) {
      if (row.mappingFieldSet) {
        const items = row.mappingFieldSet.split(',');
        items.forEach((item, i) => {
          const itemArr = item.split(':')
          if (i === 0) {
            row.mappingFieldName = itemArr[0]
            row.mappingFieldTitle = itemArr[1]
            row.mappingFieldType = itemArr[2]
            row.devEdit = false
          } else {
            list.splice(index + i, 0, {
              tableType: row.tableType,
              fieldName: row.fieldName,
              fieldTitle: row.fieldTitle,
              mappingTableName: row.mappingTableName,
              mappingFieldSet: row.mappingFieldSet,
              description: row.description,
              mappingFieldName: itemArr[0],
              mappingFieldTitle: itemArr[1],
              mappingFieldType: itemArr[2],
              mappingFieldValue: null,
              devEdit:  false,
              isEdit: true,
              isMain: false
            })
          }
        })
        this.getSpanArr(list, this.mergeFields)
      }
    },
    saveOrign(val) {
      this.orignValue = val
    },
    handleMappingTableName(newVal, list, row, index) {
      if (!this.orignValue) {
        this.orignValue = this.oldStepConfig[this.currentTabIndex].fieldList[this.currentMain][index].mappingTableName
      }
      console.log('旧值: ', this.orignValue)

      if (newVal && this.fieldOption.hasOwnProperty(newVal) && this.fieldOption[newVal].length >= 0) {
        const mField = this.fieldOption[newVal].find(f => f.label === row.mappingFieldTitle || f.value === row.mappingFieldName);
        row.mappingFieldValue  = !!mField ? mField.value : null
      } else {

        row.mappingFieldValue = null
      }
      list.forEach(item => {
        if (item.tableType === row.tableType && item.tag === row.tag && item.fieldName === row.fieldName && item.mappingTableName === this.orignValue) {
          item.mappingTableName = newVal
          if (newVal && this.fieldOption.hasOwnProperty(newVal) && this.fieldOption[newVal].length >= 0) {
            const mField = this.fieldOption[newVal].find(f => f.label === item.mappingFieldTitle || f.value === item.mappingFieldName);
            item.mappingFieldValue  = !!mField ? mField.value : null
          } else {
            item.mappingFieldValue = null
          }
        }
      })
      this.refreshRelationList()

      this.updateWhenList()
    },
    updateWhenList() {
      this.stepConfig.forEach(tab => {
        const types = Object.keys(tab.fieldList)
        let tableNames = new Set()
        types.forEach(type => {
          tab.fieldList[type].forEach(item => {
            (!!item.mappingTableName && !item.mappingTableName.includes('请选择')) && tableNames.add(item.mappingTableName)
          })
        })
        tableNames = [...tableNames]
        tableNames.forEach(tableName => {
          const hasCity = tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].find(item => item.tableName === tableName && item.fieldName === 'city' && item.type === 'systemCreate')
          const hasSdate = tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].find(item => item.tableName === tableName && item.fieldName === 'sdate' && item.type === 'systemCreate')
          const tableInfo = this.tableOptionList.find(item => item.value === tableName)
          const cityList = !this.engineering.city_name || this.engineering.city_name === '全省' ? [] : this.engineering.city_name.split(',')
          const hasLayerFieldItem = this.fieldOption[tableName].find(item => item.value === 'geom')
          const hasCityFieldItem = this.fieldOption[tableName].find(item => item.value === 'city')
          const hasSdateFieldItem = this.fieldOption[tableName].find(item => item.value === 'sdate')
          let cityItem = null, sdateItem = null
          if (cityList.length && hasCityFieldItem) {
            tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].forEach((item, i) => {
              if (item.tableName === tableName && item.fieldName === 'city' && item.type === 'systemCreate') {
                tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].splice(i, 1)
              }
            })
            cityItem = {tableName: tableName, fieldName: 'city', filterType: 'in', filterValue: cityList, type: 'systemCreate', isShow: false}
          }
          if (!hasSdate && tableInfo && !hasLayerFieldItem && hasSdateFieldItem) {
            tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].forEach((item, i) => {
              if (item.tableName === tableName && item.fieldName === 'sdate' && item.type === 'systemCreate') {
                tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].splice(i, 1)
              }
            })
            sdateItem = {tableName: tableName, fieldName: 'sdate', filterType: 'eq', filterValue: tableInfo.sdate, type: 'systemCreate', isShow: false}
          }
          let tableNameIdx = -1
          tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].forEach((item, i) => {
            if (item.tableName === tableName) {
              tableNameIdx = i
            }
          })
          if (tableNameIdx >= 0) {
            cityItem && tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].splice(tableNameIdx + 1, 0, cityItem)
            sdateItem && tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].splice(tableNameIdx + 1, 0, sdateItem)
          } else {
            cityItem && tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].push(cityItem)
            sdateItem && tab[this.showWhenSystemCreate ? 'whenList' : 'cacheWhenList'].push(sdateItem)
          }
        })
        for (let i = tab.whenList.length - 1; i >= 0 ; i--) {
          const item = tab.whenList[i]
          if (item.type === 'systemCreate' && !tableNames.includes(item.tableName)) {
            tab.whenList.splice(i, 1)
          }
        }

        this.currentTabCode === tab.tabCode && this.getSpanArr2(tab.whenList, this.mergeFields2)

      })

    },
    handleBusiType(newVal, list, row, index) {
      let lastKey = ''
      let i = 1
      let uuid = ''
      list.filter(item => item.tableType === row.tableType).forEach(item => {
        item.busiType = newVal
        let key = `${item.tableType}_${item.tag ? item.tag : 'null'}_${item.fieldName}`
        if (key === lastKey) {
          if (item.isMain) {
            i++
            const date = new Date()
            uuid = [
              date.getFullYear(),
              (date.getMonth() + 1).toString().padStart(2, '0'),
              date.getDate().toString().padStart(2, '0'),
              date.getHours().toString().padStart(2, '0'),
              date.getMinutes().toString().padStart(2, '0'),
              date.getSeconds().toString().padStart(2, '0')
            ].join('')
          } else {
          }
        } else {
          lastKey = key
          i = 1
        }
        if (this.tableOption.hasOwnProperty(item.busiType) && this.tableOption[item.busiType].length >= i) {
          item.mappingTableName  = this.tableOption[item.busiType][i - 1].value
          if (this.fieldOption.hasOwnProperty(item.mappingTableName) && this.fieldOption[item.mappingTableName].length > 0) {
            const mField = this.fieldOption[item.mappingTableName].find(f => f.label === item.mappingFieldTitle || f.value === item.mappingFieldName);
            item.mappingFieldValue  = !!mField ? mField.value : null
          }
        } else {
          item.mappingTableName  = `请选择                              ${uuid}`
          item.mappingFieldValue  = null
        }
      })
      this.updateWhenList()
      this.refreshRelationList()
    },
    handleTableName(newVal, list, row, index) {
      row.fieldName = null
      row.filterType = null
      row.filterValue = null
      list.forEach(item => {
        if (item.tableName === this.orignValue && item.type !== 'systemCreate') {
          item.tableName = newVal
          item.fieldName = null
          item.filterType = null
          item.filterValue = null
        }
      })
      // 移动本行到分组最后的位置
      if (list.filter(item => item.tableName === newVal).length >= 2) {
        list.splice(index, 1)
        let tableNameIdx = -1
        list.forEach((item, i) => {
          if (item.tableName === newVal) {
            tableNameIdx = i
          }
        })
        if (tableNameIdx >= 0) {
          list.splice(tableNameIdx + 1, 0, {tableName: newVal, fieldName: null, filterType: null, filterValue: null, isShow: row.isShow})
        }
      }
      this.$set(this.stepConfig[this.currentTabIndex], 'whenList', list)
      this.getSpanArr2(list, this.mergeFields2)
    },
    handleFieldName(newVal, list, row, index) {
      const dataType = this.fieldOption[row.tableName].find(item => item.value === newVal)?.dataType;
      row.dataType = dataType
      row.filterType = null
      row.filterValue = null
      list.forEach(item => {
        if (item.tableName === row.tableName && item.fieldName === this.orignValue && item.type !== 'systemCreate') {
          item.dataType = dataType
          item.fieldName = newVal
          item.filterType = null
          item.filterValue = null
        }
      })
      this.getSpanArr2(list, this.mergeFields2)
      this.updateIndex++
    },

    handleKeydown(event) {
      // 仅在输入框为空且按下退格键时阻止默认行为
      if (event.key === 'Backspace' && event.target.value === '') {
        event.preventDefault();
      }
    },
    rowStyle({ row }) {
      if (row.hasOwnProperty('isShow') && !row.isShow && !this.isDeveloper) {
        return { display: "none" };
      }
      return {};
    },
    handleClick() {
      this.initData()
      this.dialogVisible = true
    },
    handleTabClick() {
      console.log('切换')
      this.currentMain = this.stepConfig[this.currentTabIndex].mainTypeOption[0].value
      this.getSpanArr(this.stepConfig[this.currentTabIndex].fieldList[this.currentMain], this.mergeFields)
      this.getSpanArr2(this.stepConfig[this.currentTabIndex].whenList, this.mergeFields2)
      this.getSpanArr3(this.stepConfig[this.currentTabIndex].relationList, this.mergeFields3)
    },
    handleMainTabClick() {
      console.log('切换')
      // this.currentMain = this.stepConfig[this.currentTabIndex].mainTypeOption[0].value
      this.getSpanArr(this.stepConfig[this.currentTabIndex].fieldList[this.currentMain], this.mergeFields)
    },
    handleClose() {
      this.dialogVisible = false
    },
    async pushData() {
      this.pushLoading = true
      const code = this.menuQuery.execParamsCode + '_' + this.$route.meta.engineeringId
      const tabs = JSON.parse(JSON.stringify(this.stepConfig))
      let errorMessage = "";
      tabs.forEach(tab => {
        if (tab.hasOwnProperty('cacheWhenList')) {
          tab.whenList = [...tab.cacheWhenList, ...tab.whenList]
          delete tab.cacheWhenList
        }
        // 校验必填
        const types = Object.keys(tab.fieldList)
        types.forEach(type => {
          tab.fieldList[type].forEach(field => {
            if (field.isRequired && !field.mappingFieldValue) {
              errorMessage += field.mappingFieldTitle + ';'
            }
          })
        })
      })
      if (errorMessage) {
        this.$message.error('字段：' + errorMessage.slice(0, -1) + ' 不能为空！')
        this.pushLoading = false
        return
      }
      await commonQueryByPost('saveExecParamsConfig', {
        code: code,
        params: JSON.stringify(tabs),
        ...this.form
      })
      let chickMap = {
        fieldList: [],
        whenList: [],
        relationList: [],
        paramsList: []
      }
      this.stepConfig.forEach(tab => {
        tab.paramsList.forEach(param => {
          if (!!param.isRequired && (!param.value || param.value.length === 0)) {
            chickMap.paramsList.push(param)
          }
        })
      })
      if (chickMap.fieldList.length > 0) {
        this.$message({type: 'warning', message: '请检查【字段配置】中的必填项'})
        this.pushLoading = false
        return
      }
      if (chickMap.whenList.length > 0) {
        this.$message({type: 'warning', message: '请检查【条件配置】中的必填项'})
        this.pushLoading = false
      }
      if (chickMap.relationList.length > 0) {
        this.$message({type: 'warning', message: '请检查【关系字段】中的必填项'})
        this.pushLoading = false
        return
      }
      if (chickMap.paramsList.length > 0) {
        this.$message({type: 'warning', message: '请检查【参数配置】中的必填项'})
        this.pushLoading = false
        return
      }
      await API.execComputeLogic(code).then(res => {
        this.pushLoading = false
        this.$message({
          type: 'success',
          message: '开始计算... 稍请到日志中查看计算结果'
        })
        this.dialogVisibleLog = true
      }).catch(err => {
        this.pushLoading = false
        this.$message.error(err)
      })
      this.dialogVisible  = false
    },
    handleTabsEdit(targetName, action) {
      console.log(targetName, action)
      if (action === 'add') {
        const date = new Date()
        let uuid = [
          date.getFullYear(),
          (date.getMonth() + 1).toString().padStart(2, '0'),
          date.getDate().toString().padStart(2, '0'),
          date.getHours().toString().padStart(2, '0'),
          date.getMinutes().toString().padStart(2, '0'),
          date.getSeconds().toString().padStart(2, '0')
        ].join('')
        this.stepConfig.push({
          tabName: `新建_${uuid}`,
          tabCode: `tab${this.stepConfig.length + 1}`,
          inputFlag: false,
          fieldList: [],
          relationList: [],
          whenList: [],
          paramsList: []
        })
        this.currentTabCode = `tab${this.stepConfig.length}`
      }
      if (action === 'remove') {
        let tabs = this.stepConfig;
        tabs.forEach((tab, index) => {
          if (tab.tabCode === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              this.currentTabCode = nextTab.tabCode;
            }
          }
        })
        this.stepConfig = tabs.filter(tab => tab.tabCode !== targetName);
      }
    },
    tabsContent(tab, tabCode) {
      if (this.isDeveloper) {
        tab.inputFlag = true
      }
    },
    handleRow(list, index, type, model) {
      if (type === 'add') {
        if (model === 'field') {
          list.push({
            tableType: null,
            fieldName: null,
            fieldTitle: null,
            tag: null,
            busiType: null,
            mappingTableName: null,
            mappingFieldSet: null,
            description: null,
            mappingFieldName: null,
            mappingFieldType: null,
            mappingFieldTitle: null,
            devEdit:  true,
            isEdit:  true,
            isMain: true,
            isAdminCreate: true
          })
          this.getSpanArr(list, this.mergeFields)
        }
        if (model === 'when') {
          const item = list.find(item => !item.tableName || !item.fieldName || ((item.filterType !== 'eq' && item.filterType !== 'neq') && (!item.filterValue || !item.filterValue.length)))
          if (!item) {
            list.push({
              tableName: null,
              fieldName: null,
              filterType: null,
              filterValue: null,
              isShow: true
            })
            this.getSpanArr2(list, this.mergeFields2)
          } else {
            this.$message.warning('存在未设置筛选条件的空行, 请先完成设置')
          }
        }
        if (model === 'params') {
          list.push({
            name: null,
            type: null,
            value: null,
            description: null,
            devEdit: true,
            isEdit: true,
            isRequired: false,
            isShow: true
          })
        }
      }
      if (type === 'addLine') {
        const date = new Date()
        let uuid = [
          date.getFullYear(),
          (date.getMonth() + 1).toString().padStart(2, '0'),
          date.getDate().toString().padStart(2, '0'),
          date.getHours().toString().padStart(2, '0'),
          date.getMinutes().toString().padStart(2, '0'),
          date.getSeconds().toString().padStart(2, '0')
        ].join('')

        let row = list[index];
        let ic = list.length - 1;
        while (ic >= 0) {
          if (list[ic].tableType === row.tableType && list[ic].fieldName === row.fieldName && list[ic].mappingTableName === row.mappingTableName) {
            break
          }
          ic--
        }
        const items = row.mappingFieldSet.split(',');
        let currentIdx = 0
        items.forEach((item, i) => {
          const itemArr = item.split(':')
          currentIdx = (ic + 1) + i
          list.splice(currentIdx, 0, {
            tableType: row.tableType,
            fieldName: row.fieldName,
            fieldTitle: row.fieldTitle,
            tag: row.tag,
            busiType: row.busiType,
            mappingTableName: `请选择                              ${uuid}`,
            mappingFieldSet: row.mappingFieldSet,
            description: row.description,
            mappingFieldName: itemArr[0],
            mappingFieldTitle: itemArr[1],
            mappingFieldType: itemArr[2],
            mappingFieldValue: null,
            devEdit:  false,
            isEdit: true,
            isAdminCreate: false,
            isMain: i === 0
          })
        })

        console.log('下一行', list[currentIdx + 1]);
        const nextRow = list[currentIdx + 1]
        if (nextRow && (nextRow.tableType === row.tableType && nextRow.tag === row.tag && nextRow.fieldName !== row.fieldName)) {
          let nextIdx = 0
          let nextFieldRow = nextRow
          list.forEach((item, i) => {
            if (item.tableType === nextRow.tableType && item.tag === nextRow.tag && item.fieldName === nextRow.fieldName) {
              nextIdx = i
              nextFieldRow = item
            }
          })
          if (nextFieldRow) {
            this.handleRow(list, nextIdx, type)
          }
        }

        this.getSpanArr(list, this.mergeFields)
      }
      if (type === 'edit') {
        list[index]['devEdit'] = !list[index]['devEdit'];
        this.$set(list, index, list[index])
      }
      if (type === 'remove') {
        if (model === 'field') {
          let row = list[index];
          for (let i = list.length - 1; i >= 0; i--) {
            if (list[i].tableType === row.tableType && list[i].fieldName === row.fieldName && list[i].mappingTableName === row.mappingTableName) {
              list.splice(i, 1)
            }
          }
          this.getSpanArr(list, this.mergeFields)
        } else {
          list.splice(index, 1)
          model === 'when' && this.getSpanArr2(list, this.mergeFields2)
        }
      }
    },
    async initData() {
      const code = this.menuQuery.execParamsCode + '_' + this.$route.meta.engineeringId
      commonQueryByPost('queryExecParamsConfigByCode', {code: code}).then(async res => {
        if (res.data.length > 0) {
          this.stepType = 'business'
          this.form = {
            calculLogicCode: res.data[0].calcul_logic_code,
            remark: res.data[0].remark
          }
          this.stepConfig = JSON.parse(res.data[0].params)
          this.stepConfig.forEach(tab => {
            tab['cacheWhenList'] = []
            !this.showWhenSystemCreate && (tab.whenList = tab.whenList.filter(item => !item.type || item.type !== 'systemCreate'))
          })
          // 去掉空的
          this.stepConfig.forEach(tab => {
            tab.mainTypeOption = tab.mainTypeOption.filter(typeOption => {
              const rawValue = tab.fieldList[typeOption.value];
              return rawValue && rawValue.length >= 1;
            });
          });

          this.currentTabCode = this.stepConfig[0].tabCode
          this.currentMain = this.stepConfig[0].mainTypeOption[0].value

          this.getSpanArr(this.stepConfig[0].fieldList[this.currentMain], this.mergeFields)
          this.getSpanArr2(this.stepConfig[0].whenList, this.mergeFields2)
          this.getSpanArr3(this.stepConfig[0].relationList, this.mergeFields3)

          this.updateWhenList()
          this.refreshRelationList()
        } else {
          this.stepType = 'template'
          commonQueryByPost('query_exec_params_config_bace', {code: this.menuQuery.execParamsCode}).then(res => {
            this.form = {
              calculLogicCode: res.data[0].calcul_logic_code,
              remark: res.data[0].remark
            }
            this.stepConfig = JSON.parse(res.data[0].params)
            this.stepConfig.forEach(tab => tab['cacheWhenList'] = [])
            // 去掉空的
            this.stepConfig.forEach(tab => {
              tab.mainTypeOption = tab.mainTypeOption.filter(typeOption => {
                const rawValue = tab.fieldList[typeOption.value];
                return rawValue && rawValue.length >= 1;
              });
            });
            console.log('来自模板(原始):', res.data[0].params)
            console.log('来自模板:', JSON.parse(res.data[0].params))
            this.currentTabCode = this.stepConfig[0].tabCode
            this.currentMain = this.stepConfig[0].mainTypeOption[0].value

            this.getSpanArr(this.stepConfig[0].fieldList[this.currentMain], this.mergeFields)
            this.getSpanArr2(this.stepConfig[0].whenList, this.mergeFields2)
            this.getSpanArr3(this.stepConfig[0].relationList, this.mergeFields3)

            this.backfillFieldMapping()
            this.updateWhenList()
            this.refreshRelationList()
          })
          this.refreshRelationList()
        }
      })
    },
    refreshRelationList() {
      this.stepConfig.forEach(tab => {
        const mainTypes = Object.keys(tab.fieldList);
        let fieldList = []
        mainTypes.forEach(type => {
          fieldList = fieldList.concat(tab.fieldList[type])
        })
        const types = Array.from(
          fieldList.reduce((map, field) => {
            const key = `${field.tableType}-${field.tag ? field.tag : 'null'}-${field.mappingTableName}`
            if (!map.has(key)) {
              map.set(key, {
                tableType: field.tableType,
                tag: field.tag,
                mappingTableName: field.mappingTableName
              })
            }
            return map
          }, new Map()).values()
        )
        if (tab.relationList === null || tab.relationList.length === 0) {
          // 如果输出字段有变更, 执行一下下面这行的代码
          tab.relationList = [...types].map(item => ({... item, mappingFieldSet: null, mappingFieldName: null, mappingFieldType: null, mappingFieldTitle: null, description: null, mappingFieldValue: null, devEdit: true, isShow: true, isEdit: true}))
        } else {
          const orgRelationList = JSON.parse(JSON.stringify(tab.relationList))
          let initList = []
          const typeList = [...types]
          // 生成新的关联关系, 覆盖旧的
          typeList.forEach(item => {
            // 输出字段不发生变更的话, 类型始终能找到
            let type = orgRelationList.find(f => f.tableType === item.tableType && f.tag === item.tag)
            if (!!type && type.hasOwnProperty('mappingFieldSet') && !!type.mappingFieldSet) {
              const items = type.mappingFieldSet.split(',');
              items.forEach((it, i) => {
                const split = it.split(':');
                let org = orgRelationList.find(f => f.tableType === item.tableType && f.tag === item.tag && f.mappingTableName === item.mappingTableName && f.mappingFieldName === split[0]);
                initList.push({
                  ...item,
                  mappingFieldSet: type ? type.mappingFieldSet : null,
                  description: type ? type.description : null,

                  mappingFieldName: split[0],
                  mappingFieldTitle: split[1],
                  mappingFieldType: split[2],
                  mappingFieldValue: org ? org.mappingFieldValue : null,
                  devEdit: org ? org.devEdit : true,
                  isShow: org ? org.isShow : true,
                  isEdit: org ? org.isEdit : true
                })
              })
            } else {
              initList.push({
                ...item,
                mappingFieldSet: type ? type.mappingFieldSet : null,
                description: type ? type.description : null,
                mappingFieldName: type ? type.mappingFieldName : null,
                mappingFieldTitle: type ? type.mappingFieldTitle : null,
                mappingFieldType: type ? type.mappingFieldType : null,
                mappingFieldValue: type ? type.mappingFieldValue : null,
                devEdit: type ? type.devEdit : true,
                isShow: type ? type.isShow : true,
                isEdit: type ? type.isEdit : true
              })
            }
          })
          tab.relationList = initList
          this.backfillRelationMapping()
          console.log('已刷新关系列表')
        }

      })
      const currentTab = this.stepConfig.find(tab =>  tab.tabCode === this.currentTabCode)
      this.getSpanArr3(currentTab.relationList, this.mergeFields3)
    },
    saveTemplate() {
      const tabs = JSON.parse(JSON.stringify(this.stepConfig))
      tabs.forEach(tab => {
        tab.hasOwnProperty('cacheWhenList') && delete tab.cacheWhenList
        const types = Object.keys(tab.fieldList)
        types.forEach(type => {
          tab.fieldList[type] = Array.from(
            tab.fieldList[type].reduce((map, item) => {
              const key = `${item.fieldName}-${item.mappingFieldName}`
              item.busiType = null
              item.mappingTableName = null
              item.mappingFieldValue = null
              item.isAdminCreate = true
              if (item.mappingFieldSet) {
                if (item.mappingFieldSet.split(',').length > 1) {
                  const parts1 = item.mappingFieldSet.split(',')
                  const title = item.mappingFieldTitle
                  parts1.forEach(p => {
                    const parts = p.split(':')
                    if (parts[1] === title) {
                      item.mappingFieldType = parts[2]
                    }
                  })
                } else {
                  const parts = item.mappingFieldSet.split(':')
                  if (parts.length > 2) {
                    item.mappingFieldType = parts[2]
                  }
                }
              }
              map.set(key, item)
              return map
            }, new Map()).values()
          )
        })

        tab.whenList = []
      })
      commonQueryByPost('saveExecParamsConfigBase', {
        code: this.menuQuery.execParamsCode,
        params: JSON.stringify(tabs),
        ...this.form
      }).then(res => {
        this.$message.success('设置成功')
      })
    },
    saveStepConfig() {
      const code = this.menuQuery.execParamsCode + '_' + this.$route.meta.engineeringId
      const tabs = JSON.parse(JSON.stringify(this.stepConfig))
      let errorMessage = "";
      tabs.forEach(tab => {
        if (tab.hasOwnProperty('cacheWhenList')) {
          tab.whenList = [...tab.cacheWhenList, ...tab.whenList]
          delete tab.cacheWhenList
        }
        // 校验必填
        const types = Object.keys(tab.fieldList)
        types.forEach(type => {
            tab.fieldList[type].forEach(field => {
              if (field.isRequired && !field.mappingFieldValue) {
                errorMessage += field.mappingFieldTitle+ ";"
              }
            })
        });
      })
      if (errorMessage) {
        this.$message.error('字段：' + errorMessage.slice(0, -1) + ' 不能为空！')
        return
      }
      commonQueryByPost('saveExecParamsConfig', {
        code: code,
        params: JSON.stringify(tabs),
        ...this.form
      }).then(res => {
        this.$message.success('保存成功')
      })
    },

    handleLog() {
      this.getLogList()
      this.dialogVisibleLog = true
    },
    getLogList() {
      this.logLoading = true
      const code = this.menuQuery.execParamsCode + '_' + this.$route.meta.engineeringId
      commonQueryByPost('queryExecLog', {...this.page, code: code}).then(res => {
        this.logList = res.data.map(item => {
          const children = item.children?.value ? JSON.parse(item.children.value) : [];
          return {
            ...item,
            children: children
          }
        })
        this.page.total = res.total
        this.logLoading = false
      })
    },
    refreshLogList() {
      this.page.pageNum = 1
      this.page.pageSize = 10
      this.getLogList()
    },
    handleCloseLog() {
      this.dialogVisibleLog = false
    },
    copyText(row, target) {
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(row[target])
      } else {
        let textArea = document.createElement("textarea")
        textArea.value = row[target]
        textArea.style.position = "absolute"
        textArea.style.opacity = 0
        textArea.style.left = "-999999px"
        textArea.style.top = "-999999px"
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        textArea.remove()
      }
    },
    handleSizeChange(val) {
      this.page.pageSize = val
      this.getLogList()
    },
    handleCurrentChange(val) {
      this.page.pageNum = val
      this.getLogList()
    },

    getSpanArr(data, fields) {
      this.spanArrMap.clear();

      fields.forEach((currentField, currentIndex) => {
        // 关键修复：必须使用完整的层级路径（从第一个字段到当前字段）
        const levelFields = fields.slice(0, currentIndex + 1);
        this.calculateSpanArr(data, currentField, levelFields);
      });

      // 处理新增独立合并（与原有逻辑解耦）
      this.additionalMerges.forEach(({ field, dependencies }) => {
        // 新增规则使用自定义依赖字段（不影响原有层级）
        this.calculateSpanArr(data, field, dependencies);
      });

      // fields.forEach((field,index) => {
      //   const spanArr = [];
      //   let pos = 0;
      //   for (let i = 0; i < data.length; i++) {
      //     if (i === 0) {
      //       spanArr.push(1);
      //       pos = 0;
      //     } else {
      //       if(this.getSpanValue(data[i],index) === this.getSpanValue(data[i-1],index)){
      //         spanArr[pos]++;
      //         spanArr.push(0);
      //       } else {
      //         spanArr.push(1);
      //         pos = i;
      //       }
      //     }
      //   }
      //   this.spanArrMap.set(field, spanArr);
      // });
    },
    calculateSpanArr(data, targetField, compareFields) {
      const spanArr = [];
      let pos = 0;

      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1);
          pos = 0;
        } else {
          const currentKey = this.getCompareKey(data[i], compareFields);
          const prevKey = this.getCompareKey(data[i - 1], compareFields);

          if (currentKey === prevKey) {
            spanArr[pos]++;
            spanArr.push(0);
          } else {
            spanArr.push(1);
            pos = i;
          }
        }
      }
      this.spanArrMap.set(targetField, spanArr);
    },
    getCompareKey(row, fields) {
      return fields.map(field => row[field]?.toString() || '').join('|');
    },
    getSpanValue(row,index){
      let val=''
      for(let i=0; i<=index; i++){
        val+=row[this.mergeFields[i]]
      }
      return val;
    },
    spanMethod({ row, column, rowIndex, columnIndex }, fields) {
      const currentField = column.property;
      const spanArr = this.spanArrMap.get(currentField);

      if (!spanArr) return { rowspan: 1, colspan: 1 };

      const rowSpan = spanArr[rowIndex];
      return {
        rowspan: rowSpan,
        colspan: rowSpan > 0 ? 1 : 0
      };
      // const field = fields[columnIndex];
      // if (field) {
      //   const spanArr = this.spanArrMap.get(field);
      //   const _row = spanArr[rowIndex];
      //   const _col = _row > 0 ? 1 : 0;
      //   return {
      //     rowspan: _row,
      //     colspan: _col
      //   };
      // }
      // return {
      //   rowspan: 1,
      //   colspan: 1
      // };
    },

    getSpanArr2(data, fields) {
      this.spanArrMap2.clear();
      fields.forEach((field,index) => {
        const spanArr = [];
        let pos = 0;
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            spanArr.push(1);
            pos = 0;
          } else {
            if(this.getSpanValue2(data[i],index) === this.getSpanValue2(data[i-1],index)){
              spanArr[pos]++;
              spanArr.push(0);
            } else {
              spanArr.push(1);
              pos = i;
            }
          }
        }
        this.spanArrMap2.set(field, spanArr);
      });
    },
    getSpanValue2(row,index){
      let val=''
      for(let i=0; i<=index; i++){
        val+=row[this.mergeFields2[i]]
      }
      return val;
    },
    spanMethod2({ row, column, rowIndex, columnIndex }, fields) {
      const field = fields[columnIndex];
      if (field) {
        const spanArr = this.spanArrMap2.get(field);
        const _row = spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
      return {
        rowspan: 1,
        colspan: 1
      };
    },

    getSpanArr3(data, fields) {
      this.spanArrMap3.clear();
      fields.forEach((field,index) => {
        const spanArr = [];
        let pos = 0;
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            spanArr.push(1);
            pos = 0;
          } else {
            if(this.getSpanValue3(data[i],index) === this.getSpanValue3(data[i-1],index)){
              spanArr[pos]++;
              spanArr.push(0);
            } else {
              spanArr.push(1);
              pos = i;
            }
          }
        }
        this.spanArrMap3.set(field, spanArr);
      });
    },
    getSpanValue3(row,index){
      let val=''
      for(let i=0; i<=index; i++){
        val+=row[this.mergeFields3[i]]
      }
      return val;
    },
    spanMethod3({ row, column, rowIndex, columnIndex }, fields) {
      const field = fields[columnIndex];
      if (field) {
        const spanArr = this.spanArrMap3.get(field);
        const _row = spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
      return {
        rowspan: 1,
        colspan: 1
      };
    },
    backfillFieldMapping() {
      this.stepConfig.forEach(tab => {
        const types = Object.keys(tab.fieldList)
        types.forEach(type => {
          tab.fieldList[type].forEach(field => {
            const busiItem = this.busiTypeOption.find(b => b.value === field.tableType);
            field.busiType = !!busiItem ? busiItem.value : null
            if (!!busiItem && Object.keys(this.tableOption).includes(busiItem.value) && this.tableOption[busiItem.value].length > 0) {
              const defaultTableItem = this.tableOption[busiItem.value][0]
              field.mappingTableName = defaultTableItem.value
              if (Object.keys(this.fieldOption).includes(defaultTableItem.value)) {
                const mField = this.fieldOption[defaultTableItem.value].find(f => f.label === field.mappingFieldTitle || f.value === field.mappingFieldName);
                field.mappingFieldValue = !!mField ? mField.value : null
              }
            }
          })
        })
      })
    },
    backfillRelationMapping() {
      this.stepConfig.forEach(tab => {
        tab.relationList.forEach(field => {
          if (!!field.mappingTableName && this.fieldOption[field.mappingTableName]) {
            const mField = this.fieldOption[field.mappingTableName].find(f => f.value === field.mappingFieldName)
            field.mappingFieldValue = !!mField ? mField.value : null
          }
        })
      })
    },

    async handleDetailsExport() {
      const params = Object.assign({}, this.queryParams)
      let exportParam = {
        code: 'query_siteinfo_cgi_drawing',
        type: 'common',
        fileType: 'csv',
        paramMap: params
      }
      this.detailsExportLoading = true
      const data = await commonQueryExportData(exportParam).catch(() => {
        this.detailsExportLoading = false
        this.$message('导出异常')
      })
      this.detailsExportLoading = false
      if (data) {
        if (data.extend) {
          this.$message(data.msg)
        } else {
          window.open(process.env.VUE_APP_BASE_API + data.msg)
        }
      }
    },
  }
}
</script>
<style scoped lang="scss">
::v-deep .custom-hidden-column {
  /* 隐藏列的表头 */
  &.el-table__column {
    display: none !important;
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
    padding: 0 !important;
    border: none !important;
    visibility: hidden;
  }
  /* 隐藏列的表体内容 */
  &.el-table__cell {
    display: none !important;
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
    padding: 0 !important;
    border: none !important;
    visibility: hidden;
  }
}
::v-deep .el-table .table-blue-border-bottom {
  border-bottom: 2px solid #409EFF !important;
}
::v-deep {
  .el-dialog__body {
    padding: 0px 20px 30px;
  }
  .el-badge__content.is-fixed {
    font-size: 8px;
    top: 19px;
    right: -2px;
  }
  .item-class-danger .el-input--medium .el-input__inner {
    border-color: #f91b1b;
  }
  .item-class-warning .el-input--medium .el-input__inner {
    border-color: #ffd300;
  }
  .item--class-success .el-input--medium .el-input__inner {
    //border-color: #10cf0d;
  }
  .item-table-x .el-table--scrollable-x .el-table__body-wrapper {
    overflow-x: hidden;
  }
}
</style>
