import request from '@/utils/request'

export function qryConfigs(){
    return request({
        url: '/hvpAn/perCfgRuleType/list',
        method: 'GET'
    })
}

export function qryTypes(id){
    return request({
        url: '/hvpAn/perCfgRuleType/qryTypes/' + id,
        method: 'GET'
    })
}

export function configSave(data){
    return request({
        url: '/hvpAn/perCfgRuleType/save',
        method: 'POST',
        data: data
    })
}


export function configRemove(id){
    return request({
        url: '/hvpAn/perCfgRuleType/removeOne/' + id,
        method: 'GET'
    })
}
