import resourceConfigHelper from '@/utils/resourceConfigHelper'
import NodeStageEnum from '@/utils/nodeStageEnum'

/**
 * Schema资源配置混入
 * 整合了原 logicBaseStationConfigMixin 和 resourceConfigMixin 的功能
 * 为组件提供完整的资源配置相关的方法和数据
 */
export default {
  props: {
    // 库类型 (XQK/GHK/SJK )
    libraryType: {
      type: String,
      default: ''
    },

    // 是否启用权限控制
    enableResourceConfig: {
      type: Boolean,
      default: true
    },

    // 是否显示权限提示
    showPermissionAlert: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      // 资源权限缓存
      resourcePermissions: {},
      // 按钮配置缓存
      buttonVisibility: {},
      // 配置加载状态
      configLoading: false,

      // 资源类型映射
      resourceTypeMapping: {
        enodeb: 'enodeb',
        baseBand: 'baseBand',
        cell: 'cell',
        rru: 'rru',
        antenna: 'antenna',
        equipmentRoom: 'equipmentRoom',
        rooftop: 'rooftop',
        rack: 'rack',
        supporting: 'supporting',
        platform: 'platform',
        boardCard: 'boardCard',
        extendDevice: 'extendDevice',
      },

      // 权限检查缓存
      permissionCheckCache: {},

      // 操作日志
      resourceOperationLogs: []
    }
  },

  computed: {
    /**
     * 获取库类型中文名称
     */
    libraryTypeName() {
      return NodeStageEnum.getByCode(this.libraryType)
    },

  },

  async created() {
    if (this.enableResourceConfig) {
      await this.initSchemaResourceConfig()
    }
  },

  methods: {
    /**
     * 初始化Schema资源配置（整合原LogicBaseStation配置）
     */
    async initSchemaResourceConfig() {
      try {
        const resourceTypes = Object.values(this.resourceTypeMapping)
        await this.initResourcePermissions(this.libraryType, resourceTypes)

        if (this.showPermissionAlert) {
          this.showResourceConfigInfo()
        }
      } catch (error) {
        console.error('初始化Schema资源配置失败:', error)
      }
    },

    /**
     * 显示资源配置信息
     */
    showResourceConfigInfo() {
      const hasRestrictedPermissions = this.checkRestrictedPermissions()
      if (hasRestrictedPermissions && this.$message) {
        this.$message({
          message: `当前${this.libraryTypeName}中部分资源操作权限受限，请查看配置`,
          type: 'warning',
          duration: 3000
        })
      }
    },

    /**
     * 检查是否有受限权限
     */
    checkRestrictedPermissions() {
      const resourceTypes = Object.values(this.resourceTypeMapping)
      return resourceTypes.some(resourceType => {
        const canAdd = this.hasResourcePermission(this.normalizedLibraryType, resourceType, 'add')
        const canDelete = this.hasResourcePermission(this.normalizedLibraryType, resourceType, 'delete')
        return !canAdd || !canDelete
      })
    },

    /**
     * 检查资源是否可以添加
     * @param {string} libraryType - 库类型 (demand/planning/design)
     * @param {string} resourceType - 资源类型
     * @returns {Promise<boolean>}
     */
    async canAddResource(libraryType, resourceType) {
      // console.log('this.resourcePermissions', this.resourcePermissions)
      const cacheKey = `${libraryType}_${resourceType}_add`

      if (this.resourcePermissions[cacheKey] !== undefined) {
        return this.resourcePermissions[cacheKey]
      }

      const canAdd = await resourceConfigHelper.canAddResource(libraryType, resourceType)
      this.$set(this.resourcePermissions, cacheKey, canAdd)
      return canAdd
    },

    /**
     * 检查资源是否可以删除
     * @param {string} libraryType - 库类型 (demand/planning/design)
     * @param {string} resourceType - 资源类型
     * @returns {Promise<boolean>}
     */
    async canDeleteResource(libraryType, resourceType) {
      // console.log('this.resourcePermissions', this.resourcePermissions)

      const cacheKey = `${libraryType}_${resourceType}_delete`

      if (this.resourcePermissions[cacheKey] !== undefined) {
        return this.resourcePermissions[cacheKey]
      }

      const canDelete = await resourceConfigHelper.canDeleteResource(libraryType, resourceType)
      this.$set(this.resourcePermissions, cacheKey, canDelete)
      return canDelete
    },

    /**
     * 检查按钮是否应该显示
     * @param {string} buttonCode - 按钮代码
     * @returns {Promise<boolean>}
     */
    async shouldShowButton(buttonCode) {
      if (this.buttonVisibility[buttonCode] !== undefined) {
        return this.buttonVisibility[buttonCode]
      }

      const shouldShow = await resourceConfigHelper.shouldShowButton(buttonCode)
      this.$set(this.buttonVisibility, buttonCode, shouldShow)
      return shouldShow
    },

    /**
     * 获取资源操作权限
     * @param {string} libraryType - 库类型
     * @param {string} resourceType - 资源类型
     * @returns {Promise<Object>} {canAdd, canDelete}
     */
    async getResourcePermissions(libraryType, resourceType) {
      const permissions = await resourceConfigHelper.getResourcePermissions(libraryType, resourceType)

      // 缓存权限
      const addKey = `${libraryType}_${resourceType}_add`
      const deleteKey = `${libraryType}_${resourceType}_delete`
      this.$set(this.resourcePermissions, addKey, permissions.canAdd)
      this.$set(this.resourcePermissions, deleteKey, permissions.canDelete)

      return permissions
    },

    /**
     * 批量获取资源权限
     * @param {string} libraryType - 库类型
     * @param {Array<string>} resourceTypes - 资源类型数组
     * @returns {Promise<Object>}
     */
    async getBatchResourcePermissions(libraryType, resourceTypes) {
      const permissions = await resourceConfigHelper.getBatchResourcePermissions(libraryType, resourceTypes)

      // 缓存权限
      Object.entries(permissions).forEach(([resourceType, permission]) => {
        const addKey = `${libraryType}_${resourceType}_add`
        const deleteKey = `${libraryType}_${resourceType}_delete`
        this.$set(this.resourcePermissions, addKey, permission.canAdd)
        this.$set(this.resourcePermissions, deleteKey, permission.canDelete)
      })

      return permissions
    },

    /**
     * 过滤按钮，只返回应该显示的按钮
     * @param {Object} buttons - 原始按钮对象
     * @returns {Promise<Object>}
     */
    async filterVisibleButtons(buttons) {
      const filteredButtons = await resourceConfigHelper.filterButtons(buttons)

      // 缓存按钮可见性
      Object.keys(buttons).forEach(buttonCode => {
        this.$set(this.buttonVisibility, buttonCode, filteredButtons.hasOwnProperty(buttonCode))
      })

      return filteredButtons
    },

    /**
     * 初始化资源权限配置
     * @param {string} libraryType - 库类型
     * @param {Array<string>} resourceTypes - 资源类型数组
     */
    async initResourcePermissions(libraryType, resourceTypes = []) {
      this.configLoading = true

      try {
        if (resourceTypes.length > 0) {
          await this.getBatchResourcePermissions(libraryType, resourceTypes)
        } else {
          // 如果没有指定资源类型，获取所有默认资源类型的权限
          const defaultResourceTypes = [
            'eondeb', 'baseBand', 'cell', 'rru',
            'antenna', 'platform', 'equipmentRoom', 'rooftop', 'supporting',
            'rack', 'boardCard', 'extendDevice'
          ]
          await this.getBatchResourcePermissions(libraryType, defaultResourceTypes)
        }
      } catch (error) {
        console.error('初始化资源权限失败:', error)
        this.$message.error('加载资源权限配置失败')
      } finally {
        this.configLoading = false
      }
    },

    /**
     * 初始化按钮配置
     * @param {Array<string>} buttonCodes - 按钮代码数组
     */
    async initButtonConfig(buttonCodes = []) {
      this.configLoading = true

      try {
        const defaultButtonCodes = buttonCodes.length > 0 ? buttonCodes : [
          'approve', 'reject', 'transfer', 'return', 'save', 'submit', 'cancel'
        ]

        const promises = defaultButtonCodes.map(buttonCode => this.shouldShowButton(buttonCode))
        await Promise.all(promises)
      } catch (error) {
        console.error('初始化按钮配置失败:', error)
        this.$message.error('加载按钮配置失败')
      } finally {
        this.configLoading = false
      }
    },

    /**
     * 清除配置缓存
     */
    clearConfigCache() {
      this.resourcePermissions = {}
      this.buttonVisibility = {}
      resourceConfigHelper.clearCache()
    },

    /**
     * 刷新配置
     * @param {string} libraryType - 库类型
     * @param {Array<string>} resourceTypes - 资源类型数组
     * @param {Array<string>} buttonCodes - 按钮代码数组
     */
    async refreshConfig(libraryType, resourceTypes = [], buttonCodes = []) {
      this.clearConfigCache()

      const promises = []
      if (libraryType) {
        promises.push(this.initResourcePermissions(libraryType, resourceTypes))
      }
      if (buttonCodes.length > 0 || !libraryType) {
        promises.push(this.initButtonConfig(buttonCodes))
      }

      await Promise.all(promises)
    },

    /**
     * 检查是否有资源操作权限
     * @param {string} libraryType - 库类型
     * @param {string} resourceType - 资源类型
     * @param {string} operation - 操作类型 (add/delete)
     * @returns {boolean}
     */
    hasResourcePermission(libraryType, resourceType, operation) {
      // console.log('this.resourcePermissions', this.resourcePermissions, libraryType, resourceType, operation)
      const cacheKey = `${libraryType}_${resourceType}_${operation}`
      return this.resourcePermissions[cacheKey] !== false
    },

    /**
     * 检查按钮是否可见
     * @param {string} buttonCode - 按钮代码
     * @returns {boolean}
     */
    isButtonVisible(buttonCode) {
      return this.buttonVisibility[buttonCode] !== false
    },

    /**
     * 获取资源按钮可见性配置
     * @param {string} libraryType - 库类型
     * @param {string} resourceType - 资源类型
     * @returns {Object} {showAdd, showDelete}
     */
    getResourceButtonVisibility(libraryType, resourceType) {
      const canAdd = this.hasResourcePermission(libraryType, resourceType, 'add')
      const canDelete = this.hasResourcePermission(libraryType, resourceType, 'delete')

      return {
        showAdd: canAdd,
        showDelete: canDelete
      }
    },

    /**
     * 生成资源操作按钮配置
     * @param {string} libraryType - 库类型
     * @param {string} resourceType - 资源类型
     * @param {Object} callbacks - 回调函数 {onAdd, onDelete}
     * @returns {Array} 按钮配置数组
     */
    generateResourceButtons(libraryType, resourceType, callbacks = {}) {
      const buttons = []
      const visibility = this.getResourceButtonVisibility(libraryType, resourceType)

      if (visibility.showAdd && callbacks.onAdd) {
        buttons.push({
          type: 'primary',
          icon: 'el-icon-plus',
          text: '添加',
          handler: callbacks.onAdd
        })
      }

      if (visibility.showDelete && callbacks.onDelete) {
        buttons.push({
          type: 'danger',
          icon: 'el-icon-delete',
          text: '删除',
          handler: callbacks.onDelete
        })
      }

      return buttons
    }
  },

  /**
   * 组件销毁时清理
   */
  beforeDestroy() {
    this.permissionCheckCache = {}
    this.resourceOperationLogs = []
    // 清除组件级别的缓存，但保留全局缓存
    this.resourcePermissions = {}
    this.buttonVisibility = {}
  }
}
