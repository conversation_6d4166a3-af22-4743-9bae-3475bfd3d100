<template>
  <div>
    <el-row style="padding: 20px 0px 20px;" type=“flex” align=“middle”>
      <el-col :span="1" style="text-align: end;padding-top: 7px;">单号：</el-col>
      <el-col :span="4">
        <el-input v-model="queryParams.orderNo" clearable placeholder="按Enter搜索" @keyup.enter.native="searchHandle">
          <el-button slot="append" icon="el-icon-search" @click="searchHandle">搜索</el-button>
        </el-input>
      </el-col>

    </el-row>
    <el-row style="padding: 3px;display: ruby">
      <el-button type="primary" size="mini" style="margin-left: 10px" v-has-permi="['btn:DIIRFV_qry_page:exp']" @click="templateExport">模版导出</el-button>
      <FileUpload style="width: min-content;margin-left: 10px" @input="inputHandle" btnType="btn" :fileType="['xlsx']" :isShowTip="false" triggerButtonTitle="数据导入" :uploadUrl="uploadUrl" v-has-permi="['btn:DIIRFV_qry_page:import']" />
      <el-button style="margin-left: 10px"  type="danger" size="mini" @click="deleteOrderNos" v-has-permi="['btn:DIIRFV_qry_page:delete']">删除选中</el-button>
    </el-row>
    <el-table ref="multipleTable" border v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" element-loading-text="加载中" >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="单号"  align="center" prop="orderNo" show-overflow-tooltip/>
      <el-table-column label="导入日期" align="center" prop="importDate"  />
      <el-table-column label="导入账号" align="center" prop="importAccount" show-overflow-tooltip/>
      <el-table-column label="备注"  align="center" prop="remark" show-overflow-tooltip/>
    </el-table>
    <pagination @pagination="pagination" :total="page.total" :limit="page.pageSize" :page="page.pageNo"/>
  </div>
</template>
<script>
import * as planApi from '@/api/site/plan/handle'
import * as kernelApi from '@/api/kernel/query'

export default {
  name: 'SkipFlowOrder',
  data() {
    return {
      queryParams: {
        orderNo: ''
      },
      tableData:[],
      loading: false,
      page:{ total:0, pageNo:1, pageSize: 10 },
      uploadUrl: '/site/flowSkipOrder/importData',
    }
  },
  created() {
    this.findPage();

  },
  methods: {
    async templateExport(){
      planApi.flowSkipOrderExportTemplate();
    },
    inputHandle(res){
      this.findPage();
    },
    async deleteOrderNos(){
      const selectIdArr = this.selectionEnodebInfoArr;
      if(selectIdArr && selectIdArr.length>0){
        const promise = await planApi.flowSkipOrderDeleteByIds({'id': selectIdArr});
        if(promise.code===200){
          this.$message.success('成功');
          this.findPage();
        }else{
          this.$message.error('失败,'+promise.msg)
        }
      }
    },
    // 多选框选中数据
    async handleSelectionChange(selection) {
      this.selectionEnodebInfoArr = [];
      let tmpArr = [];
      for(const item of selection){
        if(item.id){
          tmpArr.push(item.id);
        }
      }
      this.selectionEnodebInfoArr = tmpArr
    },
    pagination(page){
      this.page.pageNo=page.page;
      this.page.pageSize = page.limit;
      this.findPage();
    },
    async findPage(){
      const pageNo = this.page.pageNo;
      const pageSize = this.page.pageSize;
      const page = {
        pageOffset:(pageNo - 1) * pageSize,
        pageSize: this.page.pageSize,
        pageNum:pageNo,
      }
      let pageTmp = {...page, ...this.queryParams};//合并两个对象
      this.loading = true;
      const promise = await kernelApi.commonQuery('DIIRFV_qry_page',pageTmp);
      this.loading=false;
      if(promise && promise.code === 200){
        this.tableData = promise.data
        this.page.total=promise.total;
      }
    },
    searchHandle(){
      this.page.pageNo=1;
      this.page.pageSize =10;
      this.findPage();
    }
  },
  components:{
    Pagination:() => import('@/components/BasicComponents/Table/Pagination/index.vue'),
    FileUpload: () => import('@/components/FileUpload'),

  }
}
</script>
<style>

</style>
