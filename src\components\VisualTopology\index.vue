<template>
  <div class="visual-topology">
    <!-- 工具栏 -->
    <div class="topology-toolbar">
      <el-button-group>
        <el-button icon="el-icon-plus" @click="addNode">添加节点</el-button>
        <el-button icon="el-icon-delete" @click="deleteSelected" :disabled="!selectedNode">删除节点</el-button>
        <el-button icon="el-icon-refresh" @click="resetGraph">重置</el-button>
        <el-button icon="el-icon-zoom-in" @click="zoomIn">放大</el-button>
        <el-button icon="el-icon-zoom-out" @click="zoomOut">缩小</el-button>
        <el-button icon="el-icon-download" @click="exportData">导出</el-button>
        <el-button icon="el-icon-upload" @click="importDialogVisible = true">导入</el-button>
      </el-button-group>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="topology-content">
      <!-- 画布区域 -->
      <div class="topology-canvas" ref="container"></div>
      
      <!-- 属性面板 -->
      <div class="topology-panel" v-if="selectedNode">
        <h3>节点属性</h3>
        <el-form label-width="80px" size="small">
          <el-form-item label="节点名称">
            <el-input v-model="selectedNode.label" @change="updateNode"></el-input>
          </el-form-item>
          <el-form-item label="节点类型">
            <el-select v-model="selectedNode.nodeType" @change="updateNode" style="width: 100%">
              <el-option label="默认" value="default"></el-option>
              <el-option label="服务器" value="server"></el-option>
              <el-option label="路由器" value="router"></el-option>
              <el-option label="交换机" value="switch"></el-option>
              <el-option label="终端" value="terminal"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="节点颜色">
            <el-color-picker v-model="selectedNode.color" @change="updateNode"></el-color-picker>
          </el-form-item>
          <el-form-item label="描述">
            <el-input type="textarea" v-model="selectedNode.description" @change="updateNode"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <!-- 导入对话框 -->
    <el-dialog title="导入拓扑数据" :visible.sync="importDialogVisible" width="500px">
      <el-input type="textarea" :rows="10" placeholder="请粘贴JSON格式的拓扑数据" v-model="importData"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleImport">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import G6 from '@antv/g6'

export default {
  name: 'VisualTopology',
  data() {
    return {
      graph: null,
      nodes: [],
      edges: [],
      selectedNode: null,
      importDialogVisible: false,
      importData: '',
      nodeIdCounter: 0
    }
  },
  mounted() {
    this.initGraph()
    // 监听窗口大小变化，调整画布大小
    window.addEventListener('resize', this.resizeGraph)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeGraph)
    if (this.graph) {
      this.graph.destroy()
    }
  },
  methods: {
    // 初始化图形
    initGraph() {
      // 注册自定义节点
      this.registerCustomNodes()
      
      // 创建G6图实例
      this.graph = new G6.Graph({
        container: this.$refs.container,
        width: this.$refs.container.offsetWidth || 800,
        height: this.$refs.container.offsetHeight || 600,
        modes: {
          default: [
            'drag-canvas',
            'zoom-canvas',
            'drag-node',
            'click-select'
          ],
        },
        defaultNode: {
          type: 'custom-node',
          size: [120, 40],
          style: {
            fill: '#E1F5FE',
            stroke: '#1890FF',
            radius: 4,
            shadowColor: '#ccc',
            shadowBlur: 0,
            lineWidth: 1
          },
          labelCfg: {
            style: {
              fill: '#333',
              fontSize: 12
            }
          }
        },
        defaultEdge: {
          type: 'cubic-horizontal',
          style: {
            stroke: '#A3B1BF',
            lineWidth: 2,
            endArrow: {
              path: G6.Arrow.triangle(8, 10, 0),
              fill: '#A3B1BF'
            }
          },
          labelCfg: {
            autoRotate: true,
            style: {
              fill: '#666',
              fontSize: 12
            }
          }
        },
        layout: {
          type: 'dagre',
          rankdir: 'LR',
          nodesep: 80,
          ranksep: 100
        },
        animate: true,
        fitView: true,
        fitViewPadding: [20, 40, 20, 40]
      })
      
      // 绑定事件
      this.bindEvents()
      
      // 渲染初始数据
      this.renderGraph()
    },
    
    // 注册自定义节点
    registerCustomNodes() {
      // 自定义节点，支持不同类型的样式
      G6.registerNode('custom-node', {
        draw(cfg, group) {
          const { nodeType = 'default', color } = cfg
          
          // 根据节点类型设置不同的形状和样式
          let shape
          const width = 120
          const height = 40
          
          const fillColor = color || '#E1F5FE'
          const strokeColor = color ? color : '#1890FF'
          
          // 根据节点类型绘制不同形状
          switch(nodeType) {
            case 'server':
              // 服务器形状
              shape = group.addShape('rect', {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width,
                  height,
                  fill: fillColor,
                  stroke: strokeColor,
                  radius: 2
                },
                name: 'node-shape'
              })
              
              // 添加服务器图标
              group.addShape('rect', {
                attrs: {
                  x: -width / 2 + 10,
                  y: -height / 2 + 10,
                  width: 20,
                  height: 20,
                  fill: strokeColor,
                  radius: 2
                },
                name: 'node-icon'
              })
              break
              
            case 'router':
              // 路由器形状
              shape = group.addShape('rect', {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width,
                  height,
                  fill: fillColor,
                  stroke: strokeColor,
                  radius: 8
                },
                name: 'node-shape'
              })
              
              // 添加路由器图标
              group.addShape('circle', {
                attrs: {
                  x: -width / 2 + 20,
                  y: -height / 2 + 20,
                  r: 8,
                  fill: strokeColor
                },
                name: 'node-icon'
              })
              break
              
            case 'switch':
              // 交换机形状
              shape = group.addShape('rect', {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width,
                  height,
                  fill: fillColor,
                  stroke: strokeColor,
                  radius: 2
                },
                name: 'node-shape'
              })
              
              // 添加交换机图标
              group.addShape('rect', {
                attrs: {
                  x: -width / 2 + 10,
                  y: -height / 2 + 15,
                  width: 20,
                  height: 10,
                  fill: strokeColor
                },
                name: 'node-icon'
              })
              break
              
            case 'terminal':
              // 终端形状
              shape = group.addShape('rect', {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width,
                  height,
                  fill: fillColor,
                  stroke: strokeColor,
                  radius: 4
                },
                name: 'node-shape'
              })
              
              // 添加终端图标
              group.addShape('rect', {
                attrs: {
                  x: -width / 2 + 10,
                  y: -height / 2 + 10,
                  width: 16,
                  height: 12,
                  fill: strokeColor,
                  radius: 1
                },
                name: 'node-icon'
              })
              
              group.addShape('rect', {
                attrs: {
                  x: -width / 2 + 14,
                  y: -height / 2 + 22,
                  width: 8,
                  height: 6,
                  fill: strokeColor
                },
                name: 'node-icon-base'
              })
              break
              
            default:
              // 默认节点形状
              shape = group.addShape('rect', {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width,
                  height,
                  fill: fillColor,
                  stroke: strokeColor,
                  radius: 4
                },
                name: 'node-shape'
              })
          }
          
          // 文本标签
          group.addShape('text', {
            attrs: {
              text: cfg.label,
              x: nodeType === 'default' ? 0 : 10,
              y: 0,
              fontSize: 12,
              textAlign: nodeType === 'default' ? 'center' : 'left',
              textBaseline: 'middle',
              fill: '#333'
            },
            name: 'node-label'
          })
          
          return shape
        },
        // 响应状态变化
        setState(name, value, item) {
          const group = item.getContainer()
          const shape = group.get('children').find(child => child.get('name') === 'node-shape')
          
          if (name === 'selected') {
            if (value) {
              shape.attr('shadowColor', '#1890FF')
              shape.attr('shadowBlur', 10)
              shape.attr('lineWidth', 2)
            } else {
              shape.attr('shadowColor', '#ccc')
              shape.attr('shadowBlur', 0)
              shape.attr('lineWidth', 1)
            }
          }
        }
      })
    },
    
    // 绑定事件
    bindEvents() {
      // 节点点击事件
      this.graph.on('node:click', evt => {
        const node = evt.item.getModel()
        this.selectedNode = { ...node }
      })
      
      // 画布点击事件，取消选中
      this.graph.on('canvas:click', () => {
        this.selectedNode = null
      })
      
      // 节点拖拽结束事件
      this.graph.on('node:dragend', evt => {
        const model = evt.item.getModel()
        const node = this.nodes.find(n => n.id === model.id)
        if (node) {
          node.x = model.x
          node.y = model.y
        }
      })
      
      // 添加边的交互
      this.graph.on('node:dblclick', evt => {
        const sourceNode = evt.item.getModel()
        
        // 进入添加边模式
        this.$message.info('请点击目标节点创建连接')
        
        const handleClick = e => {
          // 检查点击的是否为节点
          if (e.item && e.item.getType() === 'node') {
            const targetNode = e.item.getModel()
            
            // 不允许自环
            if (sourceNode.id === targetNode.id) {
              this.$message.warning('不能连接到自身')
              return
            }
            
            // 检查是否已存在该边
            const edgeExists = this.edges.some(edge => 
              edge.source === sourceNode.id && edge.target === targetNode.id
            )
            
            if (edgeExists) {
              this.$message.warning('连接已存在')
            } else {
              // 创建新边
              const newEdge = {
                id: `edge-${Date.now()}`,
                source: sourceNode.id,
                target: targetNode.id,
                label: '连接'
              }
              
              this.edges.push(newEdge)
              this.renderGraph()
              this.$message.success('连接创建成功')
            }
          }
          
          // 移除临时事件监听
          this.graph.off('node:click', handleClick)
        }
        
        // 添加临时事件监听
        this.graph.on('node:click', handleClick)
      })
      
      // 边点击事件
      this.graph.on('edge:click', evt => {
        const edge = evt.item.getModel()
        // 可以在这里实现边的选中和编辑功能
      })
    },
    
    // 渲染图形
    renderGraph() {
      const data = {
        nodes: this.nodes,
        edges: this.edges
      }
      
      this.graph.data(data)
      this.graph.render()
      
      if (this.nodes.length > 0) {
        this.graph.fitView()
      }
    },
    
    // 添加节点
    addNode() {
      const id = `node-${++this.nodeIdCounter}`
      const newNode = {
        id,
        label: `节点 ${this.nodeIdCounter}`,
        nodeType: 'default',
        description: '',
        x: 300 + Math.random() * 100,
        y: 300 + Math.random() * 100,
        color: '#E1F5FE'
      }
      
      this.nodes.push(newNode)
      this.renderGraph()
      
      // 选中新添加的节点
      this.selectedNode = { ...newNode }
    },
    
    // 删除选中的节点
    deleteSelected() {
      if (!this.selectedNode) return
      
      // 删除节点
      this.nodes = this.nodes.filter(node => node.id !== this.selectedNode.id)
      
      // 删除相关的边
      this.edges = this.edges.filter(edge => 
        edge.source !== this.selectedNode.id && edge.target !== this.selectedNode.id
      )
      
      this.selectedNode = null
      this.renderGraph()
    },
    
    // 更新节点属性
    updateNode() {
      if (!this.selectedNode) return
      
      const index = this.nodes.findIndex(node => node.id === this.selectedNode.id)
      if (index !== -1) {
        this.nodes[index] = { ...this.selectedNode }
        this.renderGraph()
      }
    },
    
    // 重置图形
    resetGraph() {
      this.$confirm('确定要清空当前拓扑图吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.nodes = []
        this.edges = []
        this.selectedNode = null
        this.nodeIdCounter = 0
        this.renderGraph()
      }).catch(() => {})
    },
    
    // 导出数据
    exportData() {
      const data = {
        nodes: this.nodes,
        edges: this.edges
      }
      
      const jsonStr = JSON.stringify(data, null, 2)
      const blob = new Blob([jsonStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = 'topology-' + new Date().getTime() + '.json'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    },
    
    // 导入数据
    handleImport() {
      try {
        const data = JSON.parse(this.importData)
        
        if (!data.nodes || !Array.isArray(data.nodes)) {
          throw new Error('无效的节点数据')
        }
        
        if (!data.edges || !Array.isArray(data.edges)) {
          throw new Error('无效的边数据')
        }
        
        this.nodes = data.nodes
        this.edges = data.edges
        
        // 更新节点计数器
        const nodeIds = this.nodes.map(node => {
          const match = node.id.match(/node-(\d+)/)
          return match ? parseInt(match[1]) : 0
        })
        
        this.nodeIdCounter = Math.max(0, ...nodeIds)
        
        this.renderGraph()
        this.importDialogVisible = false
        this.$message.success('导入成功')
      } catch (error) {
        this.$message.error('导入失败: ' + error.message)
      }
    },
    
    // 缩放控制
    zoomIn() {
      this.graph.zoom(1.2)
    },
    
    zoomOut() {
      this.graph.zoom(0.8)
    },
    
    // 调整画布大小
    resizeGraph() {
      if (this.graph && this.$refs.container) {
        const width = this.$refs.container.offsetWidth
        const height = this.$refs.container.offsetHeight
        this.graph.changeSize(width, height)
        this.graph.fitView()
      }
    }
  }
}
</script>

<style scoped>
.visual-topology {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
}

.topology-toolbar {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
}

.topology-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.topology-canvas {
  flex: 1;
  background-color: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  margin: 10px;
  position: relative;
  overflow: hidden;
}

.topology-panel {
  width: 280px;
  padding: 15px;
  background-color: #fff;
  border-left: 1px solid #e6e6e6;
  overflow-y: auto;
}

.topology-panel h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
</style>