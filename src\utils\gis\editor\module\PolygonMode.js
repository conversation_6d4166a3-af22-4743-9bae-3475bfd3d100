import {BasicMode, PrimitiveType} from "./BasicMode";
import { ActiveState } from '@/utils/gis/editor'
import * as Cesium from "cesium";
import {unionPolygon} from "@/utils/gis/tools/common";

export class PolygonMode extends BasicMode {
  constructor(editor) {
    super(editor);
  }

  clear() {
    super.clear()
  }

  onLeftClick(event) {
    super.onLeftClick(event)

    if (this.editor.actived === ActiveState.ADD) {
      let cartographic = this._getCartesian3(event.position)
      if (cartographic) {
        this.entityPoints.push(cartographic)
        if (this.entityPoints.length === 1) {
          // 两个点时绘制直线, 否则看不见
          this.entityPoints.push(cartographic)
          this.prevPolyline = this._drawPolyline(true)
        } else if (this.entityPoints.length === 3) {
          this.prevPolygon = this._drawPolygon()
        }
      }
    }
  }

  onLeftDown(event) {
    super.onLeftDown(event)

    if (this.editor.actived === ActiveState.EDIT) {
      let primary = this.editor.viewer.scene.pick(event.position)
      // 拾取到对象 判断拾取到的对象类型
      if (!primary || !primary.primitive) {
        this.editor.actived = ActiveState.ADD
        return
      }
      //拾取到具有type 属性的entity对象
      if (
        primary.primitive?.type === PrimitiveType.Vertex ||
        primary.primitive?.type === PrimitiveType.CenterPoint
      ) {
        this.isEditing = true
        //禁用场景的旋转移动功能 保留缩放功能
        this.editor.viewer.scene.screenSpaceCameraController.enableRotate = false
        //改变鼠标状态
        this.editor.viewer.enableCursorStyle = false
        this.editor.viewer.container.style.cursor = 'move'
        this.editVertext = primary.primitive
        this.editVertext.show = false
        this._clearMidpoints()
      }
      // 移动中心点
      else if (primary.primitive?.type === PrimitiveType.Midpoint) {
        const index = [...primary.primitive.vertexIndex]
        if (primary.primitive.isHole) {
          this.editEntity.datasources[index.shift()].holes[
            index.shift()
          ].splice(index.shift() + 1, 0, primary.primitive.position)
        } else {
          this.editEntity.datasources[index.shift()].positions.splice(
            index.shift() + 1,
            0,
            primary.primitive.position
          )
        }
        this._pickEntity()
        this.isEditing = true
      }
      else {
        // 新增
        this.editor.actived = ActiveState.ADD
      }
    }
  }

  onLeftUp(event) {
    if (this.editor.actived === ActiveState.EDIT) {
      if (!this.isEditing) return
      this.editor.viewer.enableCursorStyle = true
      this.editor.viewer.container.style.cursor = 'default'
      this.editor.viewer.scene.screenSpaceCameraController.enableRotate = true
      this._pickEntity()
    }
    super.onLeftUp(event)
  }

  onLeftDoubleClick(event) {
    super.onLeftDoubleClick(event)
  }

  onRightClick(event) {
    if (this.editor.actived === ActiveState.ADD) {
      this.prevPolyline && this.editor.dataSource.entities.remove(this.prevPolyline)
      this.prevPolygon && this.editor.dataSource.entities.remove(this.prevPolygon)
    } else if (this.editor.actived === ActiveState.EDIT) {
      let pickFeatures = this.editor.viewer.scene.drillPick(event.position)
      // 拾取到对象 判断拾取到的对象类型
      let primary = pickFeatures.find(p => p.primitive && p.primitive.type)
      if (primary && primary.primitive.type === PrimitiveType.Vertex) {
        const index = [...primary.primitive.vertexIndex]
        if (primary.primitive.isHole) {
          const holes = this.editEntity.datasources[index[0]].holes[index[1]]
          if (this._filterEqualsEpsilon(holes).length === 3) {
            this.editor.dataSource.entities.remove(this.editEntity.datasources[index[0]])
            this.editEntity.datasources[index[0]].holes.splice(index[1], 1)
            this._reloadDatasource(index[0])
          } else holes.splice(index[2], 1)
        } else {
          const positions = this.editEntity.datasources[index[0]].positions
          if (this._filterEqualsEpsilon(positions).length === 3) {
            this.editor.dataSource.entities.remove(this.editEntity.datasources[index[0]])
            this.editEntity.datasources.splice(index[0], 1)
          } else positions.splice(index[1], 1)
        }
        this._pickEntity()
        this.isEditing = true
        return
      }
      primary = pickFeatures.find(p => p.id && p.id.type)
      if (primary && primary.id.type === PrimitiveType.Entity) {
        // 右键菜单
        this.editor.menu.curr.items = [{
          label: '删除',
          divided: true,
          title: true,
          customClass: 'right-menu-title',
          onClick: () => this._delete(primary.id)
        }]
        this.editor.menu.onContextmenu(event)
        return
      }
    }
    super.onRightClick(event)
  }

  onMouseMove(event) {
    super.onMouseMove(event)

    let position = this._getCartesian3(event.endPosition)
    if (!position) return
    if (this.editor.actived === ActiveState.ADD) {
      this.entityPoints[this.entityPoints.length - 1] = position
    } else if (this.editor.actived === ActiveState.EDIT) {
      // 修改样式
      // 获取鼠标位置
      let pickedObject = this.editor.viewer.scene.pick(event.endPosition)
      if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive)) {
        if (
          pickedObject.primitive.type === PrimitiveType.Vertex ||
          pickedObject.primitive.type === PrimitiveType.CenterPoint
        )
          // 当鼠标悬浮在 Cesium Entity 上时，设置样式
          this.editor.viewer.container.style.cursor = 'move'
        else if (pickedObject.primitive.type === PrimitiveType.Midpoint)
          this.editor.viewer.container.style.cursor = 'pointer'
        //
        else this.editor.viewer.container.style.cursor = 'crosshair'
      } else {
        // 当鼠标不在 Cesium Entity 上时，恢复默认样式
        this.editor.viewer.container.style.cursor = ''
      }

      if (!this.isEditing || !this.editVertext) return
      if (this.editVertext.type === PrimitiveType.CenterPoint) {
        let startPosition =
          this.vertexCenterPositions[this.editVertext.vertexIndex]
        if (!startPosition) return
        this._moveEntityByOffset(startPosition, position)
        this.vertexCenterPositions[this.editVertext.vertexIndex] =
          this._getCenterPosition(
            this.editEntity.datasources[this.editVertext.vertexIndex].positions
          )
      } else {
        const index = [...this.editVertext.vertexIndex]
        if (this.editVertext.isHole) {
          this.editEntity.datasources[index.shift()].holes[index.shift()][
            index.shift()
          ] = position
        } else {
          this.editEntity.datasources[index.shift()].positions[index.shift()] =
            position
        }
      }
      this.isEditing = true
    }
  }

  _union(wkt) {
    return unionPolygon(this.editor.origin, wkt)
  }

  _formatCoordinate(points = []) {
    const cartes = this.ellipsoid.cartesianArrayToCartographicArray(points)
    const shapes = cartes.map(
      (point) =>
        `${Cesium.Math.toDegrees(point.longitude)} ${Cesium.Math.toDegrees(
          point.latitude
        )}`
    )
    shapes.push(shapes[0])
    return shapes
  }

  _formatWkt() {
    if (this.editor.actived === ActiveState.ADD) {
      if (!this.entityPoints.length || this.entityPoints.length < 2) {
        return ''
      }
      // 过滤相同的点
      const shapes = this._formatCoordinate(this.entityPoints)
      return `MULTIPOLYGON(((${shapes.join()})))`
    } else if (this.editor.actived === ActiveState.EDIT) {
      if (!this.editEntity.datasources.length || !this.isEditing) {
        return ''
      }
      const wkt = this.editEntity.datasources.map(
        (datasource) =>
          `MULTIPOLYGON ((${[
            this._formatCoordinate(datasource.positions),
            ...(datasource.holes?.map((hole) => this._formatCoordinate(hole)) ||
              []),
          ]
            .map((c) => `(${c.join()})`)
            .reduce((a, b) => `${a}, ${b}`)}))`
      ).reduce((a, b) => unionPolygon(a, b), '')

      this.editEntity.wkt = wkt
      return wkt
    }
    return ''
  }
}
