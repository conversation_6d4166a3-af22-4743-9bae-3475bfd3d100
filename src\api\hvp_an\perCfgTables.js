import request from '@/utils/request'

// 查询数据源配置列表
export function listPerCfgTables(query) {
  return request({
    url: '/hvpAn/perCfgTables/list',
    method: 'get',
    params: query
  })
}

export function getRuleTable(query) {
  return request({
    url: '/hvpAn/perCfgTables/getRuleTable',
    method: 'get',
    params: query
  })
}


// 查询数据源配置详细
export function getPerCfgTables(id) {
  return request({
    url: '/hvpAn/perCfgTables/' + id,
    method: 'get'
  })
}

// 保存数据源配置
export function savePerCfgTables(data) {
  return request({
    url: '/hvpAn/perCfgTables/save',
    method: 'post',
    data: data
  })
}

// 修改数据源配置
export function updatePerCfgTables(data) {
  return request({
    url: '/hvpAn/perCfgTables',
    method: 'put',
    data: data
  })
}

// 删除数据源配置
export function delPerCfgTables(id) {
  return request({
    url: '/hvpAn/perCfgTables/' + id,
    method: 'delete'
  })
}


// 删除数据源配置字段
export function delPerCfgFields(id) {
  return request({
    url: '/hvpAn/perCfgFeilds/' + id,
    method: 'delete'
  })
}