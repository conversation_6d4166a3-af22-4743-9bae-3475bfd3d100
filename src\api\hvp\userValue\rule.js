import request from '@/utils/request'

// 查询评估规则列表
export function pageRule(data) {
  return request({
    url: '/hvpVs/score/rule/page',
    method: 'post',
    data
  })
}
// 查询评估规则
export function listRule(data) {
  return request({
    url: '/hvpVs/score/rule/list',
    method: 'post',
    data
  })
}

// 查询评估规则详细
export function getRule(id) {
  return request({
    url: '/hvpVs/score/rule/' + id,
    method: 'get'
  })
}

// 新增评估规则
export function addRule(data) {
  return request({
    url: '/hvpVs/score/rule',
    method: 'post',
    data: data
  })
}
// 根据模板创建新规则
export function createRuleByModel(data) {
  return request({
    url: '/hvpVs/score/rule/createRuleByModel',
    method: 'post',
    data: data
  })
}


// 修改评估规则
export function updateRule(data) {
  return request({
    url: '/hvpVs/score/rule',
    method: 'put',
    data: data
  })
}

// 删除评估规则
export function delRule(id) {
  return request({
    url: '/hvpVs/score/rule/' + id,
    method: 'delete'
  })
}
