import request from '@/utils/request'

// 查询任务小区列表
export function listPerRuleRunTaskCgi(query) {
  return request({
    url: '/hvpAn/perRuleRunTaskCgi/list',
    method: 'get',
    params: query
  })
}

// 查询任务小区详细
export function getPerRuleRunTaskCgi(taskId) {
  return request({
    url: '/hvpAn/perRuleRunTaskCgi/' + taskId,
    method: 'get'
  })
}

// 新增任务小区
export function addPerRuleRunTaskCgi(data) {
  return request({
    url: '/hvpAn/perRuleRunTaskCgi',
    method: 'post',
    data: data
  })
}

// 修改任务小区
export function updatePerRuleRunTaskCgi(data) {
  return request({
    url: '/hvpAn/perRuleRunTaskCgi',
    method: 'put',
    data: data
  })
}

// 删除任务小区
export function delPerRuleRunTaskCgi(taskId) {
  return request({
    url: '/hvpAn/perRuleRunTaskCgi/' + taskId,
    method: 'delete'
  })
}