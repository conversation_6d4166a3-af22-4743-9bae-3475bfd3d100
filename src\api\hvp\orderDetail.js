import request from '@/utils/request'

export function getOrderDetail(id) {
    return request('/kernel/flow/instance/' + id)
}

export function createOrder(data,autoFlow,nextFlow) {
    return request({
        url: '/hvpVs/order/createOrder/'+autoFlow+'/'+nextFlow,
        method: 'post',
        data: data
    })
}

export function saveOrder(data,taskId, sequenceCode) {
    return request({
        url: '/hvpVs/order/saveOrder/'+taskId+'/'+ sequenceCode,
        method: 'post',
        data: data
    })
}

export function completeUserTask(id,taskId) {
    return request('/hvpVs/order/completeUserTask/'+id+'/'+ taskId)
}

export function deleteOrder(orderNo,procDefKey) {
    return request('/hvpVs/order/deleteOrder/'+orderNo+'/'+procDefKey)
}

export function saveOrderBorder(data) {
    return request({
      url: '/hvpVs/border/save',
      method: 'post',
      data: data
    })
}

export function updateOrderBorder(data) {
    return request({
      url: '/hvpVs/border/update',
      method: 'post',
      data: data
    })
}

export function getOrderBorderList(borderId) {
    return request({
      url: '/hvpVs/border/list/'+borderId,
      method: 'get',
    })
}

export function deleteOrderBorder(borderId) {
    return request({
      url: '/hvpVs/border/delete/'+borderId,
      method: 'post',
    })
}

/**
 * 删除小区
 *
 * @param orderId 任务标识
 * @param param 参数：{lat,lon,azimuth}
 */
export function dropOrderCell(orderId,param) {
  return request({
    url:'/hvpVs/order/cell/del/'+orderId,
    method: 'post',
    data: param
  })
}

/**
 * 添加小区
 *
 * @param orderId 任务标识
 * @param param 参数：{lat,lon,azimuth}
 */
export function addOrderCell(orderId,param) {
  return request({
    url:'/hvpVs/order/cell/add/'+orderId,
    method: 'post',
    data: param
  })
}

/**
 * 批量添加小区
 *
 * @param orderId 任务标识
 * @param param 参数：{lat,lon,azimuth}的数组
 */
export function batchAddOrderCell(orderId,param) {
  return request({
    url:'/hvpVs/order/cell/batchadd/'+orderId,
    method: 'post',
    data: param
  })
}

/**
 * 更新小区
 *
 * @param orderId 任务标识
 * @param param 需要更新的信息和条件
 */
export function updateOrderCell(orderId,param) {
  return request({
    url:'/hvpVs/order/cell/update/'+orderId,
    method: 'post',
    data: param
  })
}

/**
 * 读取小区
 *
 * @param orderId 任务标识
 * @param param 参数：{lat,lon,azimuth}
 */
export function readOrderCell(orderId,param) {
  return request({
    url:'/hvpVs/order/cell/read/'+orderId,
    method: 'post',
    data: param
  })
}


export function createJzxqOrder(orderId,data) {
  return request({
      url: '/hvpVs/order/createJzxqOrder/'+orderId,
      method: 'post',
      data: data
  })
}
