import request from '@/utils/request'

// 查询cosmic子过程描述列表
export function listTCosmicSubProcess(query) {
  return request({
    url: '/cosmic/subProcess/list',
    method: 'get',
    params: query
  })
}

// 查询cosmic子过程描述详细
export function getTCosmicSubProcess(id) {
  return request({
    url: '/cosmic/subProcess/' + id,
    method: 'get'
  })
}

// 新增cosmic子过程描述
export function addTCosmicSubProcess(data) {
  return request({
    url: '/cosmic/subProcess',
    method: 'post',
    data: data
  })
}

// 修改cosmic子过程描述
export function updateTCosmicSubProcess(data) {
  return request({
    url: '/cosmic/subProcess',
    method: 'put',
    data: data
  })
}

// 删除cosmic子过程描述
export function delTCosmicSubProcess(id) {
  return request({
    url: '/cosmic/subProcess/' + id,
    method: 'delete'
  })
}