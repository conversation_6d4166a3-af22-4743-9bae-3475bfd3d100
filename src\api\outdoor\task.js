import request from '@/utils/request'

// 查询覆盖预测任务列表
export function listTask(query) {
  return request({
    url: '/outdoor/task/list',
    method: 'get',
    params: query
  })
}

// 查询覆盖预测任务详细
export function getTask(id) {
  return request({
    url: '/outdoor/task/' + id,
    method: 'get'
  })
}

// 新增覆盖预测任务
export function addTask(data) {
  return request({
    url: '/outdoor/task',
    method: 'post',
    data: data
  })
}

// 修改覆盖预测任务
export function updateTask(data) {
  return request({
    url: '/outdoor/task',
    method: 'put',
    data: data
  })
}

// 删除覆盖预测任务
export function delTask(id) {
  return request({
    url: '/outdoor/task/' + id,
    method: 'delete'
  })
}

export function startTask(id) {
  return request({
    url: '/outdoor/task/service/startTask/' + id,
    method: 'get'
  })
}
