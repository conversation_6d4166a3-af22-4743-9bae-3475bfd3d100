import request from '@/utils/request'

// 查询问题清单列表
export function listPerProblemList(query) {
  return request({
    url: '/hvpAn/perProblemList/list',
    method: 'get',
    params: query
  })
}

// 查询问题清单详细
export function getPerProblemList(id) {
  return request({
    url: '/hvpAn/perProblemList/' + id,
    method: 'get'
  })
}

// 新增问题清单
export function addPerProblemList(data) {
  return request({
    url: '/hvpAn/perProblemList',
    method: 'post',
    data: data
  })
}

// 修改问题清单
export function updatePerProblemList(data) {
  return request({
    url: '/hvpAn/perProblemList',
    method: 'put',
    data: data
  })
}

// 删除问题清单
export function delPerProblemList(id) {
  return request({
    url: '/hvpAn/perProblemList/' + id,
    method: 'delete'
  })
}