import axios from '@/utils/request'

/**
 * 黑点接口
 */
class BlackPointApi {
  get base() {
    return '/blackspot/blackInfo'
  }
  // 添加黑点
  add(data) {
    return axios.post(`${this.base}/add`, data)
  }

  //黑点分析闭环
  closeTask(data) {
    return axios.post(`${this.base}/closeTask`, data)
  }

  //黑点列表
  list(data) {
    return axios.get(`${this.base}/list`, {
      params: data
    })
  }
  //关联500m内相关黑点
  relateBlackList(data) {
    return axios.get(`${this.base}/relateBlackList`, {
      params: data
    })
  }
  //关联500m内相关投诉
  relateComplainList(data) {
    return axios.get(`${this.base}/relateComplainList`, {
      params: data
    })
  }

  //黑点信息
  findById(data) {
    return axios.get(`${this.base}/findById?id=${data}`)
  }

  //根因分析信息
  findByIdAnalyze(data){
    return axios.get(`${this.base}/findByIdAnalyze?id=${data}`)
  }

  //总体问题信息
  findByIdTotalInfo(data){
    return axios.get(`${this.base}/findByIdTotalInfo?id=${data}`)
  }

  updateAnalyze(data){
    return axios.post(`${this.base}/updateAnalyze`, data)
  }

  updateTotalInfo(data){
    return axios.post(`${this.base}/updateTotalInfo`, data)
  }

  //跟新投诉关联接口updateCpRelation
  updateCpRelation(data) {
    return axios.post(`${this.base}/updateCpRelation`, data)
  }
  //更新小区关联接口updateAnalysisRelation
  updateAnalysisRelation(data) {
    return axios.post(`${this.base}/updateAnalysisRelation`, data)
  }
  //周围黑点
  outerEdgeBlackSpotInfo(data) {
    return axios.get(`${this.base}/outerEdgeBlackSpotInfo`, {
      params: data
    })
  }
  //黑点导出
  exportFile(data) {
    return axios.get(
      `${this.base}/exportBlackSpot`,
      { params: data },
      {
        responseType: 'blob'
      }
    )
  }
  //投诉csv文件导入
  autoGenerate(data) {
    return axios.get(`${this.base}/autoGenerate`, { params: data })
  }

  //流程文件导入
  autoProcess(data) {
    return axios.get(`${this.base}/autoProcess`, { params: data })
  }

  //站址缺失文件导入
  autoLostSitesInbase(data) {
    return axios.get(`${this.base}/autoLostSitesInbase`, { params: data })
  }

  //投诉取消关联并新建黑点
  removeRelation(data) {
    return axios.get(`${this.base}/removeRelation`, { params: data } )
  }

  //历史工单生成
  autoGenerateProvince(data) {
    return axios.get(`${this.base}/autoGenerateProvince`)
    // return axios.get(`${this.base}/autoGenerate1`)
  }
}

export default new BlackPointApi()
