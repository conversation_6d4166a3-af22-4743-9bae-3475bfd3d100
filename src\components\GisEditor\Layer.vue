<script>
import { reactive, onMounted } from '@/compositionApi'
import BasicDialog from '@/components/BasicComponents/Dialog/index.vue'
import legend from '@/assets/gis/legend.png'
import { findTableConfigList } from '@/api/provinces/dataManager/tableConfigApi'
import { uuid } from 'vue-contextmenujs/src/util'
import { commonQueryFieldALL } from '@/api/kernel/query'

const predefineColors = [
  '#00DAEA',
  '#00B050',
  '#92D050',
  '#5DFD00',
  '#FFFF00',
  '#FFC000',
  '#ED7D31',
  '#CAEACE',
  '#F71701',
  '#3E9B49',
  '#FFD966',
  '#FF66FF',
  '#FDCC00',
  '#00CBFF',
  '#0033FE',
  '#CA71ED',
]
// 运算符
const operators = {
  '=': '等于',
  '!=': '不等于',
  'is null': '为空',
  'is not null': '不为空',
  '>': '大于',
  '>=': '大于等于',
  '<': '小于',
  '<=': '小于等于',
  'in': '包含',
  'not in': '不包含',
  '~': '模糊匹配',
}
// 逻辑运算
const logics = ['&&', '||']

const example = {
  label: '分类1',
  color: 'rgba(217,217,217,0.6)',
  expressions: [
    {
      operator: '=',
      value: '',
    },
  ],
}

export default {
  name: 'Layer',
  components: { BasicDialog },
  props: {
    value: {
      type: Array
    }
  },
  setup(props, { root, refs, emit }) {
    const data = reactive({
      layerVisible: false,
      tableNames: [],
      legendTypes: [
        { label: '单一规则', value: 'single' },
        { label: '分类', value: 'classify' },
      ],
      operators,
      predefineColors,
      logics,
    })

    const tables = async () => {
      const { data: tabs } = await findTableConfigList({
        dataSubClassify: 'gc',
      })
      data.tableNames = tabs.map((o) => ({
        code: o.queryCode,
        value: o.tableName,
        name: o.dataName,
      }))
    }

    onMounted(() => {
      tables()
    })

    const layerForm = reactive({
      id: '',
      name: '',
      code: '',
      table: '',
      lon: '',
      lat: '',
      expressions: [
        {
          id: '1',
          column: '',
          operator: '',
          value: '',
        },
      ],
      legend: {
        type: 'single',
        color: 'rgba(217,217,217,0.6)',
        field: '',
        classify: [JSON.parse(JSON.stringify(example))],
      },
      fields: [],
    })

    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('图层名称不能为空'))
      }
      if (
        props.value.some(
          (layer) => layer.name === value && layer.id !== layerForm.id
        )
      ) {
        callback(new Error('图层名称已存在'))
      }
      callback()
    }

    const validateParam = (rule, value, callback) => {
      if (
        value &&
        value.length &&
        value.some(
          (layer) =>
            !layer.column ||
            !layer.operator ||
            (!layer.operator.includes('null') && !layer.value)
        )
      ) {
        callback(new Error('筛选条件不全, 请补全'))
      }
      callback()
    }

    const validateLegendField = (rule, value, callback) => {
      if (
        !value &&
        layerForm.legend.type === 'classify'
      ) {
        callback(new Error('请选择指标字段'))
      }
      callback()
    }

    const validateLegendClassify = (rule, value, callback) => {
      if (!value && !value.length) {
        callback(new Error('图例分类不能为空'))
      }
      else if (
        value.some(
          (classify) =>
            !classify.label ||
            !classify.color ||
            !classify.expressions ||
            !classify.expressions.length ||
            classify.expressions.some(express => !express.operator.includes('null') && !express.value)
        )
      ) {
        callback(new Error('分类条件不全, 请补全'))
      }
      callback()
    }

    const layerRules = reactive({
      name: [{ required: true, validator: validateName }],
      code: [{ required: true, message: '请选择来源表', trigger: 'change' }],
      lon: [{ required: true, message: '请选择经度字段', trigger: 'change' }],
      lat: [{ required: true, message: '请选择纬度字段', trigger: 'change' }],
      expressions: [{ required: false, validator: validateParam }],
      'legend.type': [{ required: true, message: '请选图例类型', trigger: 'change' }],
      'legend.color': [{ required: true, message: '请选图例颜色', trigger: 'change' }],
      'legend.field': [{ required: true, validator: validateLegendField, trigger: 'change' }],
      'legend.classify': [{ required: true, validator: validateLegendClassify }],
    })

    const hidden = layer => {
      emit('hidden', layer)
    }

    const update = (layer) => {
      layerForm.id = layer.id
      layerForm.name = layer.name
      layerForm.code = layer.code
      layerForm.table = layer.table
      layerForm.lon = layer.lon
      layerForm.lat = layer.lat
      layerForm.expressions = layer.expressions
      layerForm.legend = layer.legend
      layerForm.fields = layer.fields

      data.layerVisible = true
    }

    const remove = (layer) => {
      root.$confirm('是否确认删除图层【"' + layer.name + '"】?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        emit('remove', layer)
      })
    }

    const handleClassifyAdd = (i) => {
      let index = 1
      while (layerForm.legend.classify.some(classify => classify.label === `分类-${index}`)) {
        index++
      }
      const classify = JSON.parse(JSON.stringify(example))
      classify.label = `分类-${index}`
      layerForm.legend.classify.splice(
        i + 1,
        0,
        classify
      )
    }

    const handleClassifyRemove = (index) => {
      layerForm.legend.classify.splice(index, 1)
    }

    const handleExpressionAdd = (expressions, command) => {
      expressions.push({ logic: command })
      expressions.push({ operator: '=', value: '' })
    }

    const handleExpressionRemove = (expressions, index) => {
      expressions.splice(index, 2)
    }

    const handleLayerAdd = () => {
      data.layerVisible = true

      let index = 1
      while (props.value.some(layer => layer.name === `图层-${index}`)) {
        index++
      }
      layerForm.id = ''
      layerForm.name = `图层-${index}`
      layerForm.code = ''
      layerForm.expressions = []
      layerForm.legend = {
        type: 'single',
        color: 'rgba(217,217,217,0.6)',
        classify: [JSON.parse(JSON.stringify(example))],
      }
    }

    const formatKey = ({ column, value }) => {
      if (value !== '' && !isNaN(+value)) {
        return `${column}::numeric`
      }
      return column
    }

    const formatValue = (value) => {
      if (value !== '' && !isNaN(+value)) {
        return value
      } else {
        return `'${value}'`
      }
    }

    const formatExpression = () => {
      return layerForm.expressions.reduce((res, item) => {
        let value = `${item.operator} ${formatValue(item.value)}`
        if (item.operator.includes('null')) value = item.operator
        else if (item.operator.includes('in'))
          value = `${item.operator} (${item.value
            .map((v) => formatValue(v))
            .join(',')})`
        res[formatKey(item)] = value
        return res
      }, {})
    }

    const handleLayerLoad = () => {
      refs.layerFormRef.validate((valid) => {
        if (valid) {
          let id = uuid()
          let key = 0
          if (layerForm.id) {
            id = layerForm.id
            const remain = props.value.find((l) => l.id === id)
            key = remain?.key || 0
          }
          const layer = {
            ...layerForm,
            id,
            checked: true,
            disabled: true,
            key: ++key,
            url: '/smartplan/layer/dispatch/layer',
            param: {
              table: layerForm.table,
              lon: layerForm.lon,
              lat: layerForm.lat,
              expressions: formatExpression(),
            },
            legend: layerForm.legend,
          }
          emit('add', layer)
          data.layerVisible = false
        }
      })
    }

    const handleLayerCancel = () => {
      data.layerVisible = false
    }

    const handleTableChange = async (code) => {
      const curr = data.tableNames.find((o) => o.code === code)
      layerForm.table = curr.value
      layerForm.lon = ''
      layerForm.lat = ''
      layerForm.fields = []
      layerForm.expressions = []
      await handleQueryFields()
    }

    const handleQueryFields = async () => {
      const { data: query } = await commonQueryFieldALL(layerForm.code)
      layerForm.fields = query.fields
        .filter((field) => field.show)
        .map((t) => {
          if (t.title === '经度') layerForm.lon = t.name
          if (t.title === '纬度') layerForm.lat = t.name
          return { column: t.name, title: t.title, type: t.javaType }
        })
    }

    const handleParamAdd = () => {
      layerForm.expressions.push({ id: uuid() })
    }

    const handleParamRemove = (row) => {
      layerForm.expressions = layerForm.expressions.filter(
        (o) => o.id !== row.id
      )
    }

    return {
      data,
      legend,
      layerForm,
      layerRules,
      predefineColors,
      operators,
      logics,
      hidden,
      update,
      remove,
      handleLayerAdd,
      handleLayerLoad,
      handleLayerCancel,
      handleParamAdd,
      handleParamRemove,
      handleTableChange,
      handleClassifyAdd,
      handleClassifyRemove,
      handleExpressionAdd,
      handleExpressionRemove,
    }
  },
}
</script>

<template>
  <div class="layer-container">
    <div class="layers" v-if="!!value.length">
      <div class="buttons">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleLayerAdd"
          >新增图层
        </el-button>
      </div>
      <div class="layer" v-for="item in value">
        <div class="layer-title">
          <el-checkbox
            v-model="item.checked"
            @change="hidden(item)"
          ></el-checkbox>
          <el-tooltip class="tooltip-item" effect="dark" placement="top">
            <div class="layer_tab_content" slot="content">
              <template v-if="item.legend.type === 'single'">
                <div :style="`background: ${item.legend.color}; height:12px; width: 12px`"></div>
              </template>
              <template v-else-if="item.legend.type === 'classify'">
                <div class="layer_classify" v-for="it in item.legend.classify">
                  <div :style="`background: ${it.color}; height:12px; width: 12px`"></div>
                  <div class="title">{{ it.label }}</div>
                </div>
              </template>
            </div>
            <img :src="legend">
          </el-tooltip>
          {{ item.name }}
        </div>
        <div class="operation" v-if="item.disabled">
          <i type="primary" @click="update(item)" class="el-icon-edit"></i>
          <i type="primary" @click="remove(item)" class="el-icon-close"></i>
        </div>
      </div>
    </div>
    <basic-dialog
      :show.sync="data.layerVisible"
      ref="layerRef"
      title="新增图层"
      :width="850"
      :height="550"
      :left="500"
      :top="186"
      resize
      @closeDialog="handleLayerCancel"
      custom-class="menu-dialog"
    >
      <el-form
        ref="layerFormRef"
        :model="layerForm"
        :rules="layerRules"
        inline
        label-width="100px"
      >
        <el-row :gutter="5">
          <el-divider content-position="left">图层来源</el-divider>
          <el-col :span="12">
            <el-form-item label="图层名称" prop="name">
              <el-input v-model="layerForm.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源表" prop="code">
              <el-select
                v-model="layerForm.code"
                filterable
                @change="handleTableChange"
              >
                <el-option
                  v-for="layer in data.tableNames"
                  :label="layer.name"
                  :value="layer.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5">
          <el-col :span="12">
            <el-form-item label="经度" prop="lon">
              <el-select v-model="layerForm.lon" filterable>
                <el-option
                  v-for="layer in layerForm.fields"
                  :label="layer.title"
                  :value="layer.column"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="lat">
              <el-select v-model="layerForm.lat" filterable>
                <el-option
                  v-for="layer in layerForm.fields"
                  :label="layer.title"
                  :value="layer.column"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">图层筛选</el-divider>
        <el-form-item label="" style="margin-left: 40px" prop="expressions">
          <el-button type="text" @click="handleParamAdd">添加行</el-button>
          <el-table size="mini" :data="layerForm.expressions" border>
            <el-table-column label="字 段" prop="column" width="180">
              <template slot-scope="scope">
                <el-select v-model="scope.row.column" filterable>
                  <el-option
                    v-for="layer in layerForm.fields"
                    :label="layer.title"
                    :value="layer.column"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="筛选类型" prop="operator" width="150">
              <template slot-scope="scope">
                <el-select
                  style="width: 100%"
                  v-model="scope.row.operator"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in Object.keys(operators)"
                    :key="item"
                    :label="operators[item]"
                    :value="item"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="筛选值" prop="value" min-width="250">
              <template slot-scope="scope">
                <el-select
                  style="width: 100%"
                  v-if="
                    scope.row.operator === 'in' ||
                    scope.row.operator === 'not in'
                  "
                  filterable
                  allow-create
                  multiple
                  v-model="scope.row.value"
                  placeholder="请选择"
                >
                  <el-option label="请输入" :value="''" disabled />
                </el-select>
                <el-input-number
                  v-else-if="
                    scope.row.operator &&
                    (scope.row.operator.includes('>') ||
                      scope.row.operator.includes('<'))
                  "
                  v-model="scope.row.value"
                  placeholder="请输入筛选值"
                />
                <el-input
                  v-else-if="
                    scope.row.operator && !scope.row.operator.includes('null')
                  "
                  v-model="scope.row.value"
                  placeholder="请输入筛选值"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="handleParamRemove(scope.row)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-divider content-position="left">图层图例</el-divider>
        <el-row :gutter="5">
          <el-col :span="12">
            <el-form-item label="图例类型" prop="legend.type">
              <el-select v-model="layerForm.legend.type" filterable>
                <el-option
                  v-for="layer in data.legendTypes"
                  :label="layer.label"
                  :value="layer.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="layerForm.legend.type === 'single'"
              label="图例颜色"
              prop="legend.color"
            >
              <el-color-picker
                v-model="layerForm.legend.color"
                show-alpha
                :predefine="predefineColors"
              />
            </el-form-item>
            <el-form-item
              v-else-if="layerForm.legend.type === 'classify'"
              label="指标字段"
              prop="legend.field"
            >
              <el-select v-model="layerForm.legend.field" filterable>
                <el-option
                  v-for="layer in layerForm.fields"
                  :label="layer.title"
                  :value="layer.column"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          v-if="layerForm.legend.type === 'classify'"
          label="图例分类"
          prop="legend.classify"
        >
          <div
            :key="index"
            class="pick-item"
            v-for="(item, index) in layerForm.legend.classify"
          >
            <el-input
                class="input_inner"
                size="mini"
                v-model="item.label"
                placeholder="分类名称"
            />
            <el-color-picker
              v-model="item.color"
              show-alpha
              :predefine="predefineColors"
            />
            <template v-for="(expression, idx) in item.expressions">
              <el-dropdown
                trigger="click"
                v-if="expression.logic"
                @command="handleExpressionRemove(item.expressions, idx)"
              >
                <el-tag> {{ expression.logic }}</el-tag>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-dropdown
                trigger="click"
                v-if="expression.operator"
                @command="(c) => (expression.operator = c)"
              >
                <el-tag type="info">
                  {{ operators[expression.operator] }}
                </el-tag>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="operator"
                    v-for="operator in Object.keys(operators)"
                    >{{ operators[operator] }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-select
                class="input_inner"
                v-if="
                    expression.operator === 'in' ||
                    expression.operator === 'not in'
                  "
                filterable
                allow-create
                multiple
                size="mini"
                v-model="expression.value"
                placeholder="请选择"
              >
                <el-option label="请输入" :value="''" disabled />
              </el-select>
              <el-input-number
                class="input_inner"
                v-else-if="
                    expression.operator &&
                    (expression.operator.includes('>') ||
                      expression.operator.includes('<'))
                  "
                v-model="expression.value"
                size="mini"
                placeholder="请输入筛选值"
              />
              <el-input
                class="input_inner"
                size="mini"
                v-else-if="
                    expression.operator && !expression.operator.includes('null')
                  "
                v-model="expression.value"
                placeholder="请输入"
              />
            </template>
            <el-dropdown
              trigger="click"
              @command="(c) => handleExpressionAdd(item.expressions, c)"
            >
              <el-tag type="warning"> +</el-tag>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="logic" v-for="logic in logics"
                  >{{ logic }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <i
              @click="handleClassifyAdd(index)"
              class="el-icon-circle-plus"
              style="font-size: 24px"
            />
            <i
              @click="handleClassifyRemove(index)"
              class="el-icon-error"
              style="font-size: 24px"
              v-if="layerForm.legend.classify.length > 1"
            />
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer" slot="footer">
        <el-button @click="handleLayerLoad" size="small" type="primary"
          >确 定
        </el-button>
        <el-button size="small" @click="handleLayerCancel">取 消</el-button>
      </div>
    </basic-dialog>
  </div>
</template>

<style scoped lang="scss">
.layer-container {
  .buttons {
    margin-bottom: 5px;
  }
  .layers {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.6);
    z-index: 99;
    color: #eee;
    padding: 4px 8px;
    border-radius: 5px;
    max-height: 150px;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .layer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 5px;
      font-size: 12px;

      .layer-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 5px;

        img {
          width: 14px;
          height: 14px;
        }
      }

      .operation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 5px;
        cursor: pointer;
      }
    }
  }
}
</style>
<style lang="scss">
.pick-item {
  margin: 8px 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  div {
    margin: 0 2px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .input_inner {
    min-width: 40px;
    ::v-deep.el-input__inner {
      padding: 0 5px !important;
    }
  }
}

.layer_tab_content {
  .layer_classify {
    display: flex;
    gap: 6px;
    padding: 3px;

    .title {
      color: #cccccc;
    }
  }
}
</style>
