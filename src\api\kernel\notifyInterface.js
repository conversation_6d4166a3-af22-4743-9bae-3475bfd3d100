import request from '@/utils/request'

export function listInterface(query) {
    return request({
      url: '/kernel/flow/notify/interface/list',
      method: 'get',
      params: query
    })
  }

// 查询交互接口详细
export function getInterface(id) {
  return request({
    url: '/kernel/flow/notify/interface/' + id,
    method: 'get'
  })
}  

export function saveInterface(data) {
  return request({
    url: '/kernel/flow/notify/interface',
    method: 'post',
    data: data
  })
}
  
// 修改通用查询
export function updateInterface(data) {
  return request({
    url: '/kernel/flow/notify/interface',
    method: 'put',
    data: data
  })
}

//删除接口
export function deleteInterface(id) {
  return request({
    url: '/kernel/flow/notify/interface/' + id,
    method: 'delete',
  })
}
  