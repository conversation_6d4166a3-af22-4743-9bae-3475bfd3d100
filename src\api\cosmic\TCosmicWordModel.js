import request from '@/utils/request'

// 查询09 word导出模板列表
export function listTCosmicWordModel(query) {
  return request({
    url: '/cosmic/wordModel/list',
    method: 'get',
    params: query
  })
}

// 查询09 word导出模板详细
export function getTCosmicWordModel(id) {
  return request({
    url: '/cosmic/wordModel/' + id,
    method: 'get'
  })
}

// 新增09 word导出模板
export function addTCosmicWordModel(data) {
  return request({
    url: '/cosmic/wordModel',
    method: 'post',
    data: data
  })
}

// 修改09 word导出模板
export function updateTCosmicWordModel(data) {
  return request({
    url: '/cosmic/wordModel',
    method: 'put',
    data: data
  })
}

// 删除09 word导出模板
export function delTCosmicWordModel(id) {
  return request({
    url: '/cosmic/wordModel/' + id,
    method: 'delete'
  })
}