import request from '@/utils/request'

// 查询小区工参列表
export function listPerCfgCell(query) {
  return request({
    url: '/hvpAn/perCfgCell/list',
    method: 'get',
    params: query
  })
}

// 查询小区工参详细
export function getPerCfgCell(cgi) {
  return request({
    url: '/hvpAn/perCfgCell/' + cgi,
    method: 'get'
  })
}

// 新增小区工参
export function addPerCfgCell(data) {
  return request({
    url: '/hvpAn/perCfgCell',
    method: 'post',
    data: data
  })
}

// 修改小区工参
export function updatePerCfgCell(data) {
  return request({
    url: '/hvpAn/perCfgCell',
    method: 'put',
    data: data
  })
}

// 删除小区工参
export function delPerCfgCell(cgi) {
  return request({
    url: '/hvpAn/perCfgCell/' + cgi,
    method: 'delete'
  })
}