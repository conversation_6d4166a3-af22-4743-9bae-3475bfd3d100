import request from '@/utils/request'

export function list(data) {
  return request({
    url: 'cov/gisLayer/list',
    method: 'post',
    data
  })
}

export function save(data) {
  return request({
    data,
    url: 'cov/gisLayer/save',
    method: 'post'
  })
}

export function deleteGraph(data) {
  return request({
    url: 'cov/gisLayer/delete',
    method: 'delete',
    params: data
  })
}

export function update(data) {
  return request({
    data,
    url: 'cov/gisLayer/update',
    method: 'put'
  })
}

export function detail(id) {
  return request(`cov/gisLayer/detail/${id}`)
}

export function tree() {
  return request('cov/gisLayer/getLayerLevel')
}

export function updateTreeLevel(data) {
  return request({
    data,
    url: 'cov/gisLayer/updateTreeLevel',
    method: 'put'
  })
}
