<template>
  <!-- 逻辑基站信息 -->
  <div class='logic-base-station'>
    <template v-if='!period'>
      <FormArea
        :badge='true'
        :showChangeFormToolTip='true'
        :formColumn='4'
        :formEditable='isCurrentStage || inResourcePool'
        :formFields='fields["enodeb"]'
        :showAddFormButton='false'
        :showSwiper='false'
        :showUploader='false'
        :tabEditable='childStage === "GHK" && isCurrentStage'
        :tabList='allEnodebList'
        :enableResourceConfig='enableResourceConfig'
        :libraryType='libraryType'
        resourceType='enodeb'
        :canAddResource='hasResourcePermission(libraryType, "enodeb", "add")'
        :canDeleteResource='hasResourcePermission(libraryType, "enodeb", "delete")'
        @addTab='handleAddTab'
        @handleFormCascading='handleFormEvent'
        @removeTab='handleRemoveTab'
        @returnFormValue='handleFormValue'
        @switchTab='handleSwitchTab'
        :groupMark='groupMark'
        multiple
        ref='enodeb'
        tabTitle='基站'
      />

      <el-collapse-item class='second-collapse' name='baseBand' :title="`基站${parseInt(enodebIndex) + 1}-基带设备`" v-if="fields['equipment_baseBand']">
        <FormArea
          :badge='true'
          :showChangeFormToolTip='true'
          :fileStructurePath='getFileStructurePath("enodeb_baseBand")'
          :formColumn='3'
          :formEditable='isCurrentStage || inResourcePool'
          :formFields='fields["equipment_baseBand"]'
          :namePropIndex='false'
          :showAddFormButton='false'
          :tabEditable='false'
          :tabList='getBaseBandList'
          @handleFormCascading='handleFormEvent'
          @removeTab='handleRemoveTab'
          @returnFormValue='handleFormValue'
          @setImage='setImageIdentify'
          @switchTab='handleSwitchTab'
          @selectChanged="(data) => dealSelectChangeEvent(data,'equip')"
          groupMark='baseBand'
          multiple
          nameProp='subType'
          ref='baseBand'
        />
        <!--江西特有-->
        <template v-if='isCurrentProvince("JX") && childStage === "SGYY"'>
          <el-collapse-item name='electronic' title='电调申请'>
            <BasicForm :disabled='formDisable' :fields='fields["t_jx_electric"]' :formValue='getCurrentEnodebElectronic' :groupColumn='3' :useFieldDefaultValue='false' @returnFormValue='setElectronicFormData' class='electronicForm' labelWidth='120px' size='small' />
          </el-collapse-item>
          <el-collapse-item name='light' title='光路调度'>
            <el-button @click='submitLight' type='primary'>光调发起</el-button>
          </el-collapse-item>
        </template>

        <!-- 板卡 -->
        <Board
          proc-def-key="cmcc_5g_plan"
          :type="isShowBadgeStage ? 'plan' : null" :boardColumn='3' :fields='fields'
          :boardList='getBaseBandList && getBaseBandList[baseBandIndex] && getBaseBandList[baseBandIndex].boardList'
          :fileBaseStructurePath='getFileStructurePath("enodeb_baseBand")'
          :formEditable='isCurrentStage'
          :parentResourceCode='getResourceCodePath("enodeb_baseBand", true, "", "")'
          :tabEditable='editTabBoardStage.includes(childStage) && isCurrentStage &&!isOpenSite'
          @addDefaultTab='addDefaultTab("boardList")'
          @selectChanged="(data) => dealSelectChangeEvent(data,'board')"
          fieldSubType='board_baseBand'
          v-if='fields.board_baseBand'
          ref="board"
          :firstTabRemovable='getBaseBandList && getBaseBandList[baseBandIndex] && getBaseBandList[baseBandIndex].subType === "CU"'
        />
        <!--         扩展设备-->
        <ExtendDevice
          :badge='isShowBadgeStage'
          :showChangeFormToolTip='isShowChangeFormToolTipStage'
          :editable='["GHK", "SJK", "SGGK"].includes(childStage) && isCurrentStage && !isOpenSite'
          :fields='fields["extend_device_baseband"]'
          :formDisabled='formDisable'
          ref="extendDeviceBbu"
          @selectChanged="(data) => dealSelectChangeEvent(data,'bbuExtendDevice')"
          @handleFormCascading='onFormUpdate'
          :tab-list='getBaseBandList && getBaseBandList[baseBandIndex] && getBaseBandList[baseBandIndex].bbuExtendDeviceList'
          mark="bbu"
          v-if='fields["extend_device_baseband"]'
          groupMark="extendDevice_baseBand"
        />



      </el-collapse-item>

      <!-- 传输配置 -->
      <Transfer
        :enodeb='getCurrentEnodeb'
        :fields='fields'
        :isCurrentStage='isCurrentStage'
        :tabEditable='editTabTransferStage.includes(childStage) && isCurrentStage && !isOpenSite'
      />

      <!-- 小区 -->
      <el-collapse-item name='cell' :title="`基站${parseInt(enodebIndex) + 1}-小区`" v-if="fields['cell']">

        <FormArea
          :badge='true'
          :showChangeFormToolTip='true'
          :formColumn='4'
          :formEditable='isCurrentStage || inResourcePool'
          :formFields='fields["cell"]'
          :showAddFormButton='false'
          :showSwiper='false'
          :showUploader='false'
          :tabEditable='siteTabEditable'
          :tabList='getCellList'
          :enableResourceConfig='enableResourceConfig'
          :libraryType='libraryType'
          :resourceType='"cell"'
          :canAddResource='hasResourcePermission(libraryType, "cell", "add")'
          :canDeleteResource='hasResourcePermission(libraryType, "cell", "delete")'
          @addTab='handleAddTab'
          @handleFormCascading='handleFormEvent'
          @removeTab='handleRemoveTab'
          @returnFormValue='handleFormValue'
          @switchTab='handleSwitchTab'
          groupMark='cell'
          multiple
          ref='cell'
          tabTitle='小区'
        >
          <template v-if='siteTabEditable && isSFSite' v-slot:form>
            <el-button
              @click='downloadRruAndAnt(getCellList[cellIndex])'
              icon='el-icon-download'
              size='mini'
              type='primary'
            >下载模板
            </el-button>

            <el-tooltip content='注意:导入后会将原始数据覆盖!' placement='bottom-start'>
              <FileUpload :extendParam='{  node: $route.query.nodeId,  cellUUID: getCellList[cellIndex].id }'
                          :fileType='["zip"]'
                          :isShowTip='false'
                          @input='uploadRruAndAnt'
                          class='rru-ant-upload'
                          uploadUrl='/site/plan/import/detail'
              />
            </el-tooltip>

            <el-button @click='downloadErrorFile' size='mini' type='text' v-if='errorFileBlob'>错误文件下载</el-button>
          </template>
        </FormArea>

        <!-- 锚点小区 -->
        <el-collapse-item name='anchor' title='锚点小区' v-if='showAnchorAndNcellStage.includes(childStage)'>
          <Anchor
            :data='getCellList && getCellList[cellIndex]'
            :siteCmccId='siteCmccId'
            :stage='childStage'
            mark='anchor'
          />
        </el-collapse-item>

        <!-- 邻区规划 -->
        <el-collapse-item
          name='ncell'
          title='邻区规划'
          v-if='showAnchorAndNcellStage.includes(childStage)'
        >
          <Anchor
            :data='getCellList && getCellList[cellIndex]'
            :siteCmccId='siteCmccId'
            :stage='childStage'
            mark='ncell'
          />
        </el-collapse-item>

        <!-- RRU -->
        <el-collapse-item name='rru' :title="`基站${parseInt(enodebIndex) + 1}-小区${parseInt(cellIndex) + 1}-射频单元`" v-if="fields['rru']">
          <FormArea
            :enableResourceConfig='enableResourceConfig'
            :libraryType='libraryType'
            resourceType='rru'
            :canAddResource='hasResourcePermission(libraryType, "rru", "add")'
            :canDeleteResource='hasResourcePermission(libraryType, "rru", "delete")'
            :enableInherit="isCurrentProvince('XJ')"
            :badge='true'
            :showChangeFormToolTip='true'
            :fileStructurePath='getFileStructurePath("enodeb_cell_rru")'
            :formColumn='3'
            :formEditable='isCurrentStage || inResourcePool'
            :formFields='fields["rru"]'
            :namePropData='useNamePropData("rru", "rruType")'
            :showAddFormButton='false'
            :tabEditable='siteTabEditable'
            :tabList='getRruList'
            @addTab='handleAddTab'
            @handleFormCascading='handleFormEvent'
            @removeTab='handleRemoveTab'
            @returnFormValue='handleFormValue'
            @setImage='rruIntegrationAnt'
            @selectChanged="(data) => dealSelectChangeEvent(data,'rru')"
            @switchTab='handleSwitchTab'
            groupMark='rru'
            multiple
            nameProp='rruType'
            ref='rru'
            tabTitle='射频单元'
          />

          <Board
            :boardColumn='3'
            :boardList='getCurrentRru && getCurrentRru.boardList'
            :fields='fields'
            :fileBaseStructurePath='getFileStructurePath("enodeb_cell_rru")'
            :formEditable='isCurrentStage || inResourcePool'
            :parentResourceCode='getResourceCodePath("enodeb_cell_rru", true, "", "")'
            :tabEditable='editTabBoardStage.includes(childStage) && isCurrentStage &&!isOpenSite'
            @selectChanged="(data) => dealSelectChangeEvent(data,'board')"
            fieldSubType='board_rru'
            :tab-title-name-prefix="`基站${parseInt(enodebIndex) + 1}-小区${parseInt(cellIndex) + 1}-${getCurrentRru && getCurrentRru.rruType}-`"
            v-if='fields.board_rru'
          />

          <ExtendDevice
            :badge='isShowBadgeStage'
            :showChangeFormToolTip='isShowChangeFormToolTipStage'
            :editable='["GHK", "SJK", "SGGK"].includes(childStage) && isCurrentStage && !isOpenSite'
            :fields='fields["extend_device_rru"]'
            :formDisabled='formDisable'
            ref="extendDeviceRru"
            @selectChanged="(data) => dealSelectChangeEvent(data,'rruExtendDevice')"
            mark="rru"
            :tab-list='getRruExtendDevice'
            @handleFormCascading='onFormUpdate'
            v-if='fields["extend_device_rru"]'
            groupMark="extendDevice_rru"
            :tab-title-name-prefix="`基站${parseInt(enodebIndex) + 1}-小区${parseInt(cellIndex) + 1}-${getCurrentRru && getCurrentRru.rruType}-`"

            @switchTab='handleSwitchTab'
          />
          <!-- 天线 -->
          <el-collapse-item name='antenna' :title="`基站${parseInt(enodebIndex) + 1}-小区${parseInt(cellIndex) + 1}-${getCurrentRru && getCurrentRru.rruType}-天线`" v-if="fields['antenna']">
            <FormArea
              :enableResourceConfig='enableResourceConfig'
              :libraryType='libraryType'
              resourceType='antenna'
              :canAddResource='hasResourcePermission(libraryType, "antenna", "add")'
              :canDeleteResource='hasResourcePermission(libraryType, "antenna", "delete")'
              :enableInherit="isCurrentProvince('XJ')"
              :badge='true'
              :fileStructurePath='getFileStructurePath("enodeb_cell_rru_antenna")'
              :formColumn='3'
              :formEditable='isCurrentStage || inResourcePool'
              :formFields='fields["antenna"]'
              :showAddFormButton='true'
              @addDefaultTab='addDefaultTab("antennaList")'
              :showChangeFormToolTip='true'
              :tabEditable='siteTabEditable'
              :tabList='getAntennaList'
              @addTab='handleAddTab'
              @handleFormCascading='handleFormEvent'
              @removeTab='handleRemoveTab'
              @returnFormValue='handleFormValue'
              @setImage='setImageIdentify'
              @switchTab='handleSwitchTab'
              @selectChanged="(data) => dealSelectChangeEvent(data,'ant')"
              groupMark='antenna'
              multiple
              ref='antenna'
              tabTitle='天线'
            />
          </el-collapse-item>
        </el-collapse-item>
      </el-collapse-item>

      <!-- 室分审核 -->
      <el-collapse-item
        name='autoCheck'
        title='室分审核'
        v-if='childStage === "SJSH" &&
        sysconfig.ranplan_status== "ENABLE" &&
        getCurrentEnodeb &&
        getCurrentEnodeb.indoorFlag === "RoomSite"'
      >
        <Ranplan :enodebInfo='getCurrentEnodeb' :siteIdCmcc='siteCmccId' />
      </el-collapse-item>
    </template>

    <template v-else>
      <FormArea
        :formColumn='4'
        :formEditable='isCurrentStage'
        :formFields='fields["lxpf_contrunction"]'
        :showAddFormButton='false'
        :showSwiper='false'
        :showUploader='false'
        :tabEditable='siteTabEditable'
        :tabList='getEnodebList'
        @handleFormCascading='handleFormEvent'
        @returnFormValue='setPeroidData'
        @switchTab='handleSwitchTab'
        groupMark='enodeb'
        :ref='enodeb'
        tabTitle='基站'
      />
      <BasicTable
        :tableData='getPeriodTableData'
        :tableTitle='curEnodebPeriodTitle'
        @handleSelectionChange='handleSelectionChange'
        checkbox
        radio
        ref='constructionPeriodTable'
        reserveSelection
        rowKeyId='hangup_enodeb_uuid'
        v-show='enodebSamePeriod'
      ></BasicTable>
    </template>

    <!-- 共用本工单资源继承弹窗 -->
    <el-dialog
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      :visible.sync='resourceCodeSelectDialogShow'
      title='资源ID回填'
      width='50%'
    >
      <!--      一个tab页面标签分别是 本工单其他资源Id，系统其他资源ID，三方资源ID-->
      <el-tabs v-model="resourceCodeTabActive" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="本工单其他可共用资源ID" name="first">
          <el-select
            v-model="resourceCodeForm.resourceCodeShare"
            clearable
            placeholder="请选择共用资源ID,若无数据,请先暂存工单!"
            style="width: 100%"
          >
            <el-option
              v-for="item in resourceCodeList.list"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-tab-pane>
        <!--        <el-tab-pane label="系统其他资源ID" name="second">-->
        <!--          <el-empty description="暂无数据" />-->
        <!--        </el-tab-pane>-->
        <!--        <el-tab-pane label="三方资源ID" name="third">-->
        <!--          <el-empty description="暂无数据" />-->
        <!--        </el-tab-pane>-->
      </el-tabs>
      <span slot='footer'>
				<el-button @click='resourceCodeSelectDialogShow = false'>取 消</el-button>
				<el-button @click="backFill(currentBackFillType)" type='primary'>回 填</el-button>
			</span>
    </el-dialog>

    <!-- RRU继承弹窗 -->
    <inherit-dialog
      :visible.sync="dialogs.rru.inheritDialogVisible"
      title="射频单元数据继承"
      :sourceList="getRruInheritList"
      @confirm="handleRruInherit"
    />

    <!-- 天线继承弹窗 -->
    <inherit-dialog
      :visible.sync="dialogs.antenna.inheritDialogVisible"
      title="天线数据继承"
      :sourceList="getAntennaInheritList"
      @confirm="handleAntennaInherit"
    />
  </div>
</template>

<script>
import {
  ref,
  toRefs,
  reactive,
  inject,
  provide,
  watch,
  nextTick,
  computed,
  onMounted,
  defineComponent
} from '@/compositionApi'
import dayjs from 'dayjs'
import { deepClone } from '@/utils'
import { arrayGroupBy, isCurrentProvince } from '@/utils/common'
import { useGetters } from '@/utils/useMappers'
import { baseBandList, new_copy_delete_field } from '@/utils/plan/static'
import { commonQuery, commonQueryFieldData } from '@/api/kernel/query'
import { qryOpenSite, qryJxElectronic } from '@/api/site/plan/handle'
import attachmentApi from '@/api/system/attachmentApi'
import {downCellAntRuu } from '@/api/kernel/cell'
import { setDefaultListData } from '@/components/Open/Static'
import {
  fieldsMapSyncData,
  createSerializeChildren,
  controlFormFieldsChildren, generateArray
} from '@/utils/plan/handle'

import {
  rruSyncAnt,
  rruSyncBoardHN,
  firstRoomToBaseBandReverseMap as baseBandSyncField,
  firstRoofToFirstRru as rruSyncField,
  firstRoofToFirstAnt as antSyncField
} from '@/utils/plan/fieldsMap'

import {
  useFormPlanId,
  useOcrDataFillBack,
  useCompletionListData,
  useBlobToExcelDownload,
  useStringPrev,
  useStringLast,
  useRecursionCallBack,
  useCreateDoubleSerialNumber
} from '@/utils/plan/useBusiness'

import {
  autoCreateBaseBandName,
  autoCreateRruName,
  autoCreateCGI,
  autoCreateAntName,
  autoCreateAntPitchAngle
} from '@/utils/plan/hnAutoCreateData'
import Vue from 'vue'
import { extractTabIndexesFromText, fieldGroupTitleMap } from '@/utils/plan/validate'
import InheritDialog from '@/components/BasicComponents/InheritDialog/index.vue'
import SchemaResourceConfigMixin from '@/mixins/SchemaResourceConfigMixin'
import NodeStageEnum from '@/utils/nodeStageEnum'

export default defineComponent({
  name: 'logicBaseStation',
  mixins: [SchemaResourceConfigMixin],
  methods: { isCurrentProvince },
  props: {
    period: Boolean,
    fields: Object,
    planSite: Object,
    schemeIndex: Number,
    inResourcePool: Boolean,
    clickItem: Object,
    groupMark:{type:String,default:'enodeb'},//groupMark只有在“方案-逻辑基站信息”不传值，其它地方皆应应该传值
  },
  setup(props, { root, emit,refs }) {

    const { $store,$route,$set, isCurrentProvince } = root
    const { nodeId } = $route.query
    const {userId} = $store.state.user.user
    const stage = inject('stage')
    const isSFSite = inject('isSFSite')
    const childStage = inject('childStage')
    const siteCmccId = inject('siteCmccId')
    const isCurrentStage = inject('isCurrentStage')
    const reRenderPage = inject('reRenderPage')
    const xqSite = inject('xqSite')
    const { fields, period, planSite, schemeIndex, inResourcePool } = toRefs(props)
    // console.log('逻辑站数据planSite：',planSite);
    // ref
    const domRefs = reactive({
      enodeb: ref(null),
      baseBand: ref(null),
      cell: ref(null),
      rru: ref(null),
      iu: ref(null),
      antenna: ref(null),
      antenna_cell: ref(null),
      constructionPeriodTable: ref(null),
      extendDeviceBbu: ref(null),
      extendDeviceRru: ref(null),
      board: ref(null),
      dialogs: {
        rru: {
          inheritDialogVisible: false
        },
        antenna: {
          inheritDialogVisible: false
        }
      }
    })
    const state = reactive({
      equipmentActive: '',
      enodebIndex: '0',
      baseBandIndex: '0',
      cellIndex: '0',
      rruIndex: '0',
      iuIndex: '0',
      antennaIndex: '0',
      extendDevice_rruIndex:'0',//此处extendDevice_rru与扩展设备groupmark相同，否则不正确
      extendDevice_baseBandIndex: '0',//此处extendDevice_baseband与扩展设备groupmark相同，否则不正确
      showAnchorAndNcellStage: ['SJGH', 'RWSH', 'JFMY', 'BJPH', 'SJJZ', 'GJHC', 'DZYZ', 'DYSH', 'RWPG', 'XQBH'],
      showTransferConfigStage: ['CSDD', 'RWSH'],
      editTabTransferStage: ['CSDD'],
      editTabBoardStage: ['GHK', 'SJK', 'SGGK'], // 板卡可编辑阶段
      showBadgeStage: ['GJHC'],
      showChangeFormToolTipStage: ['GJHC'],
      // 线网基站
      openSiteEnodeb: [],

      //工期
      enodebPeriodStage: [],
      curEnodebPeriodData: [],
      curEnodebPeriodTitle: [],
      curConstructionPeriod: '', //当前选中建设工期  用于工期数据筛选
      curConstructionStage: '', //当前选中建设阶段  用于工期数据筛选
      enodebPeroidSwith: false,
      lookBackPeroidInit: true,

      //建设工期
      constructionPeriodData: null,
      constructionPeriodTitle: [],

      //挂起工期
      hangupPeriodData: null,
      hangupPeriodTitle: [],

      // 江西电调
      electronicList: [],

      // 导入错误文件路径
      errorFileBlob: null,
      elements: null,
    })
    watch(
      () => props.clickItem,
      (value, prev) => {
        let data = null
        if (value) {
          data = value
        } else {
          data = prev
        }
        if (data && "" !== data) {
          const tabIndexArray = extractTabIndexesFromText(data.title)

          tabIndexArray && tabIndexArray.forEach((data) => {
            if (data) {
              findAndClick(fieldGroupTitleMap[data.code] + "" + (data.index + 1))
            }
          })
          // 使用 Vue 的 nextTick 确保 DOM 完全更新后再执行 scrollIntoView
          nextTick(() => setErrorStyle(data.fields, domRefs[data.name]))
        }


      },
    )
    const findAndClick = (name) => {
      const elements = document.querySelectorAll('.el-badge');

      let targetElement = null;

      elements.forEach(element => {
        if (element.textContent.trim() === name) {
          targetElement = element;
        }
      });
      // 如果找到了匹配的元素，则点击
      if (targetElement) {
        targetElement.click();
      } else {
        console.log('Element not found');
      }
    }
    const setErrorStyle = (fields, ref) => {
      if(fields === null || fields=== undefined) {
        return;
      }
      fields.forEach(field => {
        // 如果传入了 ref，限定查找范围在 ref 组件内部
        const container = ref ? ref.$el || ref : document;

        // 查找指定 ref 下的所有匹配的元素
        const elements = container.querySelectorAll(`label[for="${field.name}"]`);

        state.elements = elements
        // 遍历所有匹配的元素，设置样式
        elements.forEach(element => {
          element.scrollIntoView({ behavior: "smooth", block: "center", inline: "center" });
          // 设置内联样式
          element.style.border = "1px solid red";
          // 设置定时器，5秒后移除样式
          setTimeout(() => {
            element.style.border = "";
          }, 5000);
        });
      });
    };

    // static state
    let originSplit = null
    let recordSelectedPeriodRow = null // 记录已选择工期 避免重复选中导致清除
    let recordBaseBandData = {}

    const newEquipmentCode = 'NewBuild_Sitetype'
    const defaultExtendEquipment = [{ sort: 0, equipId: 1 }]
    const createSerializeChildrenFields = [
      { code: 'baseBand', name: 'position' },
      { code: 'rru', name: 'rruPosition', other: 'rruPlatformNo' },
      { code: 'antenna', name: 'antPosition', other: 'antPlatformNo' }
    ]

    const sysconfig = useGetters('sysconfig')()
    const getRruExtendDevice = computed(() => getCurrentRru.value?.rruExtendDeviceList || [])
    const showInDemandStage = computed(() => stage.value === 'XQJD')
    const siteTabEditable = computed(
      () => ['XQK','GHK', 'SJK'].includes(childStage.value) && isCurrentStage.value
    )
    const enodebTabEditable = computed(
      () => siteTabEditable.value || inResourcePool.value
    )

    // enodeb computed
    const getCurrentScheme = computed(
      () =>
        planSite.value?.siteList && planSite.value.siteList[schemeIndex.value]
    )
    const getEnodebList = computed(() => getCurrentScheme.value?.enodebList)
    const allEnodebList = computed(() =>
      getEnodebList.value
        ? getEnodebList.value.concat(state.openSiteEnodeb)
        : []
    )
    const isOpenSite = computed(() => {
      const currentEnodeb = allEnodebList.value[state.enodebIndex]
      return currentEnodeb && currentEnodeb.openSite
    })
    const formDisable = computed < Boolean > (
      () => !isCurrentStage.value || isOpenSite.value
    )
    const getCurrentEnodeb = computed(
      () => allEnodebList.value && allEnodebList.value[state.enodebIndex]
    )
    const getBaseBandList = computed({
      get: () => getCurrentEnodeb.value?.baseBandList,
      set: (value) => {
        getCurrentEnodeb.value && (getCurrentEnodeb.value.baseBandList = value)
      }
    })
    const getCellList = computed(() => getCurrentEnodeb.value?.cellList)
    const getCurrentCell = computed(
      () => getCellList.value && getCellList.value[state.cellIndex]
    )
    const getRruList = computed(() => getCurrentCell.value?.rruList)
    const getRruInheritList = computed(() => getRruList?.value?.map((item, index) => ({
      label: `射频单元${index + 1}`,
      value: index
    })))
    const getCurrentRru = computed(() => getRruList.value && getRruList.value[state.rruIndex])
    const getAntennaList = computed(() => getCurrentRru.value?.antennaList || [])
    const  getAntennaInheritList = computed(() => getAntennaList.value.map((item, index) => ({
      label: `天线${index + 1}`,
      value: index
    })))
    const getRRUExtendDeviceList = computed(() => getCurrentRru.value?.rruExtendDeviceList || [])
    //基带设备下，扩展设备列表
    const getBaseBandExtendDeviceList = computed(()=>getBaseBandList.value ? getBaseBandList.value[state.baseBandIndex]?.bbuExtendDeviceList : []);
    const handleRruInherit = (data) => {
      const {enableInherit, inheritIndex, count } = data
      const sourceData = enableInherit ?
        getRruList.value[inheritIndex] : createEmptyRru()
      // sourceData.antennaList.forEach()
      // 保留天线列表的第一个元素，删除其余元素

      if(enableInherit && !sourceData) return
      for(let i = 0; i < count; i++) {
        const newData = deepClone(sourceData)
        handleInheritedRruData(newData)
        newData.rruReuse = newEquipmentCode
        configureRruName(newData)
        configureAntName(newData.antennaList[0])
        if (newData.antennaList.length > 0) {
          newData.antennaList.splice(1)
          newData.antennaList[0].antReuse = newEquipmentCode
          // 'id', 'antIdPlan', 'sort', 'resourceCode'
          newData.antennaList[0].id = null
          newData.antennaList[0].antIdPlan = null
          newData.antennaList[0].sort = 0
          newData.antennaList[0].resourceCode = null
        }
// 保留扩展设备列表的第一个元素，删除其余元素
        if (newData.rruExtendDeviceList.length > 0) {
          newData.rruExtendDeviceList.splice(1)
          newData.rruExtendDeviceList[0].id = null
        }
// 保留板卡列表的第一个元素，删除其余元素
        if (newData.boardList.length > 0) {
          newData.boardList.splice(1)
          newData.boardList[0].id = null
        }
        getRruList.value.push(newData)
      }
      // console.log('getRruList.length',getRruList.value.length)
    }
    const createEmptyAnt = (keepFields = {}) => {
      const template = getAntennaList.value[0];
      const emptyAnt = {};

// 先将所有字段设为 null
      Object.keys(template).forEach(key => {
        emptyAnt[key] = null;
      });

// 合并需要保留的字段值
      return { ...emptyAnt, ...keepFields };
    }

    const createEmptyRru = (keepFields = {}) => {
      const template = getRruList.value[0];
      const emptyAnt = {};

// 先将所有字段设为 null
      Object.keys(template).forEach(key => {
        emptyAnt[key] = null;
      });

// 合并需要保留的字段值
      return { ...emptyAnt, ...keepFields };
    }

    const handleAntennaInherit = (data) => {
      const {enableInherit, inheritIndex, count } = data
      const sourceData = enableInherit ?
        getAntennaList.value[inheritIndex] :
        createEmptyAnt()


      if(enableInherit && !sourceData) return

      for(let i = 0; i < count; i++) {
        const newData = deepClone(sourceData)
        handleInheritedAntennaData(newData)
        // console.log('antnewdata',newData)
        // useExpendAddTab(newData)
        newData.antReuse = newEquipmentCode
        configureAntName(newData)
        getAntennaList.value.push(newData)
      }
    }
    const handleInheritedRruData = (data) => {
      const clearFields = ['id', 'cellIdPlan','rruId', 'sort', 'resourceCode','resourceCodeRru']
      clearFields.forEach(field => {
        data[field] = null
      })
      data.sort = getRruList.value.length
    }
    const handleInheritedAntennaData = (data) => {
      const clearFields = ['id', 'antIdPlan', 'sort', 'resourceCode','resourceCodeAnt']
      clearFields.forEach(field => {
        data[field] = null
      })
      data.sort = getAntennaList.value.length
    }
    const getCurrentAnt = computed(
      () => getAntennaList.value && getAntennaList.value[state.antennaIndex]
    )
    const enodebSamePeriod = computed(() => {
      const curEnodebPeroid =
        state.enodebPeriodStage && state.enodebPeriodStage[state.enodebIndex]
      return (
        !curEnodebPeroid ||
        !curEnodebPeroid.samePeriod ||
        curEnodebPeroid.samePeriod == 'N'
      )
    })
    const getFileBaseStructurePath = computed(
      () =>
        `${siteCmccId.value}/${stage.value}/scheme_${+schemeIndex.value + 1}/`
    )
    const getResourceCodeBasePath = computed(
      () => `${siteCmccId.value.replace(/-/g, '')}S${+schemeIndex.value + 1}`
    )
    const getCurrentEnodebElectronic = computed(() =>
      state.electronicList?.find(
        (i) => i.enodebId == allEnodebList.value[state.enodebIndex]?.enodebId
      )
    )
    const getPeriodTableData = computed(() => {
      state.enodebPeroidSwith &&
      domRefs.constructionPeriodTable?.clearSelection()
      let data = state.curEnodebPeriodData
      if (state.curConstructionPeriod) {
        data = data?.filter(
          (item) => item.construction_period === state.curConstructionPeriod
        )
      }

      if (state.curConstructionStage) {
        data = data?.filter(
          (item) => item.construction_stage === state.curConstructionStage
        )
      }

      state.enodebPeroidSwith &&
      data?.forEach((item) => {
        if (item.id === getCurrentEnodeb.value.periodStageProject) {
          domRefs.constructionPeriodTable.selectedRow(item)
        }
      })
      state.enodebPeroidSwith = false
      // 非当前处理阶段 禁止选中
      if (!isCurrentStage.value) {
        data?.forEach((item) => {
          item.disabled = true
          if (state.lookBackPeroidInit) {
            if (item.id === getCurrentEnodeb.value.periodStageProject) {
              domRefs.constructionPeriodTable.selectedRow(item)
              state.lookBackPeroidInit = false
            }
          }
        })
      }
      return data || []
    })
    const getSameOrderShareList = computed(() => {
      const map = { BBU: [], DU: [], CU: [], rru: [], ant: [] }
      getEnodebList.value.forEach((enodeb, index) => {
        if (index != state.enodebIndex) {
          enodeb.baseBandList.forEach((baseBand) => {
            map[baseBand.subType].push(baseBand)
          })
          enodeb.cellList.forEach((cell) => {
            cell.rruList.forEach((rru) => {
              map.rru.push(rru)
              rru.antennaList.forEach((ant) => {
                map.ant.push(ant)
              })
            })
          })
        }
      })
      return map
    })
    const isShowBadgeStage = computed(() => {
      return state.showBadgeStage.includes(childStage.value)
    })
    const isShowChangeFormToolTipStage = computed(() => {
      return state.showChangeFormToolTipStage.includes(childStage.value)
    })
    provide('sameOrderShareList', ref(getSameOrderShareList))

    onMounted(() => {
      if (isCurrentProvince('HN')) {
        state.showTransferConfigStage.push('SJGH')
        state.editTabTransferStage.push('SJGH')
      } else if (isCurrentProvince('HE')) {
        state.showTransferConfigStage.push('ZLSH')
        state.showTransferConfigStage.push('SJK')
      }
      isCurrentProvince('NM') && state.editTabBoardStage.push('CSDD')
    })

    // watch
    // room
    watch(
      () => getCurrentScheme.value?.roomList,
      (room, prevRoom) => {
        useSerializeChildrenFields('room', room?.length || 0)
        setShelfOrPlatformSelect('baseband')
        setShelfOrPlatformSelect('rru')
        setShelfOrPlatformSelect('ant')
        setShelfOrPlatformSelect('extendDevice_rru')
        setShelfOrPlatformSelect('extendDevice_baseBand')

      },
      {
        deep: true,
      }
    )
    // roof
    watch(
      () => getCurrentScheme.value?.roofList,
      (roof, prevRoof) => {
        useSerializeChildrenFields('roof', roof?.length || 0)
        setShelfOrPlatformSelect('baseband')
        setShelfOrPlatformSelect('rru')
        setShelfOrPlatformSelect('ant')
        setShelfOrPlatformSelect('extendDevice_rru')
        setShelfOrPlatformSelect('extendDevice_baseBand')

      },
      {
        deep: true,
      }
    )
    //监听bbu下面的扩展设备
    watch(
      () => {
        try {
          // 检查getBaseBandList.value是否为数组以及state.baseBandIndex是否在有效范围内
          if (Array.isArray(getBaseBandList.value) && getBaseBandList.value[state.baseBandIndex]) {
            return getBaseBandList.value[state.baseBandIndex]?.bbuExtendDeviceList?.length;
          }
          return 0; // 如果检查失败，返回0
        } catch (error) {
          console.error('Error while accessing bbuExtendDeviceList length:', error);
          return 0; // 发生错误时返回0
        }
      },
      (extend, preExtend) => {
        const pfPositionField = fields.value['rru']?.find(
          (i) => i.name === 'relatedRhubSelect'
        )
        if( getBaseBandList.value){
          const length = getBaseBandList.value[state.baseBandIndex]?.bbuExtendDeviceList?.length
          const children = new Array(length).fill(1).map((item, index) => {
            //此处""不要去除，否则变为Integer型影响显示
            return { name: '扩展设备' + (item + index), value: item + index + "", }
          })
          pfPositionField && Vue.set(pfPositionField, 'children', children)
        }
        setShelfOrPlatformSelect('baseband')
        setShelfOrPlatformSelect('rru')
        setShelfOrPlatformSelect('ant')
        setShelfOrPlatformSelect('extendDevice_rru')
        setShelfOrPlatformSelect('extendDevice_baseBand')

      }
    )
    function setShelfOrPlatformSelect(mark) {
      let roomRoofId = 1
      let roomRoof = 'room'
      let relatedResourceSelect = null
      if (mark === 'baseband') {
        getBaseBandList.value && getBaseBandList.value.length>0 && [state.baseBandIndex] && (roomRoof = getBaseBandList.value[state.baseBandIndex]?.position)
        getBaseBandList.value && getBaseBandList.value[state.baseBandIndex] && (roomRoofId = getBaseBandList.value[state.baseBandIndex].positionNo)
        relatedResourceSelect = fields.value['equipment_baseBand']?.find((i) => i.name === 'relatedResourceSelect')
        fields.value['equipment_baseBand']?.find((i) => i.name === 'positionNo')
      }
      if (mark === 'rru') {
        getCurrentRru.value && (roomRoofId = getCurrentRru.value.rruPositionNo)
        getCurrentRru.value && (roomRoof = getCurrentRru.value.rruPosition)
        relatedResourceSelect = fields.value['rru']?.find((i) => i.name === 'relatedResourceSelect')
        //设置安装平台层数下拉
        const rruPlatformNoField = fields.value['rru']?.find(
          (i) => i.name === 'rruPlatformNo'
        )
        if (roomRoof === 'room') {
          //机架则为1
          createSerializeChildren(rruPlatformNoField, 1)
        } else {
          //平台则设置为所选平台的层数
          let count = 1;
          //解决为空时控制台爆红
          // console.log('解决为空时控制台爆红',getCurrentScheme.value,getCurrentRru.value,roomRoof,getCurrentScheme.value[roomRoof + 'List'],getCurrentRru.value['rruPositionNo'] - 1)
          if(getCurrentScheme.value[roomRoof + 'List'] && getCurrentScheme.value[roomRoof + 'List'][getCurrentRru.value['rruPositionNo'] - 1] && getCurrentScheme.value[roomRoof + 'List'][getCurrentRru.value['rruPositionNo'] - 1]['platformList']){
            count = getCurrentScheme.value[roomRoof + 'List'][getCurrentRru.value['rruPositionNo'] - 1]['platformList'][getCurrentRru.value.relatedResourceSelect - 1]?.pfNum || 1
          }
          createSerializeChildren(rruPlatformNoField, count);
        }
      }
      //rru下的扩展设备，解决【平台/机架，】在初始化时为空
      if (mark === 'extendDevice_rru') {
        getCurrentRru.value && (roomRoofId = getRRUExtendDeviceList.value[state.extendDevice_rruIndex]?.positionNo)
        getCurrentRru.value && (roomRoof =  getRRUExtendDeviceList.value[state.extendDevice_rruIndex]?.position)
        relatedResourceSelect = fields.value['extend_device_rru']?.find((i) => i.name === 'relatedResourceSelect')
      }
      //系带设备下的扩展设备，平台/机架在初始化时为空
      if (mark === 'extendDevice_baseBand') {
        if(getBaseBandExtendDeviceList.value && getBaseBandExtendDeviceList.value.length>0){
          getCurrentRru.value && (roomRoofId = getBaseBandExtendDeviceList.value[state.extendDevice_baseBandIndex]?.positionNo)
          getCurrentRru.value && (roomRoof =  getBaseBandExtendDeviceList.value[state.extendDevice_baseBandIndex]?.position)
          relatedResourceSelect = fields.value['extend_device_baseband']?.find((i) => i.name === 'relatedResourceSelect')
        }
      }
      if (mark === 'ant') {
        getAntennaList.value && getAntennaList.value[state.antennaIndex] && (roomRoof = getAntennaList.value[state.antennaIndex].antPosition)
        getAntennaList.value && getAntennaList.value[state.antennaIndex] && (roomRoofId = getAntennaList.value[state.antennaIndex].antPositionNo)
        relatedResourceSelect = fields.value['antenna']?.find((i) => i.name === 'relatedResourceSelect')
        //设置安装平台层数下拉
        const antPlatformNoField = fields.value['antenna']?.find((i) => i.name === 'antPlatformNo')
        if (roomRoof === 'room') {
          //机架则为1
          createSerializeChildren(antPlatformNoField, 1)
        } else {
          let count =1;
          if(getCurrentScheme.value[roomRoof + 'List'] && getCurrentScheme.value[roomRoof + 'List'][getCurrentAnt?.value['antPositionNo'] - 1] && getCurrentScheme.value[roomRoof + 'List'][getCurrentAnt?.value['antPositionNo'] - 1]['platformList']){
            count = getCurrentScheme.value[roomRoof + 'List'][getCurrentAnt?.value['antPositionNo'] - 1]['platformList'][getCurrentAnt.value.relatedResourceSelect - 1]?.pfNum || 1
          }
          //平台则设置为所选平台的层数
          createSerializeChildren(antPlatformNoField, count);
        }
      }
      //机架
      const room = getCurrentScheme.value['roomList']?.find(
        (i) => i.roomId === roomRoofId
      ) || { shelfList: []}
      if (mark ==='baseband' && isCurrentProvince("XJ")) {
        getBaseBandList.value && (getBaseBandList.value[state.baseBandIndex].installedPos = room.address)
        getBaseBandList.value?.forEach(baseBand => {
          if (baseBand.position === roomRoof && baseBand.positionNo === roomRoofId) {
            baseBand.installedPos = room.address
          }
        })
      }

      const shelfSelect = new Array(room['shelfList']?.length).fill(1).map((item, index) => {
        return {
          name: '机架' + (item + index),
          value: item + index,
        }
      })
      //平台
      const roof = getCurrentScheme.value['roofList']?.find(
        (i) => i.roofId === roomRoofId
      ) || { platformList: []}
      const platformSelect = new Array(roof['platformList']?.length).fill(1).map((item, index) => {
        return {
          name: '平台' + (item + index),
          value: item + index,
        }
      })
      if (roomRoof === 'room') {
        relatedResourceSelect && Vue.set(relatedResourceSelect, 'children', shelfSelect)
      } else if (roomRoof === 'roof') {
        relatedResourceSelect && Vue.set(relatedResourceSelect, 'children', platformSelect)
      }




    }

    function useSerializeChildrenFields(mark, count) {
      if (!inResourcePool.value && count) {
        createSerializeChildrenFields.forEach((item) => {
          const { name, code, other } = item
          const data = getCurrentFormValue(code)
          const fieldKey = getCurrentFieldsGroupKey(code)
          data &&
          data[name] &&
          setCascadingPosition(fieldKey, name + 'No', data[name])

          // 选择的安装平台编号被删除后 默认设为1
          if (data && data[name] && data[name] === mark && count && data[name + 'No'] > count) {
            const defaultPositionNo = 1
            domRefs[code].setFormValue({ [name + 'No']: defaultPositionNo })
            setCascadingPositionNo(data, defaultPositionNo, name, code)
          }
          other && data && setPlatformNo(code, data, data[name + 'No'])

        })
      }
    }

    const configureRruName = (rruFormData) =>{
      const { sort, rruName, rruType, rruReuse } = rruFormData
      if (rruReuse === newEquipmentCode && rruName) {
        rruFormData.rruName =
          getCurrentCell.value.cellName +
          '-' +
          (rruType ?? '') +
          useCreateDoubleSerialNumber(getRruList.value.length + 1)
      }
    }
    const configureAntName = (antFormData) =>{
      const { sort, antName,  antReuse } = antFormData
      if (antReuse === newEquipmentCode && antName) {
        antFormData.antName =
          getCurrentCell.value.cellName +
          '-天线' +
          useCreateDoubleSerialNumber(getCurrentCell.value.rruList.map(rru => rru.antennaList.length).reduce((a, b) => a + b, 0) +  1)
      }
    }


    function updateAntennaNames(antFormData) {
      let count = 0
      getCurrentCell.value.rruList.forEach((rru) => {
        rru.antennaList.forEach((ant) => {
          const { antReuse, antName } = ant
          count += 1

          if (antReuse === newEquipmentCode && antName) {
            ant.antName =
              getCurrentCell.value.cellName +
              '-天线' +
              useCreateDoubleSerialNumber(count)
          }
        })
      })
    }

    const handleAddTab = (tab, code) => {
      console.log('handleAddTab',tab,code)
      if (isCurrentProvince("XJ")) {
        if (code === 'rru') {
          getRruList.value.pop()
          domRefs.dialogs.rru.inheritDialogVisible = true
        } else  if (code === 'antenna') {
          domRefs.dialogs.antenna.inheritDialogVisible = true
          getAntennaList.value.pop()
        }
      }
      useFormPlanId(code, tab, getCurrentFormValue(code, false))

      // 生成resourceCode
      const dataPath = {
        enodeb: 'enodeb',
        baseBand: 'enodeb_baseBand',
        cell: 'enodeb_cell',
        rru: 'enodeb_cell_rru',
        antenna: 'enodeb_cell_rru_antenna'//小区下的天线(antenna_cell)与射频单元下的天线(antenna)
      }
      tab.resourceCode = getResourceCodePath(dataPath[code], true, '', '', tab)
      useChildrenResourceCode(tab, tab.resourceCode)

      // 广西复制数据时删除字段
      isCurrentProvince('GX') && useRecursionCallBack(tab, (obj) => {
        new_copy_delete_field.forEach(key => {
          delete obj[key]
        })
      })
      switch (code) {
        case 'enodeb': {
          getEnodebList.value.push(tab)
        }
          break
        case 'cell': {
          tab.sectorId = null;
          tab.resourceCodeCell = null;
          tab.cgi=null;
          if (isCurrentProvince('HN,XJ,YN')) {
            const { sort, cellName } = tab

            if (cellName) {
              tab.cellName =
                useStringPrev(cellName) +
                useCreateDoubleSerialNumber(getCellList.value.length)
            }
          }
        }
          break
        case 'rru': {
          tab.rruName = null
          if (isCurrentProvince('HN,XJ,YN')) {
            configureRruName(tab)
          }
        }
          break
        case 'antenna': {
          configureAntName(tab)
        }
          break
      }
      //
      // if (isCurrentProvince('HN,XJ,YN') && ['rru', 'antenna']) {
      //   updateAntennaNames()
      // }
    }
    const handleRemoveTab = (tabIndex, tab, code) => {
      // 处理有线网基站时删除无法删除基站bug
      if (code === 'enodeb') {
        getEnodebList.value.splice(tabIndex, 1)
      }

      // 重排resourceCode
      const currentDataList = getCurrentFormValue(code, false)
      currentDataList?.forEach((item, index) => {
        if (item.resourceCode) {
          const markLastIndex = item.resourceCode.lastIndexOf(
            useTransferMark(code, true)
          )
          item.resourceCode =
            item.resourceCode.slice(0, markLastIndex + 1) + (index + 1)
          useChildrenResourceCode(item, item.resourceCode)
        }
      })
    }
    const handleSwitchTab = (index, mark) => {
      console.log('tab切换方法被触发,index:',index,'mark:',mark)
      state[mark + 'Index'] = index
      if (mark === 'cell') {
        console.log('当前小区',getCurrentCell.value)
      }
      resetChildTab(index, mark)

      if (mark === 'baseBand') {
        const currentBaseBand = getCurrentFormValue(mark)
        state.equipmentActive = currentBaseBand.subType
      }

      if (mark === 'enodeb' && period.value) {
        state.enodebPeroidSwith = true
      }
    }

    /**
     * <AUTHOR>  修改：沈宗航  2025/5/16 14:38
     * @description:  扩展设备下拉框框值变动时被触发  扩展设备下的安装位置类型和平台/机架数据联动处理
     * @param field 字段对象
     * @param value 值
     * @param autoTrigger 自动触发：页面刷新或联动时为true，手动选下拉框时为false
     */
    const onFormUpdate = (field, value, autoTrigger, mark,groupMark) => {
      //autoTrigger==true时直接返回
      if (autoTrigger && !['position','positionNo'].includes(field?.name)) return
      if (mark === 'bbu') {
        switch (field.name) {
          case 'position': {
            let dataList = getCurrentScheme.value[value + 'List']
            const childFieldName = 'positionNo'
            // 设置 安装位置ID 选项
            const positionNoField = fields.value['extend_device_baseband']?.find((i) => i.name === childFieldName)
            console.log('onFormUpdate')
            createSerializeChildren(positionNoField, dataList?.length || 0)
            if(autoTrigger===false){//自动触发时不置空值，否则初始化时数据 为空
              domRefs.extendDeviceBbu?.setFormValue({ [childFieldName]: '' })
            }
          }
            break
          case 'positionNo': {
            // 设置 平台/机架
            const pfPositionField = fields.value['extend_device_baseband']?.find((i) => i.name === 'relatedResourceSelect')
            //组装下拉选项
            // 根据选择的天面获取
            const cur = domRefs.extendDeviceBbu?.getCurrentExtendDevice
            if (cur && cur['position'] === 'roof') {
              const roof = getCurrentScheme.value['roofList']?.find((i) => i.roofId === value) || {platformList: []}
              const children =new Array(roof['platformList']?.length).fill(1).map((item, index) => {
                return { name:  '平台' + (item + index), value: item + index, }
              })
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              const roofData = getCurrentScheme.value['roofList'][value - 1]
              domRefs.extendDeviceBbu.setFormValue({"euLatitude":roofData.roofLat,"euLongitude":roofData.roofLon,"siteNameCmcc": roofData.roofNameCmcc,"siteIdCmcc": roofData.roofIdCmcc})
            } else if (cur && cur['position'] === 'room') {
              const room = getCurrentScheme.value['roomList']?.find((i) => i.roomId === value) || {shelfList: []}
              const children =new Array(room['shelfList'].length).fill(1).map((item, index) => {
                return { name:  '机架' + (item + index), value: item + index}
              })
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              const roomData = getCurrentScheme.value['roomList'][value - 1]
              domRefs.extendDeviceBbu.setFormValue({"euLatitude":roomData.roomLat,"euLongitude":roomData.roomLon,"siteNameCmcc": roomData?.roomNameCmcc,"siteIdCmcc": roomData.roomIdCmcc})
            }
          }
            break
          case 'relatedResourceSelect': {
            switch (groupMark) {
              case 'extendDevice_baseBand': {
                const extendDeviceBbu = domRefs.extendDeviceBbu?.getCurrentExtendDevice
                const roomOrRoof = extendDeviceBbu.position
                let relatedResource

                if (roomOrRoof === 'room' && value) {
                  const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceBbu.positionNo - 1]['shelfList'][value - 1]
                  relatedResource = shelfData?.resourceCode
                } else {
                  const platformData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceBbu.positionNo - 1]['platformList'][value - 1]
                  relatedResource = platformData?.resourceCode
                  // 只有平台才处理resourceCodeSpace和resourceCodeName
                  const resourceCodeSpace = platformData?.resourceCodeSpace
                  const resourceCodeName = platformData?.resourceNameSpace
                  // 设置空间编码下拉
                  if (resourceCodeSpace) {
                    const spaceCodeField = fields.value['extend_device_baseband']?.find(i => i.name === 'relatedSpaceCode')
                    if (spaceCodeField) {
                      const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                        name: code.trim(),
                        value: code.trim()
                      }))
                      spaceCodeField && Vue.set(spaceCodeField, 'children', spaceCodeOptions)
                      domRefs.extendDeviceBbu?.setFormValue({
                        "relatedSpaceCode": spaceCodeOptions[0].value
                      })
                    }
                  }
                  // 设置空间名称下拉
                  if (resourceCodeName) {
                    const spaceNameField = fields.value['extend_device_baseband']?.find(i => i.name === 'relatedSpaceName')
                    if (spaceNameField) {
                      const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                        name: name.trim(),
                        value: name.trim()
                      }))
                      spaceNameField && Vue.set(spaceNameField, 'children', spaceNameOptions)
                      //设置值为第一个下拉
                      domRefs.extendDeviceBbu?.setFormValue({
                        "relatedSpaceName": spaceNameOptions[0].value
                      })
                    }
                  }
                }

                domRefs.extendDeviceBbu?.setFormValue({
                  "relatedResource": relatedResource
                })
                break
              }
            }
          }
            break
          case 'relatedSpaceName': {
            const extendDeviceBbu = domRefs.extendDeviceBbu?.getCurrentExtendDevice
            const relatedResourceSelect = extendDeviceBbu.relatedResourceSelect
            const roomOrRoof = extendDeviceBbu.position
            let relatedResource

            if (roomOrRoof === 'room' && value) {
              const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceBbu.positionNo - 1]['shelfList'][relatedResourceSelect - 1]
              relatedResource = shelfData?.resourceCode
            } else {
              const platformData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceBbu.positionNo - 1]['platformList'][relatedResourceSelect - 1]
              relatedResource = platformData?.resourceCode
              const resourceCodeName = platformData?.resourceNameSpace
              //resourceCodeName是逗号分隔，判断当前value是第几个元素，根据索引设置relatedSpaceCode的值
              if (resourceCodeName) {
                const resourceCodeNameArray = resourceCodeName.split(',')
                const index = resourceCodeNameArray.indexOf(value)
                const resourceCodeSpace = platformData?.resourceCodeSpace
                if (resourceCodeSpace) {
                  const resourceCodeSpaceArray = resourceCodeSpace.split(',')
                  domRefs.extendDeviceBbu?.setFormValue({
                    "relatedSpaceCode": resourceCodeSpaceArray[index] || null
                  })
                }
              }
            }
          }
        }
      } else if (mark === 'rru') {
        //rru下扩展设备
        switch (field.name) {
          case 'position': {
            // if (!autoTrigger) {//此处注释原因：初始化时未创建安装位置ID，导致下拉框为空，故注释
            let dataList = getCurrentScheme.value[value + 'List']
            const childFieldName = 'positionNo'
            // 设置 安装位置ID 选项
            const positionNoField = fields.value['extend_device_rru']?.find((i) => i.name === childFieldName)
            const count = dataList.length
            //设置安装位置ID
            console.log('onFormUpdate')
            createSerializeChildren(positionNoField, count)
            // 设置 平台/机架
            if(autoTrigger===false){//自动触发（初始化页面）时不置空【安装位置ID】
              domRefs.extendDeviceRru.setFormValue({ [childFieldName]: '' })
            }
            // }
          }
            break
          case 'positionNo': {
            // 设置 平台/机架
            const pfPositionField = fields.value['extend_device_rru']?.find((i) => i.name === 'relatedResourceSelect')
            //组装下拉选项
            // 根据选择的天面获取
            const cur = domRefs.extendDeviceRru?.getCurrentExtendDevice
            if (cur && cur['position'] === 'roof') {
              const roof = getCurrentScheme.value['roofList']?.find((i) => i.roofId === value)
              const children =new Array(roof['platformList']?.length).fill(1).map((item, index) => {
                return { name:  '平台' + (item + index), value: item + index, }
              })
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              const roofData = getCurrentScheme.value['roofList'][value - 1]
              domRefs.extendDeviceRru.setFormValue({"euLatitude":roofData.roofLat,"euLongitude":roofData.roofLon,"siteNameCmcc": roofData.roofNameCmcc,"siteIdCmcc": roofData.roofIdCmcc})
            } else if (cur && cur['position'] === 'room') {
              const room = getCurrentScheme.value['roomList']?.find((i) => i.roomId === value);
              const children =new Array(room['shelfList'].length).fill(1).map((item, index) => {
                return { name:  '机架' + (item + index), value: item + index, }
              })
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              const roomData = getCurrentScheme.value['roomList'][value - 1]
              domRefs.extendDeviceRru.setFormValue({"euLatitude":roomData.roomLat,"euLongitude":roomData.roomLon,"siteNameCmcc": roomData?.roomNameCmcc,"siteIdCmcc": roomData.roomIdCmcc})
            }
          }
            break
          case 'relatedResourceSelect': {
            switch (groupMark) {
              case 'extendDevice_rru': {
                console.log('111',value)
                const rru = getRruList.value[state.rruIndex]
                const roomOrRoof = rru.rruPosition
                let relatedResource

                if (roomOrRoof === 'room' && value) {
                  const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['shelfList'][value - 1]
                  relatedResource = shelfData?.resourceCode
                } else {
                  const platformData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['platformList'][value - 1]
                  relatedResource = platformData?.resourceCode
                  // 只有平台才处理resourceCodeSpace和resourceCodeName
                  const resourceCodeSpace = platformData?.resourceCodeSpace
                  const resourceCodeName = platformData?.resourceNameSpace
                  console.log('resourceCodeSpace',resourceCodeSpace)
                  // 设置空间编码下拉
                  if (resourceCodeSpace) {
                    const spaceCodeField = fields.value['extend_device_rru']?.find(i => i.name === 'relatedSpaceCode')
                    if (spaceCodeField) {
                      const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                        name: code.trim(),
                        value: code.trim()
                      }))
                      spaceCodeField && Vue.set(spaceCodeField, 'children', spaceCodeField)
                      //设置值为第一个下拉
                      domRefs.extendDeviceRru?.setFormValue({
                        "relatedSpaceCode": spaceCodeOptions[0].value
                      })
                    }
                  }
                  // 设置空间名称下拉
                  if (resourceCodeName) {
                    const spaceNameField = fields.value['extend_device_rru']?.find(i => i.name === 'relatedSpaceName')
                    if (spaceNameField) {
                      const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                        name: name.trim(),
                        value: name.trim()
                      }))
                      spaceNameField && Vue.set(spaceNameField, 'children', spaceNameOptions)
                      //设置值为第一个下拉
                      domRefs.extendDeviceRru?.setFormValue({
                        "relatedSpaceName": spaceNameOptions[0].value
                      })
                    }
                  }
                }
                domRefs.extendDeviceRru?.setFormValue({
                  "relatedResource": relatedResource
                })
                break
              }
            }
          }
            break
          case 'relatedSpaceName': {
            const extendDeviceRru = domRefs.extendDeviceRru?.getCurrentExtendDevice
            const relatedResourceSelect = extendDeviceRru.relatedResourceSelect
            const roomOrRoof = extendDeviceRru.position
            let relatedResource

            if (roomOrRoof === 'room' && value) {
              const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceRru.positionNo - 1]['shelfList'][relatedResourceSelect - 1]
              relatedResource = shelfData?.resourceCode
            } else {
              const platformData = getCurrentScheme.value[roomOrRoof + 'List'][extendDeviceRru.positionNo - 1]['platformList'][relatedResourceSelect - 1]
              relatedResource = platformData?.resourceCode
              const resourceCodeName = platformData?.resourceNameSpace
              //resourceCodeName是逗号分隔，判断当前value是第几个元素，根据索引设置relatedSpaceCode的值
              if (resourceCodeName) {
                const resourceCodeNameArray = resourceCodeName.split(',')
                const index = resourceCodeNameArray.indexOf(value)
                const resourceCodeSpace = platformData?.resourceCodeSpace
                if (resourceCodeSpace) {
                  const resourceCodeSpaceArray = resourceCodeSpace.split(',')
                  domRefs.extendDeviceRru?.setFormValue({
                    "relatedSpaceCode": resourceCodeSpaceArray[index] || null
                  })
                }
              }
            }
          }
            break;
        }
      }
    }
    //此处传入的mark为antenna或antenna_cell,需求阶段传入false
    const handleFormValue = (data, mark) => {
      const markTmp = mark?.replace('_rru','').replace('_cell', '')
      const currentFormValue = getCurrentFormValue(mark)
      if (!currentFormValue || !isCurrentStage.value) return
      switch (markTmp) {
        case 'enodeb': {
          const currentBaseBand = getCurrentFormValue('baseBand')

          if (isCurrentProvince('HN,XJ,YN')) {
            if (
              Object.keys(data).includes('enodebName') &&
              currentBaseBand.utilizeMode === newEquipmentCode
            ) {
              domRefs.baseBand?.setFormValue({
                name: autoCreateBaseBandName(
                  currentFormValue,
                  currentBaseBand
                )
              })
            }
          }

          if (Object.keys(data).includes('enodebId')) {
            const result = autoCreateCGI(
              getCurrentFormValue('cell'),
              currentFormValue,
              data
            )
            currentFormValue.cellList.forEach((cell) => {
              Object.assign(cell, result)
            })
          }
        }
          break
        case 'cell': {
          if (isCurrentProvince('HN,XJ,YN') && Object.keys(data).includes('cellName')) {
            getCurrentFormValue('rru').rruReuse === newEquipmentCode && setRruNameFormValue(currentFormValue)
            const currAntValue = getCurrentFormValue('antenna');
            currAntValue?.antReuse === newEquipmentCode && (domRefs.antenna || domRefs.antenna).setFormValue({
              antName: autoCreateAntName(currentFormValue, state.rruIndex, state.antennaIndex)
            })
          }
          domRefs.cell?.setFormValue(autoCreateCGI(currentFormValue, getCurrentFormValue('enodeb'), data));
        }
          break
        case 'rru': {
          if (isCurrentProvince('HN,XJ,YN')) {
            rruIntegrationAnt(currentFormValue)
            boardExtendsRru(currentFormValue)
          }
        }
          break
        case 'antenna': {
          if (isCurrentProvince('HN,XJ,YN')) {
            autoCreateAntPitchAngle(currentFormValue, data)
          }
        }
          break
      }
    }
    const handleFormEvent = (field, value, autoTrigger, mark) => {
      const { name } = field
      const changeObj = { [name]: value }
      if (mark === 'enodeb') {
        cellExtendsEnodeb(changeObj, autoTrigger)
        name === 'configure' &&
        (!autoTrigger || inResourcePool.value) &&
        serializeCellCount(value)
      }

      switch (name) {
        case 'relatedSpaceName': {
          console.log('handleFormEvent-field:',field,',value:',value,",mark:",mark);
          switch (mark) {
            case 'rru': {
              const rru = getRruList.value[state.rruIndex]
              const roomOrRoof = rru.rruPosition
              let relatedResource
              const relatedResourceSelect = rru.relatedResourceSelect
              if (roomOrRoof === 'room' && value) {
                const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['shelfList'][relatedResourceSelect - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['platformList'][relatedResourceSelect - 1]
                relatedResource = platformData?.resourceCode

                // 只有平台才处理resourceCodeSpace和resourceCodeName
                const resourceCodeName = platformData?.resourceNameSpace
                if (resourceCodeName) {
                  const resourceCodeNameArray = resourceCodeName.split(',')
                  const index = resourceCodeNameArray.indexOf(value)
                  const resourceCodeSpace = platformData?.resourceCodeSpace
                  if (resourceCodeSpace) {
                    const resourceCodeSpaceArray = resourceCodeSpace.split(',')
                    domRefs.rru?.setFormValue({
                      "relatedSpaceCode": resourceCodeSpaceArray[index] || null
                    })
                  }
                }
              }
            }
              break;
            case 'antenna': {
              const ant = getAntennaList.value[state.antennaIndex]
              const roomOrRoof = ant?.antPosition
              let relatedResource
              const relatedResourceSelect = ant.relatedResourceSelect
              if (roomOrRoof === 'room' && value) {
                const shelfData = ant && getCurrentScheme.value[roomOrRoof + 'List'][ant.antPositionNo - 1]['shelfList'][relatedResourceSelect - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = ant && getCurrentScheme.value[roomOrRoof + 'List'][ant.antPositionNo - 1]['platformList'][relatedResourceSelect - 1]
                relatedResource = platformData?.resourceCode
                const resourceCodeName = platformData?.resourceNameSpace
                if (resourceCodeName) {
                  const resourceCodeNameArray = resourceCodeName.split(',')
                  const index = resourceCodeNameArray.indexOf(value)
                  const resourceCodeSpace = platformData?.resourceCodeSpace
                  if (resourceCodeSpace) {
                    const resourceCodeSpaceArray = resourceCodeSpace.split(',')
                    domRefs.antenna?.setFormValue({
                      "relatedSpaceCode": resourceCodeSpaceArray[index] || null
                    })
                  }
                }
              }
            }
          }
        }
          break;
        case 'relatedRhubSelect': {
          domRefs?.rru?.setFormValue({"relatedRhub" :getBaseBandList.value[state.baseBandIndex]?.bbuExtendDeviceList[value - 1].resourceCode })
        }
          break;
        case 'indoorFlag': {
          // enodeb站型
          const currentField = fields.value[mark]?.find(
            (field) => field.name === 'configure'
          )
          currentField && controlFormFieldsChildren(value, currentField)
          !autoTrigger &&
          domRefs[mark].setFormValue({
            configure: value === 'MacroSite' ? 'S1' : 'O1'
          })
        }
          break
        case 'freBandCode': {
          const childrenData = field.children?.find(
            (item) => item.value === value
          )
          !autoTrigger && childrenData && domRefs[mark]?.setFormValue(childrenData)
        }
          break
        case 'factory':
        case 'rruFactory':
        case 'antFactory': {
          // 厂家
          const fieldPrefix = name.slice(0, -7)
          const childFieldName = fieldPrefix.length ? fieldPrefix + 'Model' : 'model'
          !autoTrigger && domRefs[mark].setFormValue({ [childFieldName]: '' })
        }
          break
        case 'model':
        case 'rruModel':
        case 'antModel': {
          // 型号
          !autoTrigger && setModelDataBackFill(field, value, mark)
        }
          break
        case 'extendEquip': {
          if (mark === 'baseBand') {
            if (value === 'Y') {
              getBaseBandList.value[0].extendEquipList = deepClone(defaultExtendEquipment)
              getCurrentEnodeb.value.extendEquipList = deepClone(defaultExtendEquipment)
            } else {
              getBaseBandList.value[0].extendEquipList = getCurrentEnodeb.value.extendEquipList = null
            }
          } else if (mark === 'rru') {
            getCurrentRru.value.extendEquipList = value === 'Y' ? deepClone(defaultExtendEquipment) : null
          }
        }
          break
        case 'position':
        case 'rruPosition':
        case 'antPosition': {
          if (!autoTrigger) {
            const childFieldName = name + 'No'
            const fieldGroupKey = getCurrentFieldsGroupKey(mark)
            // 设置安装位置选项
            setCascadingPosition(fieldGroupKey, childFieldName, value)

            // 清空下级字段值
            domRefs[mark].setFormValue({ [childFieldName]: '' })
          }
        }
          break
        case 'positionNo':
        case 'rruPositionNo':
        case 'antPositionNo': {
          if (!autoTrigger) {
            const parentFiledName = name.slice(0, -2)
            const data = getCurrentFormValue(mark)
            const fieldGroupKey = getCurrentFieldsGroupKey(mark)
            setCascadingPositionNo(data, value, parentFiledName, mark)

            const code = name.slice(0, 3)
            const fieldsMap = { rru: 'rru', }
            fieldsMap[code] && setPlatformNo(fieldsMap[code], data, value)
            if (parentFiledName === 'position') {
              const pfPositionField = fields.value['equipment_baseBand']?.find((i) => i.name === 'relatedResourceSelect')
              const roomOrRoof = getBaseBandList.value[state.baseBandIndex][parentFiledName]
              const roof = getCurrentScheme.value[ roomOrRoof + 'List']?.find((i) => (roomOrRoof === 'roof' ? i.roofId = value : i.roomId = value))
              const children =new Array(roomOrRoof === 'roof' ? roof['platformList']?.length : roof['shelfList'].length).fill(1).map((item, index) => {
                return {
                  name: roomOrRoof === 'roof' ?  '平台' + (item + index) :  '机架' + (item + index),
                  value: item + index,
                }
              })
              //设置机架平台下拉
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              domRefs.baseBand?.setFormValue({"relatedResourceSelect": null,"relatedResource": null})
            } else if (parentFiledName === 'rruPosition') {
              const pfPositionField = fields.value['rru']?.find(
                (i) => i.name === 'relatedResourceSelect'
              )
              const roomOrRoof = getRruList.value[state.rruIndex][parentFiledName]
              const roof = getCurrentScheme.value[ roomOrRoof + 'List']?.find(
                (i) => (roomOrRoof === 'roof' ? i.roofId = value : i.roomId = value)
              )
              const children =new Array(roomOrRoof === 'roof' ? roof['platformList']?.length : roof['shelfList'].length).fill(1).map((item, index) => {
                return {
                  name: roomOrRoof === 'roof' ?  '平台' + (item + index) :  '机架' + (item + index),
                  value: item + index,
                }
              })
              //设置机架平台下拉
              pfPositionField && Vue.set(pfPositionField, 'children', children)
              domRefs.rru.setFormValue({"relatedResourceSelect": null,"relatedResource": null,"rruPlatformNo":null})
            } else  {
              const pfPositionField = fields.value['antenna']?.find((i) => i.name === 'relatedResourceSelect')
              const roomOrRoof = getAntennaList.value[state.antennaIndex][parentFiledName]
              const roof = getCurrentScheme.value[ roomOrRoof + 'List']?.find((i) => (roomOrRoof === 'roof' ? i.roofId = value : i.roomId = value))
              const children =new Array(roomOrRoof === 'roof' ? roof['platformList']?.length : roof['shelfList'].length).fill(1).map((item, index) => {
                return {
                  name: roomOrRoof === 'roof' ?  '平台' + (item + index) :  '机架' + (item + index),
                  value: item + index,
                }
              })
              //设置机架平台下拉
              pfPositionField && Vue.set(pfPositionField,'children', children);
              (domRefs.antenna || domRefs.antenna_cell)?.setFormValue({"relatedResourceSelect": null,"relatedResource": null,"antPlatformNo":null})
            }
          }
        }
          break
        case 'relatedResourceSelect': {
          switch (mark) {
            case 'baseBand': {
              const baseband = getBaseBandList.value[state.baseBandIndex]
              const roomOrRoof = baseband.position
              let relatedResource

              if (roomOrRoof === 'room' && value) {
                const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][baseband.positionNo - 1]['shelfList'][value - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = getCurrentScheme.value[roomOrRoof + 'List'][baseband.positionNo - 1]['platformList'][value - 1]
                relatedResource = platformData?.resourceCode
              }

              domRefs.baseBand?.setFormValue({
                "relatedResource": relatedResource
              })
              break
            }

            case 'extendDevice_rru': {
              const rru = getRruList.value[state.rruIndex]
              const roomOrRoof = rru.rruPosition
              let relatedResource

              if (roomOrRoof === 'room' && value) {
                const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['shelfList'][value - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['platformList'][value - 1]
                relatedResource = platformData?.resourceCode

                // 只有平台才处理resourceCodeSpace和resourceCodeName
                const resourceCodeSpace = platformData?.resourceCodeSpace
                const resourceCodeName = platformData?.resourceCodeName

                // 设置空间编码下拉
                if (resourceCodeSpace) {
                  const spaceCodeField = fields.value['extendDevice_rru']?.find(i => i.name === 'relatedSpaceCode')
                  if (spaceCodeField) {
                    const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                      name: code.trim(),
                      value: code.trim()
                    }))
                    createSerializeChildren(spaceCodeField, spaceCodeOptions)
                  }
                }

                // 设置空间名称下拉
                if (resourceCodeName) {
                  const spaceNameField = fields.value['extendDevice_rru']?.find(i => i.name === 'relatedSpaceName')
                  if (spaceNameField) {
                    const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                      name: name.trim(),
                      value: name.trim()
                    }))
                    createSerializeChildren(spaceNameField, spaceNameOptions)
                  }
                }
              }

              domRefs.extendDevice_rru?.setFormValue({
                "relatedResource": relatedResource
              })
              break
            }

            case 'extendDevice_baseBand': {
              const baseband = getBaseBandList.value[state.baseBandIndex]
              const roomOrRoof = baseband.position
              let relatedResource

              if (roomOrRoof === 'room' && value) {
                const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][baseband.positionNo - 1]['shelfList'][value - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = getCurrentScheme.value[roomOrRoof + 'List'][baseband.positionNo - 1]['platformList'][value - 1]
                relatedResource = platformData?.resourceCode

                // 只有平台才处理resourceCodeSpace和resourceCodeName
                const resourceCodeSpace = platformData?.resourceCodeSpace
                const resourceCodeName = platformData?.resourceNameSpace

                // 设置空间编码下拉
                if (resourceCodeSpace) {
                  const spaceCodeField = fields.value['extend_device_baseband']?.find(i => i.name === 'relatedSpaceCode')
                  if (spaceCodeField) {
                    const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                      name: code.trim(),
                      value: code.trim()
                    }))
                    spaceCodeField && Vue.set(spaceCodeField, 'children', spaceCodeOptions)
                    domRefs.extendDeviceBbu?.setFormValue({
                      "relatedSpaceCode": spaceCodeOptions[0].value
                    })
                  }
                }

                // 设置空间名称下拉
                if (resourceCodeName) {
                  const spaceNameField = fields.value['extend_device_baseband']?.find(i => i.name === 'relatedSpaceName')
                  if (spaceNameField) {
                    const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                      name: name.trim(),
                      value: name.trim()
                    }))
                    spaceNameField && Vue.set(spaceNameField, 'children', spaceNameOptions)
                    //设置值为第一个下拉
                    domRefs.extendDeviceBbu?.setFormValue({
                      "relatedSpaceName": spaceNameOptions[0].value
                    })
                  }
                }
              }

              domRefs.extendDeviceBbu?.setFormValue({
                "relatedResource": relatedResource
              })
              break
            }

            case 'rru': {
              const rru = getRruList.value[state.rruIndex]
              const roomOrRoof = rru.rruPosition
              let relatedResource

              if (roomOrRoof === 'room' && value) {
                const shelfData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['shelfList'][value - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = getCurrentScheme.value[roomOrRoof + 'List'][rru.rruPositionNo - 1]['platformList'][value - 1]
                relatedResource = platformData?.resourceCode

                // 只有平台才处理resourceCodeSpace和resourceCodeName
                const resourceCodeSpace = platformData?.resourceCodeSpace
                const resourceCodeName = platformData?.resourceNameSpace

                // 设置空间编码下拉
                if (resourceCodeSpace) {
                  const spaceCodeField = fields.value['rru']?.find(i => i.name === 'relatedSpaceCode')
                  if (spaceCodeField) {
                    const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                      name: code.trim(),
                      value: code.trim()
                    }))
                    spaceCodeField && Vue.set(spaceCodeField, 'children', spaceCodeOptions)
                    domRefs.rru?.setFormValue({
                      "relatedSpaceCode": spaceCodeOptions[0].value
                    })
                  }
                }

                // 设置空间名称下拉
                if (resourceCodeName) {
                  const spaceNameField = fields.value['rru']?.find(i => i.name === 'relatedSpaceName')
                  if (spaceNameField) {
                    const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                      name: name.trim(),
                      value: name.trim()
                    }))
                    spaceNameField && Vue.set(spaceNameField, 'children', spaceNameOptions)
                    domRefs.rru?.setFormValue({
                      "relatedSpaceName": spaceNameOptions[0].value
                    })
                  }
                }
              }

              domRefs.rru?.setFormValue({
                "relatedResource": relatedResource
              })

              //设置安装平台层数下拉
              const positionField = fields.value['rru']?.find(
                (i) => i.name === 'rruPlatformNo'
              )
              if (roomOrRoof === 'room') {
                //机架则为1
                createSerializeChildren(positionField, 1)
              } else {
                //平台则设置为所选平台的层数
                createSerializeChildren(positionField, getCurrentScheme.value?.[roomOrRoof + 'List']?.[rru.rruPositionNo - 1]?.['platformList'][value - 1]?.pfNum || 1)
              }
              break
            }

            case 'antenna': {
              const ant = getAntennaList.value[state.antennaIndex]
              const roomOrRoof = ant?.antPosition
              let relatedResource

              if (roomOrRoof === 'room' && value) {
                const shelfData = ant && getCurrentScheme.value[roomOrRoof + 'List'][ant.antPositionNo - 1]['shelfList'][value - 1]
                relatedResource = shelfData?.resourceCode
              } else {
                const platformData = ant && getCurrentScheme.value[roomOrRoof + 'List'][ant.antPositionNo - 1]['platformList'][value - 1]
                relatedResource = platformData?.resourceCode

                // 只有平台才处理resourceCodeSpace和resourceCodeName
                const resourceCodeSpace = platformData?.resourceCodeSpace
                const resourceCodeName = platformData?.resourceNameSpace

                // 设置空间编码下拉
                if (resourceCodeSpace) {
                  const spaceCodeField = fields.value['antenna']?.find(i => i.name === 'relatedSpaceCode')
                  if (spaceCodeField) {
                    const spaceCodeOptions = resourceCodeSpace.split(',').map((code, index) => ({
                      name: code.trim(),
                      value: code.trim()
                    }))
                    spaceCodeField && Vue.set(spaceCodeField, 'children', spaceCodeOptions)
                    domRefs.antenna?.setFormValue({
                      "relatedSpaceCode": spaceCodeOptions[0].value
                    })
                  }
                }

                // 设置空间名称下拉
                if (resourceCodeName) {
                  const spaceNameField = fields.value['antenna']?.find(i => i.name === 'relatedSpaceName')
                  if (spaceNameField) {
                    const spaceNameOptions = resourceCodeName.split(',').map((name, index) => ({
                      name: name.trim(),
                      value: name.trim()
                    }))
                    spaceNameField && Vue.set(spaceNameField, 'children', spaceNameOptions)
                    domRefs.antenna?.setFormValue({
                      "relatedSpaceName": spaceNameOptions[0].value
                    })
                  }
                }
              }

              domRefs.antenna?.setFormValue({
                "relatedResource": relatedResource
              })

              //设置安装平台层数下拉
              const positionField = fields.value['antenna']?.find(
                (i) => i.name === 'antPlatformNo'
              )
              if (roomOrRoof === 'room') {
                //机架则为1
                createSerializeChildren(positionField, 1)
              } else {
                //平台则设置为所选平台的层数
                createSerializeChildren(positionField, getCurrentScheme.value[roomOrRoof + 'List'][ant.antPositionNo - 1]['platformList'][value - 1]?.pfNum || 1)
              }
              break
            }
          }
        }
          break
        case 'protocolStackSplit': {
          !autoTrigger && dynamicInitEnodebForm(value)
        }
          break
        // 设备来源
        case 'utilizeMode':
        case 'rruReuse':
        case 'antReuse': {
          const childFieldName = {
            baseBand: 'name',
            rru: 'rruName',
            antenna: 'antName',
            antenna_cell: 'antName'
          }

          if (!autoTrigger) {
            if (isCurrentProvince('HN,XJ,YN')) {
              if (value === newEquipmentCode) {
                if (mark === 'baseBand') {
                  domRefs.baseBand?.setFormValue({
                    [childFieldName[mark]]: autoCreateBaseBandName(
                      getCurrentFormValue('enodeb'),
                      getCurrentFormValue('baseBand')
                    )
                  })
                  return
                }

                if (mark === 'rru') {
                  setRruNameFormValue(getCurrentFormValue('cell'))
                  return
                }

                if (mark === 'antenna' ||mark === 'antenna_cell') {
                  (domRefs.antenna || domRefs.antenna_cell)?.setFormValue({
                    antName: autoCreateAntName(
                      getCurrentFormValue('cell'),
                      state.rruIndex,
                      state.antennaIndex
                    )
                  })
                  return
                }
              }
            }
            domRefs[mark].setFormValue({ [childFieldName[mark]]: '' })
          }
        }
          break
        case 'rruType': {
          if (!autoTrigger && isCurrentProvince('HN,YN,XJ')) {
            getCurrentFormValue('rru')?.rruReuse === newEquipmentCode &&
            setRruNameFormValue(getCurrentFormValue('cell'), changeObj)
          }
        }
          break
        case 'constructionPeriod': {
          mark === 'enodeb' &&
          !autoTrigger &&
          (state.curConstructionPeriod = value)
        }
          break
        case 'constructionStage': {
          mark === 'enodeb' &&
          !autoTrigger &&
          (state.curConstructionStage = value)
        }
          break
        case 'ifCascadeRru' : {
          console.log('ifCascadeRru vaule',value)
          //级联RRU
          const pfPositionField = fields.value['rru']?.find(
            (i) => i.name === 'cascadeRruName'
          )
          //同基站下的所有小区进行筛选
          const children = [];
          if(getCellList.value) {
            for(let i = 0; i < getCellList.value.length; i++) {
              let cell = getCellList.value[i]
              cell.rruList.forEach((rru) => {
                if(rru.rruName && rru.rruName !== getCurrentRru.value.rruName) {
                  children.push({
                    name: rru.rruType ? '小区' + (i + 1) + '-' +rru.rruType + '-' + rru.rruName : '小区' + (i + 1) + '-' + '射频单元' +rru.rruName,
                    value: rru.rruName
                  })
                }
              })
            }
          }
          pfPositionField && Vue.set(pfPositionField, 'formType', 'select')
          pfPositionField && Vue.set(pfPositionField, 'children', children)

        }
      }
    }

    // 湖南自动生成字段内容
    const setRruNameFormValue = (data, changeObj) => {
      const createRruName = autoCreateRruName(
        data,
        getCurrentFormValue('rru'),
        changeObj
      )
      domRefs.rru.setFormValue({ rruName: createRruName(state.rruIndex) })
    }
    const boardExtendsRru = (data) => {
      const { boardList } = data
      boardList.forEach((board) => {
        fieldsMapSyncData(rruSyncBoardHN, data, board, false)
      })
    }

    const cellExtendsEnodeb = (data, autoTrigger) => {
      const forceSync = inResourcePool.value || !autoTrigger
      const { cellBand, networkMode, enodebNamePlan, networkStandard } = data
      getCellList.value?.forEach((cell, index) => {
        data.hasOwnProperty('networkMode') &&
        (!cell.networkMode || (cell.networkMode && forceSync)) &&
        $set(cell, 'networkMode', networkMode)

        data.hasOwnProperty('cellBand') &&
        (!cell.freBand || (cell.freBand && forceSync)) &&
        $set(cell, 'freBand', cellBand)

        data.hasOwnProperty('networkStandard') &&
        (!cell.networkStandard || (cell.networkStandard && forceSync)) &&
        $set(cell, 'networkStandard', networkStandard)


        if (data.hasOwnProperty('enodebNamePlan') && (!cell.cellName || (cell.cellName && forceSync)) && !isCurrentProvince('XJ')) {
          $set(
            cell,
            'cellName',
            enodebNamePlan + '-' + useCreateDoubleSerialNumber(index + 1)
          )
        }
      })
    }

    const serializeCellCount = (count) => {
      const { enodebNamePlan } = getCurrentEnodeb.value
      const cellListSize = count ? count.match(/1/gi)?.length : 1
      // console.log('serializeCellCount',cellListSize,getCellList.value.length)
      if (getCellList.value.length < cellListSize) {
        const cellData = deepClone(getCellList.value[0], true, false, true)
        for (
          let i = 0, length = getCellList.value.length;
          i < cellListSize - length;
          i++
        ) {
          getCellList.value.push(deepClone(cellData))
        }
      } else {
        // 给一个比总数大的数字  就会把后面的数据全部删除
        getCellList.value.splice(
          cellListSize,
          getCellList.value.length + cellListSize
        )
        state.cellIndex = '0'
      }

      // 重置小区数据
      getCellList.value.forEach((cell, index) => {
        cell.sort = index
        cell.cellIdPlan = String(index + 1)
        !cell.cellName &&
        enodebNamePlan &&
        (cell.cellName =
          enodebNamePlan + '-' + useCreateDoubleSerialNumber(index + 1))
      })
    }
    const setModelDataBackFill = (field, value, mark) => {
      const modelData = field.children?.find((item) => item.value === value)
      if (modelData) {
        const recodeModelData = deepClone(modelData)
        delete recodeModelData.name
        delete recodeModelData.value
        domRefs[mark].setFormValue(recodeModelData)

        // 型号信息包含图片 图片组件不在表单内 所以要单独设置图片地址
        const currentDataList = getCurrentFormValue(mark, false)
        const currentIndex = state[mark + 'Index']

        if (currentDataList !== undefined && currentIndex !== undefined) {
          Object.keys(recodeModelData).forEach((key) => {
            if (key.includes('Pic')) {
              $set(currentDataList[currentIndex], key, recodeModelData[key])
            }
          })
        }
      }
    }
    const getCurrentFieldsGroupKey = (mark) => {
      const specialFieldsGroup = ['baseBand']
      return specialFieldsGroup.includes(mark) ? 'equipment_' + mark : mark
    }

    const getCurrentFormValue = (mark, child = true) => {
      const markTmp = mark.replace("_rru", '').replace("_cell", '');//小区下直属天线与rru下直属天线，两个不会同时出现，故用一个
      const getterMap = {
        enodeb: getEnodebList.value,
        baseBand: getBaseBandList.value,
        cell: getCellList.value,
        rru: getRruList.value,
        antenna: getAntennaList.value,//有小区下直属天线，与rru下直属天线，不可能同时出现，故只用一个
        extendDeviceBbu: getBaseBandExtendDeviceList.value,
        extendDeviceRru: getRRUExtendDeviceList.value,
      }
      const index = state[markTmp + 'Index']
      return child ? getterMap[markTmp] && getterMap[markTmp][index] : getterMap[markTmp]
    }

    const setCascadingPosition = (fieldsKey, name, value) => {
      const positionNoField = fields.value[fieldsKey]?.find(
        (i) => i.name === name
      )
      const count = getCurrentScheme.value[value + 'List']?.length || 0
      createSerializeChildren(positionNoField, count)
    }
    const setCascadingPositionNo = (data, value, key, mark) => {
      const position = data[key]
      const siteData = getCurrentScheme.value[position + 'List'][value - 1]
      if (siteData) {
        const syncFieldMap = {
          baseBand: baseBandSyncField,
          rru: rruSyncField,
          antenna: antSyncField,
          antenna_cell: [],//保留值
        }
        const currentSyncMap = syncFieldMap[mark]
        const fieldsMap = {}
        Object.keys(currentSyncMap).forEach((key) => {
          const syncKey = position + currentSyncMap[key].slice(4)
          fieldsMap[key] = siteData[syncKey]
        })
        domRefs[mark]?.setFormValue(fieldsMap)
      }
    }


    // 设置安装平台数
    const updateRoofPlatformNo = () => {
      const params = ['rru']
      params.forEach((item) => {
        const data = getCurrentFormValue(item)
        data && setPlatformNo(item, data, data[item.slice(0, 3) + 'PositionNo'])
      })

    }
    const updatePlatformPfNum = () => {
      planSite.value?.siteList && planSite.value.siteList[schemeIndex.value].roofList.forEach((roof) => {
        roof.platformList.forEach((platform) => {
          platform.pfNum = roof.roofSupportPlatformNumber
        })
      })

    }

    const setPlatformNo = (code, data, number) => {
      const paramCode = code.slice(0, 3)
      const position = data[paramCode + 'Position']
      const currentFields = fields.value[code]
      const count = position === 'roof' ? getCurrentScheme?.value?.roofList[+number - 1]?.roofSupportPlatformNumber : 1

      const platformField = currentFields?.find((i) => i.name === paramCode + 'PlatformNo')
      createSerializeChildren(platformField, count)
    }

    const getFileStructurePath = (code) => getFileBaseStructurePath.value + useEnodebDataPath(code)
    const getResourceCodePath = (a, b, c, d, e) => getResourceCodeBasePath.value + useEnodebDataPath(a, b, c, d, e)

    const useEnodebDataPath = (
      code,
      transferMark = false,
      splitCode = '/',
      linkCode = '_',
      endCodeData
    ) => {
      let currentCode = ''
      code && code.split('_').forEach((key, index, arr) => {
        currentCode += useTransferMark(key, transferMark)

        if (key !== 'baseBand') {
          let sortCode = +state[key + 'Index'] + 1
          if (index === arr.length - 1 && endCodeData) {
            sortCode = +endCodeData.sort + 1
          }
          currentCode += linkCode + sortCode
        }

        if (index < arr.length - 1) {
          currentCode += splitCode
        }
      })
      return currentCode
    }
    const useTransferMark = (mark, transferMark) => {
      if (mark === 'enodeb') {
        return transferMark ? mark.charAt(0).toUpperCase() : getCurrentEnodeb.value?.networkStandard
      } else if (mark === 'baseBand') {
        return (
          getBaseBandList.value && getBaseBandList.value.length > 0 && getBaseBandList.value[state.baseBandIndex].subType
        )
      } else {
        return transferMark ? mark.charAt(0).toUpperCase() : mark
      }
    }
    const useChildrenResourceCode = (tab, baseCode) => {
      Object.keys(tab).forEach((key) => {
        if (Array.isArray(tab[key])) {
          useCreateResourceCode(key, tab[key], baseCode)
        }
      })
    }
    const useCreateResourceCode = (key, list, baseCode) => {
      list?.forEach((item) => {
        const code = key.slice(0, -4)
        const resourceCode = useEnodebDataPath(code, true, '', '', item)

        item.resourceCode = baseCode + resourceCode
        Object.keys(item).forEach((code) => {
          if (Array.isArray(item[code])) {
            useCreateResourceCode(code, item[code], item.resourceCode)
          }
        })
      })
    }
    const useNamePropData = (code, name) => {
      const currentFields = fields.value[code]
      if (currentFields) {
        const field = currentFields.find((i) => i.name === name)
        return field?.children
      }
    }

    const dynamicInitEnodebForm = (protocolStackSplit) => {
      if (fields.value.equipment_baseBand && protocolStackSplit) {
        let defaultBaseBandList = []
        const currentEnodeb = getCurrentFormValue('enodeb')
        const enodebUnique = currentEnodeb.resourceCode

        // 基站唯一标识:resourceCode
        // recordBaseBandData: {[resourceCode]: {S: [], C: []}}
        if (!recordBaseBandData[enodebUnique]) {
          recordBaseBandData[enodebUnique] = {}
        }

        originSplit &&
        (recordBaseBandData[enodebUnique][originSplit] = deepClone(
          getBaseBandList.value
        ))

        if (recordBaseBandData[enodebUnique][protocolStackSplit]) {
          defaultBaseBandList =
            recordBaseBandData[enodebUnique][protocolStackSplit]
        } else {
          // 设置基带设备默认值
          let baseBandTypeList = []
          if (protocolStackSplit === 'S') {
            // 分离
            baseBandTypeList = ['DU', 'CU']
          } else {
            baseBandTypeList = ['BBU']
          }

          defaultBaseBandList = baseBandTypeList.map((item, index) => {
            const defaultDataList = baseBandList()
            const data = defaultDataList[0]
            data.sort = index
            data.subType = item
            data.equipId = index + 1
            data.resourceCode =
              getResourceCodePath('enodeb', true, '', '') + item

            if (isCurrentProvince('XJ')) {
              data.boardList = [
                {
                  ...deepClone(data.boardList[0]),
                  boardType: 'controlBoard'
                },
                {
                  ...deepClone(data.boardList[0]),
                  closable: false,
                  boardType: 'baseBandCard'
                }
              ]
            }

            //基带设备CU的时候默认板卡为空
            if (item === 'CU') {
              data.boardList = []
            }

            return data
          })
        }

        domRefs.baseBand?.resetFormValue()
        nextTick(() => {
          getBaseBandList.value = defaultBaseBandList
        })

        originSplit = protocolStackSplit
        state.baseBandIndex = '0'
        domRefs.baseBand.tabIndex = '0'
        state.equipmentActive = getBaseBandList.value[0].subType
      }
    }

    const resetChildTab = (index, mark) => {
      const formStructure = ['enodeb', 'cell', 'rru', 'antenna']
      const currentStructureIndex = formStructure.findIndex((i) => i === mark)
      const childStructure = ~currentStructureIndex
        ? formStructure.slice(currentStructureIndex + 1)
        : []

      if (childStructure?.length) {
        childStructure.forEach((code) => {
          if (domRefs[code]) {
            domRefs[code].tabIndex = '0'
          }
        })
      }

      if (mark === 'enodeb') {
        period.value && setCurPeroidTableData()
      }
    }
    const rruIntegrationAnt = (data, res, name, mark) => {
      // rru集成天线
      const { rruAntenna, antennaList } = data
      if (rruAntenna?.toUpperCase() === 'Y') {
        antennaList.forEach((ant) => {
          fieldsMapSyncData(rruSyncAnt, data, ant, false)
        })
      }
      mark && setImageIdentify(data, res, name, mark)
    }
    const handleSelectionChange = (val) => {
      const currentEnodeb = getCurrentEnodeb.value
      if (val.length == 0) {
        if (!state.enodebPeroidSwith) {
          currentEnodeb.constructionPeriod = null
          currentEnodeb.constructionStage = null
          currentEnodeb.periodStageProject = null
        }
      } else {
        currentEnodeb.constructionPeriod = val[0].construction_period
        currentEnodeb.constructionStage = val[0].construction_stage
        currentEnodeb.periodStageProject = val[0].id
        currentEnodeb['hangupEnodebIdPlan'] = val[0].hangup_enodeb_id_plan
        $set(currentEnodeb, 'hangupOrder', val[0].xq_site_id_cmcc)
        $set(currentEnodeb, 'samePeriod', 'N')
      }
      setPeroidExtendData()
      emit('setPeriodStage', state.enodebPeriodStage)
    }
    const setPeroidExtendData = () => {
      state.enodebPeriodStage = []
      getEnodebList.value?.forEach((item, index) => {
        state.enodebPeriodStage.push({
          enodebIdPlan: item.enodebIdPlan,
          constructionPeriod: item.constructionPeriod,
          constructionStage: item.constructionStage,
          periodStageProject: item.periodStageProject,
          samePeriod: item.samePeriod,
          hangupOrder: item.hangupOrder,
          hangupPeroid: item.hangupPeroid,
          hangupEnodebIdPlan: item.hangupEnodebIdPlan
        })
      })
    }
    const setPeroidData = (changeObj) => {
      const currentEnodeb = getCurrentEnodeb.value
      if (!currentEnodeb) return

      currentEnodeb.hangupEnodebIdPlan = null
      !changeObj.samePeriod && (changeObj.samePeriod = 'N')
      if (changeObj.samePeriod == 'N') {
        if (changeObj.hangupPeroid == 'Y') {
          currentEnodeb.constructionPeriod = null
          currentEnodeb.constructionStage = null
          currentEnodeb.periodStageProject = null
          domRefs.constructionPeriodTable.clearSelection()
        }
      } else if (changeObj.samePeriod == 'Y' || changeObj.hangupPeroid == 'N') {
        currentEnodeb.constructionPeriod = null
        currentEnodeb.constructionStage = null
        currentEnodeb.periodStageProject = null
        currentEnodeb.hangupPeroid = null
        domRefs.constructionPeriodTable.clearSelection()
      }

      setPeroidExtendData()
      setCurPeroidTableData()
      emit('setPeriodStage', state.enodebPeriodStage)
    }
    const setCurPeroidTableData = () => {
      const currentEnodeb = getCurrentEnodeb.value
      if (!currentEnodeb) {
        state.curEnodebPeriodData =
          state.constructionPeriodData[
            Object.keys(state.constructionPeriodData)[0]
            ]
        return
      }

      if (
        (!currentEnodeb['samePeriod'] || currentEnodeb['samePeriod'] == 'N') &&
        (!currentEnodeb['hangupPeroid'] || currentEnodeb['hangupPeroid'] == 'N')
      ) {
        state.curEnodebPeriodData =
          state.constructionPeriodData &&
          state.constructionPeriodData[currentEnodeb.id]
        state.curEnodebPeriodTitle = deepClone(state.constructionPeriodTitle)
      } else if (
        currentEnodeb['samePeriod'] == 'N' &&
        currentEnodeb['hangupPeroid'] == 'Y'
      ) {
        state.curEnodebPeriodData =
          state.hangupPeriodData && state.hangupPeriodData[currentEnodeb.id]
        state.curEnodebPeriodTitle = deepClone(state.hangupPeriodTitle)
      } else {
        state.curEnodebPeriodData =
          state.constructionPeriodData &&
          state.constructionPeriodData[currentEnodeb.id]
      }
    }
    const setElectronicFormData = (data) => {
      if (getCurrentEnodebElectronic.value) {
        Object.assign(getCurrentEnodebElectronic.value, data)
      } else {
        const defaultData = {
          orderNo: getCurrentScheme.value?.xqSiteIdCmcc,
          enodebId: allEnodebList.value[state.enodebIndex]?.enodebId,
          enodebUuid: allEnodebList.value[state.enodebIndex]?.id,
          siteUuid: allEnodebList.value[state.enodebIndex]?.siteUuid
        }
        state.electronicList.push(Object.assign(defaultData, data))
      }
    }
    const submitLight = () => {
      const linkCode = sysconfig.JX_LIGHT_IFRAME_URL.includes('?') ? '&' : '?'
      window.open(
        `${sysconfig.JX_LIGHT_IFRAME_URL + linkCode}sourceordercode=${
          siteCmccId.value
        }&stationno=${
          allEnodebList.value[state.enodebIndex]?.enodebId
        }&naastimestamp=${dayjs(+new Date()).format('YYYYMMDD HH:mm:ss')}`
      )
    }
    const downloadErrorFile = () =>
      useBlobToExcelDownload(state.errorFileBlob, 'error.xlsx')

    // 导入rru 天线
    const downloadRruAndAnt = (cellData) => {
      // console.log(cellData)
      const paramMap = {
        status:"WCL",
        nodeId:nodeId,
        sortBy:"",
        userId:userId,
        site_id_cmcc:planSite.value.xqSite.siteIdCmcc,
        stage_code:planSite.value.stageCode,
        cell_uuid:cellData.id
      }
      let formData =  {
        code:"PLAN_CELL_IMPORT",
        type:"config",
        fileType:"excel",
        paramMap:paramMap
      };
      // formData.append('code', "PLAN_CELL_IMPORT");
      // formData.append('type', "config");
      // formData.append('fileType', "excel");
      // formData.append('paramMap', paramMap);
      // console.log("formData",JSON.stringify(formData))
      downCellAntRuu(formData).then(res =>{
        if(res.code =='200'){
          let path = res.msg
          attachmentApi.downloadUrl(
            path, 'rru_antenna导入模板.zip'
          )
        }
      })
    }
    const uploadRruAndAnt = (data) =>
      data ? (state.errorFileBlob = data) : reRenderPage()

    // 图像识别回填
    const setImageIdentify = (data, res, name, mark) => {
      const FieldKey = mark === 'baseBand' ? 'equipment_baseBand' : mark
      const currentFields = fields.value[FieldKey]
      const ocrField = currentFields.find((field) => field.name === name)

      const fillBack = useOcrDataFillBack(ocrField, res?.picIdentification)
      fillBack && domRefs[mark].setFormValue(fillBack)
    }
    // request
    //建设工期
    const requestPeoridList = async () => {
      const data = await commonQueryFieldData('qryConstructionPeriodList', {
        siteIdCmcc: siteCmccId.value
      })
      if (data.data.dataList) {
        state.constructionPeriodData = arrayGroupBy(
          data.data.dataList,
          (item) => item.enodebuuid
        )
      }
      state.constructionPeriodTitle = data.data.title
      state.curEnodebPeriodTitle = deepClone(state.constructionPeriodTitle)
      await requestHangupPeoridList()
      setCurPeroidTableData()
    }
    //挂起基站
    const requestHangupPeoridList = async () => {
      const data = await commonQueryFieldData('qryHangupPeroidList', {
        siteIdCmcc: siteCmccId.value
      })
      if (data.data.dataList) {
        state.hangupPeriodData = arrayGroupBy(
          data.data.dataList,
          (item) => item.enodebuuid
        )
      }
      state.hangupPeriodTitle = data.data.title
    }
    // 线网基站
    const requestOpenSite = async (id) => {
      const data = await qryOpenSite(id)
      const enodebList = data?.data?.enodebList
      if (enodebList) {
        // 设置线网站点不可关闭 不可编辑
        enodebList.forEach((enodeb) => {
          enodeb.closable = false
          enodeb.openSite = true
          useCompletionListData(enodeb)
        })
        state.openSiteEnodeb = enodebList
      }
    }
    // 江西电调
    const requestElectronicList = async () => {
      if (getCurrentScheme.value?.xqSiteIdCmcc) {
        const data = await qryJxElectronic({
          orderNo: getCurrentScheme.value.xqSiteIdCmcc
        })
        state.electronicList = data.data
      }
    }

    const addDefaultTab = (list)=>{
      setDefaultListData(getCurrentScheme.value, list)
      const ref = list.slice(0, -4)
    }

    //资源回填
    const resourceCodeForm = reactive({})
    const resourceCodeList = reactive({})
    const resourceCodeSelectDialogShow = ref(false)
    const currentBackFillType = ref('')
    const resourceCodeTabActive = ref('first')
    const handleTabClick = () => {

    }
    const copyProperties = (src, dest, props) => {
      props.forEach(prop => {
        if (src.hasOwnProperty(prop)) {
          dest[prop] = src[prop]
        }
      })
    }
    // 定义一个辅助函数来处理单个射频单元的逻辑
    const processRru = (rru, index, siteIndex, enodebIndex, cellIndex) => {
      return {
        value: rru.resourceCode,
        label: `基站${enodebIndex + 1}-小区${cellIndex + 1}-射频${index + 1}-【${rru.resourceCode}】`,
        data: rru
      }
    }
    // 定义一个辅助函数来处理单个射频单元的逻辑
    const processAnt = (ant, index, siteIndex, enodebIndex, cellIndex, rruIndex) => {
      return {
        value: ant.resourceCode,
        label: `基站${enodebIndex + 1}-小区${cellIndex + 1}-射频${rruIndex + 1}-天线${index + 1}-【${ant.resourceCode}】`,
        data: ant
      }
    }
    const processBbu = (band, index, siteIndex, enodebIndex) => {
      return {
        value: band.resourceCode,
        label: `基站${enodebIndex + 1}-基带设备${index + 1}-【${band.resourceCode}】`,
        data: band
      }
    }

// 定义一个函数来处理 siteList 并收集所有符合条件的 Rru
    const collectRruResources = (siteList) => {
      const list = []
      siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.cellList.forEach((cell, cellIndex) => {
            cell.rruList.forEach((rru, rruIndex) => {
              const resource = processRru(rru, rruIndex, siteIndex, enodebIndex, cellIndex)
              if (resource) {
                list.push(resource)
              }
            })
          })
        })
      })
      return list
    }
    const collectAntResources = (siteList) => {
      const list = []
      siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.cellList.forEach((cell, cellIndex) => {
            cell.rruList.forEach((rru, rruIndex) => {
              rru.antennaList.forEach((ant, antIndex) => {
                const resource = processAnt(ant, antIndex, siteIndex, enodebIndex, cellIndex, rruIndex)
                if (resource) {
                  list.push(resource)
                }
              })
            })
          })
        })
      })
      return list
    }
    const collectBbuExtendDeviceResources = (siteList) => {
      const list = []
      siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.baseBandList.forEach((baseBand, baseBandIndex) => {
            baseBand.bbuExtendDeviceList.forEach((extendDevice, extendDeviceIndex) => {
              list.push( {
                value: extendDevice.resourceCode,
                label: `基站${enodebIndex + 1}-基带设备${baseBandIndex + 1}-扩展设备${extendDeviceIndex + 1}-【${extendDevice.resourceCode}】`,
                data: extendDevice
              })
            })

          })
        })
      })
      return list
    }
    const collectBoardResources = (siteList) => {
      const  list = []
      siteList.forEach((site,siteIndex) => {
        site.enodebList.forEach((enodeb,enodebIndex) => {
          enodeb.baseBandList.forEach((baseBand, baseBandIndex) => {
            baseBand.boardList.forEach((board,boardIndex) => {
              list.push({
                value: board.id,
                label: `基站${enodebIndex + 1}--板卡${boardIndex + 1}`,
                data: board
              })
            })
          })
        })
      })
      return list
    }
    const collectRruExtendDeviceResources = (siteList) => {
      const list = []
      siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.cellList.forEach((cell, cellIndex) => {
            cell.rruList.forEach((rru, rruIndex) => {
              rru.rruExtendDeviceList.forEach((extendDevice, extendDeviceIndex) => {
                list.push( {
                  value: extendDevice.resourceCode,
                  label: `基站${enodebIndex + 1}-小区${cellIndex + 1}-射频${rruIndex + 1}-扩展设备${extendDeviceIndex + 1}-【${extendDevice.resourceCode}】`,
                  data: extendDevice
                })
              })
            })
          })
        })
      })
      return list
    }
    const collectEquipResources = (siteList) => {
      const list = []
      siteList.forEach((site, siteIndex) => {
        site.enodebList.forEach((enodeb, enodebIndex) => {
          enodeb.baseBandList.forEach((baseBand, baseBandIndex) => {
            const resource = processBbu(baseBand, baseBandIndex, siteIndex, enodebIndex)
            if (resource) {
              list.push(resource)
            }
          })
        })
      })
      return list
    }
    const backFill =  (type) => {
      resourceCodeSelectDialogShow.value = false
      console.log('resourceCodeForm.resourceCodeShare', resourceCodeForm.resourceCodeShare)
      console.log('type',type)
      commonQuery('resourceCodeBackFill').then(res => {
        // console.log('res',res)
        const dataJson = JSON.parse(res.data[0].value)
        if (type === 'rru') {
          // console.log('planSite', planSite.value)
          const tempRru = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.rruIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data, tempRru, dataJson['t_5g_rru'])
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.rruIndex] = tempRru
        } else if (type === 'ant') {
          const tempAnt = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.rruIndex].antennaList[state.antennaIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data, tempAnt, dataJson['t_5g_antenna'])
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.rruIndex].antennaList[state.antennaIndex] = tempAnt
        } else if (type === 'equip') {
          const tempEquip = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data, tempEquip, dataJson['t_5g_equipment'])
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex] = tempEquip
        } else if (type === 'bbuExtendDevice') {
          // console.log('datajons' ,dataJson['t_5g_equipment'])
          const tempBbuExtendDevice = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex].bbuExtendDeviceList[domRefs.extendDeviceBbu.extendDeviceIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data,tempBbuExtendDevice,Object.keys(tempBbuExtendDevice).filter(key => key !== 'euId' && key !== 'id' && key !== 'euUtilizeMode'))
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex].bbuExtendDeviceList[domRefs.extendDeviceBbu.extendDeviceIndex] = tempBbuExtendDevice
        } else if (type === 'rruExtendDevice') {
          const tempRruExtendDevice = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.baseBandIndex].rruExtendDeviceList[domRefs.extendDeviceRru.extendDeviceIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data,tempRruExtendDevice,Object.keys(tempRruExtendDevice).filter(key => key !== 'euId' && key !== 'id' && key !== 'euUtilizeMode'))
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].cellList[state.cellIndex].rruList[state.baseBandIndex].rruExtendDeviceList[domRefs.extendDeviceRru.extendDeviceIndex] = tempRruExtendDevice
        } else if (type === 'board') {
          const tempBoard = planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex].boardList[domRefs.board.boardIndex]
          copyProperties(resourceCodeList.list.filter(item => item.value === resourceCodeForm.resourceCodeShare)[0].data,tempBoard,Object.keys(tempBoard).filter(key => key !== 'boardId' && key !== 'id' && key !== 'boardUtilizeMode'))
          tempBoard?.portList?.forEach(item => {
            item.id =null
          })
          planSite.value.siteList[schemeIndex.value].enodebList[state.enodebIndex].baseBandList[state.baseBandIndex].boardList[domRefs.board.boardIndex] = tempBoard
        }
      })
    }
// 资源池选择
    const dealSelectChangeEvent = (data, type) => {
      currentBackFillType.value = type
      if (type === 'rru') {
        if (data.field.tableName === 't_5g_rru' && data.field.name === 'rruReuse' && data.value === 'Share_Sitetype') {  //增删调-> 方案设计
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectRruResources(planSite.value.siteList)
        }
      } else if (type === 'ant') {
        if (data.field.tableName === 't_5g_antenna' && data.field.name === 'antReuse' && data.value === 'Share_Sitetype') {  //增删调-> 方案设计
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectAntResources(planSite.value.siteList)
        }
      } else if (type === 'equip') {
        if (data.field.tableName === 't_5g_equipment' && data.field.name === 'utilizeMode' && data.value === 'Share_Sitetype') {  //增删调-> 方案设计
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectEquipResources(planSite.value.siteList)
        }
      } else if (type === 'bbuExtendDevice') {
        if (data.field.tableSubType === "baseband" && data.field.name === 'euUtilizeMode'  && data.value === 'Share_Sitetype') {
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectBbuExtendDeviceResources(planSite.value.siteList)
        }
      } else if (type === 'rruExtendDevice') {
        if (data.field.tableSubType === "rru" && data.field.name === 'euUtilizeMode'  && data.value === 'Share_Sitetype') {
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectRruExtendDeviceResources(planSite.value.siteList)
        }
      } else if (type === 'board') {
        if (data.field.name === 'boardUtilizeMode' && data.value === 'Share_Boardcard') {
          resourceCodeSelectDialogShow.value = true
          resourceCodeList.list = collectBoardResources(planSite.value.siteList)
        }
      }
    }

    provide('enodebList',getEnodebList)
    provide('rruList',getRruList)
    provide('rru',getCurrentRru)
    provide('enodeb',getCurrentEnodeb)
    provide('antList',getAntennaList)
    provide('ant',getCurrentAnt)
    provide('cell',getCurrentCell)
    provide('cellList',getCellList)
    provide('baseBandList',getBaseBandList)

    return {
      ...toRefs(domRefs),
      ...toRefs(state),
      sysconfig,
      siteCmccId,
      childStage,
      isSFSite,
      isCurrentStage,
      showInDemandStage,
      getRruExtendDevice,
      siteTabEditable,
      enodebTabEditable,
      isOpenSite,
      formDisable,
      getEnodebList,
      allEnodebList,
      getCurrentEnodeb,
      // getEquipmentTitle,
      getBaseBandList,
      getBaseBandExtendDeviceList,
      getCellList,
      getRruList,
      getRRUExtendDeviceList,
      getRruInheritList,
      getAntennaInheritList,
      handleRruInherit,
      handleAntennaInherit,
      handleInheritedRruData,
      handleInheritedAntennaData,
      getCurrentRru,
      getAntennaList,
      getResourceCodePath,
      getFileStructurePath,
      getCurrentEnodebElectronic,
      enodebSamePeriod,
      getPeriodTableData,
      setPeroidData,
      setImageIdentify,
      handleAddTab,
      handleFormValue,
      onFormUpdate,
      handleFormEvent,
      handleRemoveTab,
      handleSwitchTab,
      rruIntegrationAnt,
      handleSelectionChange,
      requestOpenSite,
      requestPeoridList,
      requestElectronicList,
      setElectronicFormData,
      submitLight,
      downloadErrorFile,
      downloadRruAndAnt,
      uploadRruAndAnt,
      updateRoofPlatformNo,
      updatePlatformPfNum,
      useEnodebDataPath,
      useChildrenResourceCode,
      useNamePropData,
      backFill,
      dealSelectChangeEvent,
      resourceCodeSelectDialogShow,
      currentBackFillType,
      resourceCodeForm,
      resourceCodeList,
      handleTabClick,
      resourceCodeTabActive,
      addDefaultTab,
      isShowBadgeStage,
      isShowChangeFormToolTipStage,
    }
  },
  components: {
    InheritDialog,
    Anchor: () => import('@/components/Open/Components/Anchor'),
    Board: () => import('@/components/Open/Components/Board'),
    Port: () => import('@/components/Open/Components/Port'),
    // Transfer: () => import('@/components/Open/Components/Transfer'),
    FormArea: () => import('@/components/Open/Components/FormArea'),
    BasicForm: () => import('@/components/BasicComponents/Form'),
    BasicTable: () => import('@/components/BasicComponents/Table'),
    Ranplan: () => import('@/components/Plan/Ranplan'),
    // Shelf: () => import('@/components/Plan/Shelf'),
    ExtendDevice: () => import('@/components/Plan/ExtendDevice'),
    FileUpload: () => import('@/components/FileUpload'),
    Transfer: () => import('@/components/Plan/Transfer')

  }
})
</script>

<style lang='scss' scoped>
@import '@/assets/styles/plan/common.scss';

::v-deep {
  .el-collapse {
    border-top-width: 0;
  }

  .tabs-wrapper {
    border: 1px solid #dfe4ed;

    .tabs-content {
      padding: 0 15px 15px 15px;
    }
  }

  .site-area {
    form .el-form-item:last-child {
      width: 100%;
    }
  }

  .electronicForm {
    .el-form-item__content {
      width: calc(100% - 180px) !important;
    }
  }
}

.file-required {
  margin-right: 6px;
  color: #ff4949;
}

.rru-ant-upload {
  margin: 0 12px;
}
</style>
