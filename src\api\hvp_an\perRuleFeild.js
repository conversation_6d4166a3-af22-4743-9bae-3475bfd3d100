import request from '@/utils/request'

// 查询规则字段列表
export function listPerRuleFeild(query) {
  return request({
    url: '/hvpAn/perRuleFeild/list',
    method: 'get',
    params: query
  })
}

// 查询规则字段详细
export function getPerRuleFeild(id) {
  return request({
    url: '/hvpAn/perRuleFeild/' + id,
    method: 'get'
  })
}

// 新增规则字段
export function addPerRuleFeild(data) {
  return request({
    url: '/hvpAn/perRuleFeild',
    method: 'post',
    data: data
  })
}

// 修改规则字段
export function updatePerRuleFeild(data) {
  return request({
    url: '/hvpAn/perRuleFeild',
    method: 'put',
    data: data
  })
}

// 删除规则字段
export function delPerRuleFeild(id) {
  return request({
    url: '/hvpAn/perRuleFeild/' + id,
    method: 'delete'
  })
}