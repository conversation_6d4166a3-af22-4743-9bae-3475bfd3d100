import request from '@/utils/request'

/**
 * 修改三方报文
 * @param {*} data
 * @returns
 */
export function updateThirdCall(data) {
    return request({
      url: '/site/third/call/edit',
      method: 'post',
      data: data
    })
}

/**
 * 报文重新入库
 * @param {} data
 * @returns
 */
export function thirdCallRetry(thirdCallId) {
  return request({
    url: '/site/third/call/retry/' + thirdCallId,
    method: 'post',
  })
}

/**
 * 批量删除工单
 * @param source source 来源：1 需求库删除；2工具箱
 * @param {}} data
 * @returns
 */
export function batchDelOrder(source,data) {
    return request({
      url: '/site/planSite/site/batchDel/'+source,
      method: 'post',
      data: data
    })
}

/**
 * 调用流程通知接口
 * @param {}} data
 * @returns
 */
export function callFlowNotify(data) {
  return request({
    url: '/site/third/call/callFlowNotify',
    method: 'post',
    data: data
  })
}

/**
 * 天线测量重新入库
 * @param {} data
 * @returns
 */
export function attitudeIndicatorAgain(thirdCallId,data) {
    return request({
      url: '/service/attitudeIndicator/nokiaAttitudeIndicator/againUpdate/' + thirdCallId,
      method: 'post',
      data: data
    })
}

/**
 * 工单批量流转
 * @param {} data
 * @returns
 */
export function planBatchFlow(data){
  return request({
    url: '/site/planSite/batch/taskComplete',
    method: 'post',
    data: data
  })
}


export function finishedRollback(procDefKey,data){
  return request({
    url: '/kernel/flow/finished/rollback/' + procDefKey,
    method: 'post',
    data: data
  })
}

export function lnChangeBatchDel(data) {
  return request({
    url: '/site/ln/change/batch/del',
    method: 'post',
    data: data
  })
}

/**
 * 用于开发工具箱（已归档工单回退至最后一个节点），工单回退后如果是新站的流程需要额外删除deliver相关表的数据
 * @param data
 * @returns
 */
export function deliverRollback(data){
  return request({
    url: '/site/deliver/finished/rollback/',
    method: 'post',
    data: data
  })
}
